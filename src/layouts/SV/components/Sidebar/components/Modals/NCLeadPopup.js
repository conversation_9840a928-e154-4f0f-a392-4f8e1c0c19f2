
import { Grid, LinearProgress, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { JsonToNormalDate, ToMinutesSeconds } from "../../../../../../utils/utility";
import dayjs from "dayjs";
import { useSnackbar } from "notistack";
import { AddLeadToPriorityQueueService, AddLeadsToQueueService } from "../../../../../../services/Common";
import { connect } from "react-redux";
import { setRefreshAgentStats } from "../../../../../../store/actions/SalesView/SalesView";
import { SelectDropdown } from "../../../../../../components";
import User from "../../../../../../../src/services/user.service";
import rootScopeService from "../../../../../../../src/services/rootScopeService";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import { GetNotContactedLeadsService } from "../../helper/sidebarHelper";

const NCLeadPopup = (props) => {
    // let rows = props.data !== undefined ? props.data : [];
    let [noOfDays, setNoOfDays] = useState(3);
    let [rows, setRows] = useState([]);
    // let _rows = [{ CreatedOn: "/Date(1606914122860+0530)/", CustID: 20163762, CustName: "Avinash Tayade", LeadId: 343282652, UserId: 30407 },
    // { CreatedOn: "/Date(1606914122860+0530)/", CustID: 20163763, CustName: "Navjot", LeadId: 343282655, UserId: 30407 }
    // ];
    // let [rows, setRows] = useState(_rows);

    const { enqueueSnackbar } = useSnackbar();
    const [sortOrderTT, setSortOrderTT] = useState('asc');
    const [sortOrderTD, setSortOrderTD] = useState('asc');
    const [sortOrderCountry, setsortOrderCountry] = useState('asc');
    const [sortOrderLCOn, setsortOrderLCOn] = useState('asc');
    const [sortOrderStatus, setsortOrderStatus] = useState('asc');
    const [ncleadloader, setNcleadloader] = useState(false);

    const CallBack = {
        1: 'Customer Requested Callback', 2: 'Customer Agreed Callback',
        3: 'Best Guess', 4: 'Payment Callback'
    }

    const GetNotContactedLeads = (LastDays) => {
            setNcleadloader(true);
            GetNotContactedLeadsService(LastDays).then((result) => {
                if (result && Array.isArray(result)) {
                    setRows(result);
                }
            }).finally(() => {
                setNcleadloader(false);
            })
        }

    useEffect(() => {
        GetNotContactedLeads(3);
        setNoOfDays(3);
    }, [props.open]);

    const clearAll = () => {
        setRows(
            rows.map((row) => ({ ...row, IsChecked: false }))
        );
    }
    const close = () => {
        clearAll();
        props.handleClose();
    }
    const handleChange = (e, LeadId) => {
        let checked = e.target.checked;
        let selectedLeads = rows.filter((lead) => lead.IsChecked);
        if (selectedLeads.length >= 5 && checked) {
            alert("You can not add more then 5 leads.");
            return false;
        }
        let temp = rows.map((row) => {
                if (row.LeadId === LeadId) {
                    row.IsChecked = checked;
                }
                return row;
            });
        temp = temp;

        setRows(temp); 
    }

     // Function to handle sorting
    const sortTableByTotalDials = () => {
    const sortedData = [...rows].sort((a, b) => {
        if (sortOrderTD === 'asc') {
            if (a.TotalAttempts != null && b.TotalAttempts != null) {
                return a.TotalAttempts - b.TotalAttempts;
              } else if (a.TotalAttempts != null) {
                // If b.Talktime is null, place the row with a non-null Talktime first
                return 1;
              } else if (b.TotalAttempts != null) {
                // If a.Talktime is null, place the row with a non-null Talktime first
                return -1;
              } else {
                // Both Talktime are null, no change in order
                return 0;
              }
        } else {
            if (a.TotalAttempts != null && b.TotalAttempts != null) {
                return b.TotalAttempts - a.TotalAttempts;
              } else if (b.TotalAttempts != null) {
                // If b.TotalAttempts is null, place the row with a non-null TotalAttempts first
                return 1;
              } else if (a.TotalAttempts != null) {
                // If a.TotalAttempts is null, place the row with a non-null TotalAttempts first
                return -1;
              } else {
                // Both TotalAttempts are null, no change in order
                return 0;
              }
        }
      })
    
    //a.TotalAttempts - b.TotalAttempts);
    setRows(sortedData);
    setSortOrderTD(sortOrderTD === 'asc' ? 'desc' : 'asc');
    };

    const sortTableByCountry = () => {
        let sortedData = []
        if (sortOrderCountry === 'asc')
            sortedData = rows.sort((a, b) => a.Country.localeCompare(b.Country));
        else
            sortedData = rows.sort((a, b) => b.Country.localeCompare(a.Country));

    setRows(sortedData);
    setsortOrderCountry(sortOrderCountry === 'asc' ? 'desc' : 'asc');
    };

    const sortTableByStatus = () => {
        let sortedData = []
        if (sortOrderStatus === 'asc')
            sortedData = rows.sort((a, b) => a.StatusName.localeCompare(b.StatusName));
        else
            sortedData = rows.sort((a, b) => b.StatusName.localeCompare(a.StatusName));
    
    setRows(sortedData);
    setsortOrderStatus(sortOrderStatus === 'asc' ? 'desc' : 'asc');
    };

    const sortTableByLCOn = () => {
        let sortedData = []
        if (sortOrderLCOn === 'asc')
            sortedData = rows.sort((a, b) => a.CreatedOn.localeCompare(b.CreatedOn));
        else
            sortedData = rows.sort((a, b) => b.CreatedOn.localeCompare(a.CreatedOn));

    setRows(sortedData);
    setsortOrderLCOn(sortOrderLCOn === 'asc' ? 'desc' : 'asc');
    };

    const sortTableByTalkTime = () => {
        const sortedData = [...rows].sort((a, b) => {
            // Check if the Talktime field is present and not null/undefined
            if (sortOrderTT === 'asc') {
            if (a.TotalTT != null && b.TotalTT != null) {
              return a.TotalTT - b.TotalTT;
            } else if (a.TotalTT != null) {
              // If b.Talktime is null, place the row with a non-null Talktime first
              return 1;
            } else if (b.TotalTT != null) {
              // If a.Talktime is null, place the row with a non-null Talktime first
              return -1;
            } else {
              // Both Talktime are null, no change in order
              return 0;
            }
        }else{
            if (a.TotalTT != null && b.TotalTT != null) {
                return b.TotalTT - a.TotalTT;
              } else if (b.TotalTT != null) {
                // If b.Talktime is null, place the row with a non-null Talktime first
                return 1;
              } else if (a.TotalTT != null) {
                // If a.Talktime is null, place the row with a non-null Talktime first
                return -1;
              } else {
                // Both Talktime are null, no change in order
                return 0;
              }
        }
        });
        setRows(sortedData);
        setSortOrderTT(sortOrderTT === 'asc' ? 'desc' : 'asc');

    };

    const AddLeadsToQueue = () => {
        let IscheckedLeads = rows.filter((lead) => lead.IsChecked);
        if (IscheckedLeads.length == 0) {
            enqueueSnackbar("Please Select Leads.", { variant: 'error', autoHideDuration: 3000, });
            return;
        }

        // AddLeadsToQueueService(IscheckedLeads, 'Manual added', 33, 1).then((res) => {
        //     if (res && Array.isArray(res.incompatibleTimeZone)) {
        //         res.incompatibleTimeZone.forEach((LeadId) => {
        //             enqueueSnackbar("Sorry lead id " + LeadId + " has incompatible time zone time.", { variant: 'error', autoHideDuration: 3000 });
        //         })
        //     }
        //     if (res && Array.isArray(res.validation)) {
        //         res.validation.forEach((LeadId) => {
        //             enqueueSnackbar(LeadId, { variant: 'error', autoHideDuration: 3000 });
        //         })
        //     }
        //     props.setRefreshAgentStatsToRedux(true);
        // });

        IscheckedLeads.forEach((items) => {

            var priorityLead = {
                "LeadId": items.LeadId,
                "Name": items.CustName,
                "CustomerId": items.CustID,
                "UserID": User.UserId,
                "Priority": 1,
                "ProductId": items.ProductID || rootScopeService.getProductId(),
                "Reason": 'Manual added',
                "ReasonId": items.IsNBTSorted ? 80 : 33,
                "CallStatus": "",
                "IsAddLeadtoQueue": 1,
                "IsNeedToValidate": 1
            }

            AddLeadToPriorityQueueService(priorityLead).then((resultData) => {
                if (resultData != null) {
                    if (resultData && resultData.message && resultData.message == "Success") {
                        enqueueSnackbar("Lead " + resultData.LeadID + " Added successfully", {
                            variant: 'success',
                            autoHideDuration: 3000,
                        });
                    }
                    else {
                        let error = (resultData && resultData.message && resultData.message !== '')
                            ? (resultData.LeadID + " : " + resultData.message)
                            : "Error while adding lead: " + resultData.LeadID;
                        enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                    }
                }
            })
        });
        props.setRefreshAgentStatsToRedux(true);
        close();
    };
    return (
        <ModalPopup open={props.open} title='Untouched Leads' handleClose={close} className="addmoreLeadPopup scrollFx">
            <Grid container>
            <SelectDropdown
                name="days"
                label="Not Called Days"
                value={noOfDays}
                options={['Today', 1, 2, 3, 4, 5, 6, 7]}
                labelKeyInOptions='_all'
                valueKeyInOptions='_all'
                handleChange={(e) => {
                    setNoOfDays(e.target.value);
                    GetNotContactedLeads(Number.isInteger(e.target.value) ? e.target.value : 0);
                }}
                fixOptionListBelow
                sm={6} md={4} xs={12}
                
            />
            </Grid>

            {rows.some(row => row.IsNBTSorted === true) && (
                <div className="tip-container">
                    <div className="tip-accent-bar"></div>
                    <div className="tip-icon">
                        <TipsAndUpdatesIcon />
                    </div>
                    <div>
                        <Typography variant="body1" className="tip-text">
                            <span className="tip-label">Tip:</span> Leads highlighted in yellow have a <em className="tip-emphasis">higher chance</em> of picking up your call right now
                        </Typography>
                    </div>
                </div>
            )}
          
            {ncleadloader ?
                <LinearProgress color="secondary" />
                :
                (rows.length === 0
                    ? <h3 className="nodataFound">No Data Found</h3>
                    : <>
                        <div className="content">
                            <TableContainer component={Paper}>
                                <Table aria-label="simple table" className="reassigned-table">
                                    <TableHead>
                                        <TableRow>
                                            <TableCell align="left">Sno</TableCell>
                                            <TableCell align="left">Lead Id</TableCell>
                                            <TableCell align="left">Customer Name</TableCell>
                                            <TableCell align="left" className="shortingIcon">Status
                                                <span onClick={sortTableByStatus}>{sortOrderStatus === 'asc' ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}</span>
                                            </TableCell>
                                            <TableCell align="left" className="shortingIcon">Total Dials 
                                            <span onClick={sortTableByTotalDials}>{sortOrderTD === 'asc' ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}</span>
                                            </TableCell>
                                            <TableCell align="left" className="shortingIcon">Talktime 
                                            <span onClick={sortTableByTalkTime}>{sortOrderTT === 'asc' ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}</span>
                                            </TableCell>
                                            <TableCell width="13%" align="left"className="shortingIcon">Last ContactedOn
                                                <span onClick={sortTableByLCOn}>{sortOrderLCOn === 'asc' ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}</span>
                                            </TableCell>                                           
                                            <TableCell align="left">CallBack Time</TableCell>
                                            <TableCell align="left">CallBack Type</TableCell>
                                            <TableCell align="left"className="shortingIcon">Country
                                            <span onClick={sortTableByCountry}>{sortOrderCountry === 'asc' ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}</span>
                                            </TableCell>
                                            <TableCell align="left">Call Timings</TableCell>
                                            <TableCell align="left">Add Lead</TableCell>

                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {rows.map((row, i) => (
                                            <TableRow 
                                                key={i} 
                                                className="moreDeatilsData"
                                                style={row.IsNBTSorted === true ? {
                                                    background: 'linear-gradient(90deg, rgb(255 249 180) 0%, rgb(251 245 188) 49%, rgba(255, 255, 255, 1) 100%)'
                                                } : {}}
                                            >
                                                <TableCell align="left">{i + 1}</TableCell>
                                                <TableCell align="left">{row.LeadId !== undefined ? row.LeadId : ""}</TableCell>
                                                <TableCell align="left">{row.CustName !== undefined ? row.CustName : ""}</TableCell>
                                                <TableCell align="left">{row.StatusName ? row.StatusName : ""}</TableCell>                                           
                                                <TableCell align="left">{row.TotalAttempts ? row.TotalAttempts : 0}</TableCell>
                                                <TableCell align="left">{row.TotalTT ? ToMinutesSeconds(row.TotalTT) : 0}</TableCell>
                                                <TableCell width="13%" align="left">
                                                    {row.CreatedOn !== undefined ?
                                                        dayjs(row.CreatedOn).format('DD/MM/YYYY h:mm a') : ""}
                                                </TableCell>
                                                <TableCell align="left">
                                                    {row.EventDate !== undefined ?
                                                        dayjs(row.EventDate).format('DD/MM/YYYY h:mm:ss a') : ""}
                                                </TableCell>
                                                <TableCell align="left">
                                                    {row.CallbackType ? CallBack[row.CallbackType] : ""}
                                                </TableCell>
                                                <TableCell align="left">
                                                    {row.Country ? row.Country : "INDIA"}
                                                </TableCell>
                                                <TableCell align="left">
                                                    {row.CallTiming ? row.CallTiming : "N.A"}
                                                </TableCell>
                                                <TableCell align="left" >
                                                    <input
                                                        type="checkbox"
                                                        name={row.LeadId}
                                                        value={row.IsChecked || false}
                                                        checked={row.IsChecked || false}
                                                        onChange={(event) => { handleChange(event, row.LeadId) }}
                                                    />
                                                </TableCell>

                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                            </TableContainer>
                        </div>
                        <div className="text-center">
                            <button onClick={clearAll} className="clearAllBtn">Clear All</button>
                            <button onClick={() => { AddLeadsToQueue() }} className="addLeadBtn">Add Leads</button>
                        </div>
                    </>
                )}

        </ModalPopup>
    )
};

const mapDispatchToProps = dispatch => {
    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
    };
};
export default connect(() => ({}), mapDispatchToProps)(NCLeadPopup);


