import React from "react";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { Typography } from "@mui/material";

export const ReferralPopup = (props) => {
    const { handleClose, open } = props;
    
    if (!open) return null;

    return (
        <ModalPopup
            className="ReferralPopup"
            title="Referral Reminder"
            open={open}
            handleClose={handleClose}
            showCloseButton={true}
        >
            <div className="referral-popup-content">
                {/* Main Question */}
                <Typography variant="h6" className="main-question">
                    Did you ask your customer for a referral?
                </Typography>

                {/* Why section */}
                <div className="why-section">
                    <Typography variant="subtitle1" className="why-title">
                        Why you have to ask for referrals?
                    </Typography>
                    
                    <div className="benefits-list">
                        <div className="benefit-item">
                            <div className="benefit-icon">🚀</div>
                            <Typography variant="body1">
                                Boost your conversions by up to 3X
                            </Typography>
                        </div>
                        
                        <div className="benefit-item">
                            <div className="benefit-icon">💰</div>
                            <Typography variant="body1">
                                Unlock higher incentive slabs
                            </Typography>
                        </div>
                        
                        <div className="benefit-item">
                            <div className="benefit-icon">🌐</div>
                            <Typography variant="body1">
                                Grow your customer base / Network
                            </Typography>
                        </div>
                    </div>
                </div>

            </div>
        </ModalPopup>
    );
};
