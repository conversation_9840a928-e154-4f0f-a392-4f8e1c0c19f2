import { Button, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, CircularProgress, TableSortLabel } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalPopup from "../../../../../../components/Dialogs/ModalPopup";
import { useSnackbar } from "notistack";
import { FetchRolloverLeadsforUser } from "../../helper/sidebarHelper";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

const RolloverPanel = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [loading, setLoading] = useState(false);
    const [leads, setLeads] = useState([]);
    const [fromDate, setFromDate] = useState(dayjs().startOf('month'));
    const [toDate, setToDate] = useState(dayjs().endOf('month'));
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [orderBy, setOrderBy] = useState('BookingStatusName');
    const [order, setOrder] = useState('asc');

    const handleFromDateChange = (newValue) => {
        if (!newValue) return;
        setFromDate(newValue);
        // Auto-update To Date to maintain 30-day window
        const newToDate = dayjs(newValue).add(30, 'day');
        if (newToDate.isAfter(dayjs())) {
            setToDate(dayjs()); // Set to today if 30 days would exceed current date
        } else {
            setToDate(newToDate);
        }
    };

    const handleToDateChange = (newValue) => {
        if (!newValue) return;
        const maxDate = dayjs(fromDate).add(30, 'day');
        if (newValue.isAfter(maxDate)) {
            enqueueSnackbar("Date range cannot exceed 30 days", { variant: 'warning' });
            setToDate(maxDate);
        } else if (newValue.isBefore(fromDate)) {
            enqueueSnackbar("To Date cannot be before From Date", { variant: 'warning' });
            setToDate(fromDate);
        } else {
            setToDate(newValue);
        }
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const fetchLeads = async () => {
        try {
            setLoading(true);
            const response = await FetchRolloverLeadsforUser(
                dayjs(new Date(fromDate)).format('YYYY-MM-DD'),
                dayjs(new Date(toDate)).format('YYYY-MM-DD')
            );
            if (response) {
                setLeads(response);
            }
        } catch (error) {
            enqueueSnackbar("Error fetching leads", { variant: 'error' });
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (props.open) {
            fetchLeads()
        }
    }, [fromDate, toDate]);

    const handleClose = () => {
        setFromDate(dayjs().startOf('month'));
        setToDate(dayjs().endOf('month'));
        props.handleClose();
    }

    const handleSort = () => {
        const isAsc = orderBy === 'BookingStatusName' && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy('BookingStatusName');
        
        const sortedLeads = [...leads].sort((a, b) => {
            if (a.BookingStatusName < b.BookingStatusName) {
                return order === 'asc' ? -1 : 1;
            }
            if (a.BookingStatusName > b.BookingStatusName) {
                return order === 'asc' ? 1 : -1;
            }
            return 0;
        });
        setLeads(sortedLeads);
    };

    return (
        <ModalPopup open={props.open} title="Rollover Leads" handleClose={handleClose} className="RolloverLeadPopup">
            <Grid container spacing={2}>
                {/* Date Filters */}
                <Grid item xs={12} container spacing={2}>
                    <Grid item xs={12} md={4}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                                label="From Date"
                                value={fromDate}
                                onChange={handleFromDateChange}
                                maxDate={dayjs()}
                                format="DD/MM/YYYY"
                                slotProps={{
                                    textField: { fullWidth: true }
                                }}
                            />
                        </LocalizationProvider>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                                label="To Date"
                                value={toDate}
                                onChange={handleToDateChange}
                                minDate={fromDate}
                                maxDate={dayjs(fromDate).add(30, 'day')}
                                format="DD/MM/YYYY"
                                slotProps={{
                                    textField: { fullWidth: true }
                                }}
                            />
                        </LocalizationProvider>
                    </Grid>
                    <Grid item xs={12} md={4}>
                        <Button
                            variant="contained"                            
                            onClick={() => fetchLeads()}
                            className="ViewBtn"
                        >
                            View
                        </Button>
                    </Grid>
                </Grid>

                {/* Results Table */}
                <Grid item xs={12}>
                    <TableContainer component={Paper} aria-label="simple table" >
                        {loading ? (
                            <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                <CircularProgress />
                            </div>
                        ) : (
                            <>
                                <Table stickyHeader>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>Renewal Lead</TableCell>
                                            <TableCell>Rollover Lead</TableCell>
                                            <TableCell>Renewal Status</TableCell>
                                            <TableCell>
                                                <TableSortLabel
                                                    active={orderBy === 'BookingStatusName'}
                                                    direction={order}
                                                    onClick={handleSort}
                                                >
                                                    Rollover Status
                                                </TableSortLabel>
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {leads.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((lead, index) => (
                                            <TableRow key={index}>
                                                <TableCell>{lead.RenewalLead}</TableCell>
                                                <TableCell>{lead.BookingID}</TableCell>
                                                <TableCell>{lead.RenewalStatusName}</TableCell>
                                                <TableCell>{lead.BookingStatusName}</TableCell>
                                            </TableRow>
                                        ))}
                                        {leads.length === 0 && (
                                            <TableRow>
                                                <TableCell colSpan={4} align="center">
                                                    No leads found
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                                <TablePagination
                                    rowsPerPageOptions={[5, 10, 25]}
                                    component="div"
                                    count={leads.length}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                />
                            </>
                        )}
                    </TableContainer>
                </Grid>
            </Grid>
        </ModalPopup>
    );
};

export default RolloverPanel;
