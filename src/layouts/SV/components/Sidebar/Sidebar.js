import React, { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import makeStyles from '@mui/styles/makeStyles';
import PropTypes from "prop-types";
import Drawer from "@mui/material/Drawer";
import CssBaseline from "@mui/material/CssBaseline";
import { SidebarNav } from "./components";
import User from "../../../../services/user.service";
import { CONFIG } from "../../../../appconfig";
import { useDispatch, useSelector } from "react-redux";
import masterService from "../../../../services/masterService";
import rootScopeService from "../../../../services/rootScopeService";
import { setPlayRemainingTimeService } from "../../../../views/SV/Header/PlayPause";
import SidebarDrawer from './SidebarDrawer';
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { updateStateInRedux } from "../../../../store/actions/SalesView/SalesView";
import AgentInfo from "../../../../views/SV/Main/AgentInfo";
import MenuIcon from '@mui/icons-material/Menu';
import { ErrorBoundary } from "../../../../hoc";
import NotificationsPopup from "./components/Modals/NotificationsPopup";
import BMSInternalEmailPopup from "../../../../views/SV/RightBar/Modals/BMSInternalEmailPopup";
import { isAgentDashboardVisible, IsPaymentOverdueVisible, OpenAgentDashboard, OpenInternalEmailINNewTab, ShowNewMyBookings,GetConsolidatedBizHealthDataService,GetBHRPercentageAndColorService } from "./helper/sidebarHelper";
import AgentStories from "./components/Modals/AgentStories";
import { CALL_API } from '../../../../services';
import { eraseCookie } from "../../../../store/token-store/Token-Store";
import { useSnackbar } from 'notistack';
import { isNonProgressiveWfhCalling } from "../../../../helpers";
import { GetIframeURL } from "../../../../../src/services/api.service";
import { IsApptfeedbackLead, IsCustomerAccess } from "../../../../services/Common";
import {localStorageCache} from "../../../../../src/utils/utility";
import CustReqCallBacks from "./components/Modals/CustReqCallBackPopup";
import { getMongoNotificationData, getSearchObjFromLS } from "../../../../helpers/commonHelper";

const drawerWidth = 240;

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
  },
  logo: {
    maxWidth: "122px",
  },
  hide: {
    display: "none",
  },
  drawer: {
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: "nowrap",
  },
  drawerClose: {
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    overflowX: "hidden",
    width: 0,
    [theme.breakpoints.up("sm")]: {
      width: theme.spacing(7) + 0,
    },
    "& $logo": {
      backgroundImage: `url('${CONFIG.PUBLIC_URL}/images/logo_mob.png')`,
      backgroundPosition: "center",
      backgroundSize: "contain",
      backgroundRepeat: "no-repeat",
      maxWidth: "100%",
      position: "unset",
      height: 30,
      width: 30,
      margin: "5px auto",
    },
  },
  svg: {
    backgroundColor: "#ffcccc",
  },
  notificationTooltip: {
    background: '#fff'
  },
  notificationTooltipArrow: {
    color: '#fff'
  }
}));


const Sidebar = (props) => {
  let notificationDatafromRedux = useSelector(state => state.salesview.notificationData);
  let notificationDataNewFromRedux = useSelector(state => state.salesview.notificationDataNew);
  let showNewNotificationPanel = useSelector(state => state.salesview.showNewNotificationPanel);
  let PaymentOverdueCountfromRedux = useSelector(state => state.salesview.PaymentOverdueCount);
  let isSidebarDrawerOpen = useSelector(state => state.salesview.isSidebarDrawerOpen);
  const classes = useStyles();

  let Ispriority = rootScopeService.getPriority();

  const dispatch = useDispatch();
  const [currentPopup, setCurrentPopup] = useState(null);
  const [NewStoryForUser, setNewStoryForUser] = useState(false);
  const [NewStoryOnLogout, setNewStoryOnLogout] = useState(0);
  const [NewStoryUserCount, setNewStoryUserCount] = useState(0);
  const [AgentStoryDetails, setAgentStoryDetails] = useState([]);
  const [autoPlaySpeed, setAutoPlaySpeed] = useState(0);
  const { enqueueSnackbar } = useSnackbar();
  const [CallBackPopup, setCallBackPopup] = useState(false);
  const [nxt60minCallbacks, setNxt60minCallbacks] = useState({});
  const [logoutwindow, setLogoutwindow] = useState(false);
  const [NewStoryNotificationVisible, setNewStoryNotificationVisible] = useState(false);
  const [open, setOpen] = useState(false);
  const [UserProduct, setUserProduct] = useState(0);
  const prevNewStoryUserCountRef = useRef();


  const GetUnreadNotifications = () => {
    let count = 0;
    let MongoNotification = getMongoNotificationData();
    count = Array.isArray(MongoNotification) ?
      MongoNotification.filter(item => item.IsRead !== true).length
      : 0;
    // if(notificationDatafromRedux && notificationDatafromRedux.length > 0)
    // {
    //   count += Array.isArray(notificationDatafromRedux) ? notificationDatafromRedux.filter(item => item.IsRead !== true).length : 0
    // }
    // if(showNewNotificationPanel && notificationDataNewFromRedux && notificationDataNewFromRedux.length > 0)
    // {
    //   count += Array.isArray(notificationDataNewFromRedux) ? notificationDataNewFromRedux.filter(item => item.IsRead !== true).length : 0
    // }
    return count;
  };

  const handlePopupOpen = (name) => () => {
    setCurrentPopup(name);
    if (name == 'AgentStories') {
      if (AgentStoryDetails && AgentStoryDetails[0] && AgentStoryDetails[0].TemplateType == 2) {
        setAutoPlaySpeed(300000);
      }
    }
  };


  let NewTab = OpenInternalEmailINNewTab();

  const checkLogout = () => {

    if (User.RoleId !== 13) {
      fnLogout();
      return;
    }

    let leadId = rootScopeService.getLeadId();
    if (leadId == 0) {
      let assignLeads = getSearchObjFromLS();
      if (Array.isArray(assignLeads) && assignLeads.length > 0) {
        leadId = assignLeads[0].LeadID
      } else {
        fnLogout();
        return;
      }
    }

    const input = {
      url: `coremrs/api/CallBackSchedular/GetCallBacks`,
      method: 'POST',
      service: 'MatrixCoreAPI',
      timeout: 2000,
      requestData: {
        "AgentId": User.RoleId === 13 ? User.UserId : 0,
        "Id": leadId,
        "IsCore": false
      }
    };

    CALL_API(input).then((result) => {
      var nxt60minCallbacks = {};

      if (result && result.ErrorCode === 0 && result.Data) {
        Array.isArray(result.Data.events) && result.Data.events.forEach((event) => {
          if (result.Data.LeadAssignedUser && result.Data.LeadAssignedUser == User.UserId) {
            const nxt60minsDateTime = new Date();
            nxt60minsDateTime.setHours(nxt60minsDateTime.getHours() + 1);
            if ((new Date(event.StartDate)) >= (new Date()) && (new Date(event.StartDate)) <= nxt60minsDateTime) {
              /* NOTE:-  
                nxt60minCallbacks
                  key = 0 (Payment CallBacks)
                  key = 1 (Customer Requested)
                  key = 2 (Customer Agreed)
              */

              if (event.IsPaymentCallback) {
                event.Color = 0;
              }

              if ([0, 1, 2].includes(event.Color) &&
                (!nxt60minCallbacks[event.Color] || new Date(event.StartDate) < new Date(nxt60minCallbacks[event.Color]))
              ) {
                nxt60minCallbacks[event.Color] = event.StartDate;
              }
            }
          }
        });
      } else {
        nxt60minCallbacks["apiRespNotReceived"] = true;
      }

      if (nxt60minCallbacks && Object.keys(nxt60minCallbacks).length > 0) {
        setNxt60minCallbacks(nxt60minCallbacks);
        setLogoutwindow(true);
      } else {
        fnLogout();
      }
    }).catch(() => {
      setNxt60minCallbacks({ "apiRespNotReceived": true });
      setLogoutwindow(true);
    });
  }

  const fnLogout = () => {
    if (NewStoryOnLogout == 1) {
      setCurrentPopup("AgentStories");
      enqueueSnackbar("You have Pending Stories to View. Click on Stories under Menu", {
        variant: 'success',
        autoHideDuration: 3000,
      });
    }
    else {

      //Todo list  need to discuss
      // if (AgentCall && AgentCall.popwin && AgentCall.popwin.oSipSessionCall != null) {
      //     $scope.setLogout = true;
      //     $scope.pause = 1;
      //     $scope.LogPlayPause(false);

      //     var modalInstanceOpen = $modal.open({
      //         animation: true,
      //         size: 'sm',
      //         template: '<div class="modal-body ng-scope" style="height:50px"> Your logout action is in queue, you will automatically logged out after this call.</div>'
      //     });
      //     window.parent.scroll(0, 0);
      // } else {
      try {
        eraseCookie('posPayment');
      }
      catch { }
      dispatch(updateStateInRedux({ key: "logoutInProcess", value: true }));
      // masterService.updateagentloginstatus(User.EmployeeId, "LOGOUT");
      // masterService.updateagentstatus(User.EmployeeId, "LOGOUT");
      masterService.updateagentstatus(User.EmployeeId, "UNAVAILABLE");

      try {
        if (window.logoutCallWindow) {
          window.logoutCallWindow("LogoutClick");
        }
      }
      catch { }

      // if ($scope.showProgressivePlayPause) {
      //     var userid = $scope.User.UserId;
      //     topNavService.SetPlayRemainingTime(userid).then(function (resultData) { }, function () {

      //     })
      // }

      if (window.localStorage.getItem("IsPause") === "true") {
        setPlayRemainingTimeService(User.UserId);
      }
      //clear storage
      // localStorage.clear();
      var url = "/PGV/Leads/BlankLogout.aspx?logoutSV=1";
      window.localStorage.removeItem('OnVideoCall');
      window.localStorage.removeItem('callDisposition');
      setTimeout(function () {
        window.location.href = url;
      }, 1000);
      //}
    }
  }

  const OpenURLinNewWindow = (key) => {
    const url = GetIframeURL(key);
    window.open(url, "_blank");
  }
  const openSidebarDrawer = (title) => () => {
    // fn to expand only one menu inside drawer
    dispatch(updateStateInRedux({ key: "isSidebarDrawerOpen", value: true }))
    dispatch(updateStateInRedux({
      key: "sidebarDrawerOpenMenus",
      value: [title]
    }));
  }

  const checkIsAvailableStory = () => {
    const input = {
      url: `api/SalesView/GetAgentStory/`,
      method: 'GET', service: 'MatrixCoreAPI'
    };
    CALL_API(input).then((result) => {
      if (result != null && result.IsSuccess == true && result.Message != "[]") {

        setAgentStoryDetails(JSON.parse(result.Message));
        let story = JSON.parse(result.Message);
        if (Array.isArray(story) && story.filter(item => item.IsViewed == false).length > 0) {
          setNewStoryForUser(true);
          const newStoryCount = story.filter(item => item.IsViewed === false).length;
          setNewStoryUserCount(newStoryCount);
          setNewStoryOnLogout(NewStoryOnLogout + 1)
        }
        else {
          setNewStoryForUser(false);
          setNewStoryUserCount(0);
          setNewStoryOnLogout(0)
        }
        return;
      }
    });
  }

  useEffect(() => {
    checkIsAvailableStory();

    // Check if BHR should be shown based on user's product list and role
    let usergrp = User.ProductList;
    let productid = 0;
    if (Array.isArray(usergrp)) {
      for (const item of usergrp) {
        if ([115, 1001].indexOf(item.ProductId) !== -1) {
          productid = 115;
          break;
        } else if ([7, 1000].indexOf(item.ProductId) !== -1) {
          productid = 7;
          break;
        }
      }
    }
    setUserProduct(productid);

    if ((productid === 115 || productid === 7) && User.RoleId === 13) {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      let BizRatingAgentId = window.localStorage.getItem('BizRatingAgentId') ? window.localStorage.getItem('BizRatingAgentId') : 0;
      let LastBizRatingSetTime = window.localStorage.getItem('LastBizRatingSetTime') ? window.localStorage.getItem('LastBizRatingSetTime') : "";
      let BizRatingPercentage = window.localStorage.getItem('BizRatingPercentage') ? window.localStorage.getItem('BizRatingPercentage') : -2;
      //Expiry time of LastBizRatingSetTime is 1 day
      if (BizRatingPercentage === -2 || BizRatingAgentId === 0 || parseInt(BizRatingAgentId) !== User.UserId || LastBizRatingSetTime === "" || (Number(Date.parse(new Date())) - Number(window.localStorage.getItem('LastBizRatingSetTime')) >= 86400000)) {
        GetBHRPercentageAndColorService(User.UserId,productid).then((result) => {
          if(result){
            if(result.Status == true){

              window.localStorage.setItem('BizRatingAgentId', User.UserId);
              window.localStorage.setItem('LastBizRatingSetTime', Date.parse(new Date()));
              if(result.Data != null && result.Data != undefined){
                window.localStorage.setItem('BizRatingPercentage', result.Data.Percentage);
                
                const root = document.documentElement;
                root.style.setProperty('--background-color', result.Data.BGColorCode);
                root.style.setProperty('--button-color', '#E6E6E6');
                root.style.setProperty('--box-color', result.Data.BoxColorCode);

                window.localStorage.setItem('BHRBGColorCode', result.Data.BGColorCode);
                window.localStorage.setItem('BHRBoxColorCode', result.Data.BoxColorCode);

                if (productid === 7) {
                  window.localStorage.setItem('UserSuperGroup', result.Data.UserSuperGroup);
                  window.localStorage.setItem('BHRTooltipParagraph', result.Data.TooltipParagraph);
                }
              }
              else{
                window.localStorage.setItem('BizRatingPercentage', 0);
              }
            }
            else{
              window.localStorage.setItem('BizRatingPercentage', 0);
            }
          }
        })
        GetConsolidatedBizHealthDataService(User.UserId, productid).then((result) => {
          if (Array.isArray(result)) {
            window.localStorage.setItem('ConsolidatedInforcementData', JSON.stringify(result));
          }
          else { window.localStorage.setItem('ConsolidatedInforcementData', []); }
        });
      }
    }

  }, [])

  useEffect(() => {
    // Compare current notificationCount with previous value
    if (NewStoryUserCount > prevNewStoryUserCountRef.current) {
      setNewStoryNotificationVisible(true);
      setOpen((prev) => !prev);
    }

    // Update the previous value
    prevNewStoryUserCountRef.current = NewStoryUserCount;
    // Set timeout to hide the class after 30 seconds
    const timeout = setTimeout(() => {
      setNewStoryNotificationVisible(false);
    }, 10000);

    // Cleanup function to clear timeout when component unmounts or visibility changes
    return () => clearTimeout(timeout);
  }, [NewStoryUserCount]);

  let pages = [
    {
      title: "My Bookings",
      href: "#",
      icon: <img alt="myBooking" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/mybookingIcon.svg"} />,
      show: (User.RoleId === 13 || User.RoleId === 12),
      onClick: ShowNewMyBookings
    },
    {
      title: "BHR",
      show: ((UserProduct === 115 || UserProduct === 7) && User.RoleId === 13 && !IsCustomerAccess()),
      href: "#",
      icon: (
        <div className="bhr-icon-container">
          <span
            className="bhr-bullet-badge"
            style={{
              backgroundColor: window.localStorage.getItem('BHRBoxColorCode') || '#4CAF50'
            }}
          >
            {window.localStorage.getItem('BizRatingPercentage') || '0'}%
          </span>
          <img alt="BHR" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/dashboard.svg"} />

        </div>
      ),
      onClick: () => { window.open('../BusinessHealthRatingPopup', "_blank"); }
    },
    {
      title: "Stories",
      setNewStory: NewStoryForUser,
      setNewStoryUserCount: NewStoryUserCount,
      open: open,
      NewStoryNotificationVisible: NewStoryNotificationVisible,
      href: "#",
      icon: NewStoryForUser
        ? <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/NeedsAttention-White.svg"} />
        : <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/NeedsAttention.svg"} />,

      //icon:  <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/NeedsAttention-White.svg"} />,
      //show: true,
      show: (!IsCustomerAccess()),
      onClick: handlePopupOpen("AgentStories")
    },
    {
      title: "Dashboard",
      href: "#",
      icon: <img alt="Agent Dashboard" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/dashboard.svg"} />,
      show: (isAgentDashboardVisible() > 0 && (!IsCustomerAccess())),
      onClick: OpenAgentDashboard
    },
    {
      title: "Leads",
      href: "#",
      icon: <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/lead.svg"} />,
      //show: Ispriority,
      show: (!IsCustomerAccess()),
      onClick: openSidebarDrawer("Leads")
    },
    {
      title: IsApptfeedbackLead() ? "Leads" : "Cases",
      href: "#",
      icon: <img alt="Leads" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/lead.svg"} />,
      //show: Ispriority,
      show: IsCustomerAccess(),
      onClick: openSidebarDrawer(IsApptfeedbackLead() ? "Leads" : "Cases")
    },
    {
      title: "Payment Overdue",
      href: "#",
      // icon: <Badge badgeContent="New" color="secondary" className="NewBadge">
      //   <span className="counter">
      //   {PaymentOverdueCountfromRedux}
      // </span>
      //   <img alt="PaymentOverdue" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/PaymentOverdueV2.svg"} className="fosIconPayOverdueV2" />
      // </Badge>,
      icon: <Badge color="secondary" className="NewBadge">
        <span color="secondary" className="counter">
          {PaymentOverdueCountfromRedux}
        </span>
        <img alt="PaymentOverdue" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/PaymentOverdueV2.svg"} className="fosIconPayOverdueV2" />
      </Badge>,
      show: (IsPaymentOverdueVisible() && !IsCustomerAccess()),
      onClick: () => { OpenURLinNewWindow("PaymentFailedCases"); }
    },
    {
      title: "FOS",
      href: "#",
      icon: <img alt="FOS" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/fosIcon.svg"} />,
      show: (User.RoleId === 13 && !IsCustomerAccess()),
      onClick: openSidebarDrawer("FOS")
    },
    {
      title: "Performance",
      href: "#",
      icon: <img alt="Performance" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/Performance.svg"} />,
      //show: Ispriority,
      show: (!IsCustomerAccess()),
      onClick: openSidebarDrawer("Performance")
    },

    {
      title: "More",
      href: "#",
      icon: <img alt="More" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/more.svg"} />,
      //show: Ispriority,
      show: (!IsCustomerAccess()),
      // onMouseEnter: openMoreMenu,
      // onMouseLeave: handleClose
      onClick: openSidebarDrawer("More")

    },
    {
      title: "Notifications",
      href: "#",
      icon: <> <span className="counter">
        {GetUnreadNotifications()}
      </span><img alt="Notifications" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/notification.svg"} /></>,
      show: (!IsCustomerAccess() && (!User.IsProgressive || !(User.RoleId === 13 && Ispriority))),
      onClick: handlePopupOpen("NotificationsPopup")
      // tooltip: {
      //   title: <Notifications />,
      //   interactive: true,
      //   placement: "right-end",
      //   classes: {
      //     tooltip: classes.notificationTooltip,
      //     arrow: classes.notificationTooltipArrow
      //   }
      // }
    },
    {
      title: "Internal Email",
      href: "#",
      icon: <img alt="Internal Email" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/InternalEmail.svg"} />,
      show: (!IsCustomerAccess()),
      onClick: handlePopupOpen("BMSInternalEmailPopup")
    },

    {
      title: "Logout",
      href: "#",
      icon: <img alt="Logout" src={CONFIG.PUBLIC_URL + "/images/salesview/menuicon/logout.svg"} />,
      show: Ispriority,
      onClick: checkLogout
    },

  ];
  if (User.RoleId !== 13) {
    return null;
  }
  return (
    <>
      <div className={`${classes.root} leftmenu`} >

        <SidebarDrawer fnLogout={checkLogout} />
        <CssBaseline />

        <Drawer
          variant="permanent"
          className={clsx(classes.drawer, classes.drawerClose)}
          classes={{
            paper: clsx(classes.drawerClose),
          }}
        >
          {/* <div className={classes.logo}></div> */}
          {!isNonProgressiveWfhCalling() &&
            <Button className="toggleIcon" onClick={() => {
              dispatch(updateStateInRedux({ key: "isSidebarDrawerOpen", value: !isSidebarDrawerOpen }))
            }}>
              <MenuIcon />
            </Button>
          }
          {/* <div className={classes.logo}></div> */}
          <AgentInfo />
          <SidebarNav className={classes.nav} pages={pages} />
        </Drawer>
      </div >
      <ErrorBoundary name="NotificationsPopup">
        <NotificationsPopup open={currentPopup === "NotificationsPopup"} handleClose={() => { setCurrentPopup(null) }} />
      </ErrorBoundary>
      <ErrorBoundary name="BMSInternalEmailPopup">
        <BMSInternalEmailPopup open={currentPopup === "BMSInternalEmailPopup"} IsNewTab={NewTab} handleClose={() => { setCurrentPopup(null) }} />
      </ErrorBoundary>
      {/* <ErrorBoundary name="AgentStories"> */}
      <AgentStories open={currentPopup === "AgentStories"} handleClose={() => { setCurrentPopup(null); checkIsAvailableStory() }}
        data={(AgentStoryDetails && AgentStoryDetails.length > 0) ? AgentStoryDetails : []} autoPlaySpeed={autoPlaySpeed} />
      {/* </ErrorBoundary> */}

      <CustReqCallBacks callBackPopup={CallBackPopup} setCallBackPopup={setCallBackPopup} logoutwindow={logoutwindow} setLogoutwindow={setLogoutwindow} fnLogout={() => fnLogout()} nxt60minCallbacks={nxt60minCallbacks} checkLogout={() => checkLogout()} />
    </>
  );
};



Sidebar.propTypes = {
  className: PropTypes.string,
  variant: PropTypes.string.isRequired,
  user: PropTypes.object,
};


export default Sidebar;
