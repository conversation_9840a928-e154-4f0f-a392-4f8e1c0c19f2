const StaticData =
{
	"PolicyType": {
		"Health": [
			{
				"PolicyTypeId": 0,
				"PolicyTypeName": "New"
			},
			{
				"PolicyTypeId": 1,
				"PolicyTypeName": "Renewal"
			}
		],
		"Investment": [
			{
				"PolicyTypeId": 0,
				"PolicyTypeName": "New"
			},
			{
				"PolicyTypeId": 1,
				"PolicyTypeName": "Renewal"
			}
		],
		"Term": [
			{
				"PolicyTypeId": 0,
				"PolicyTypeName": "New"
			},
			{
				"PolicyTypeId": 1,
				"PolicyTypeName": "Renewal"
			}
		],
		"Motor": [
			{
				"PolicyTypeId": 0,
				"PolicyTypeName": "Fresh"
			},
			{
				"PolicyTypeId": 1,
				"PolicyTypeName": "Renewal"
			},
			{
				"PolicyTypeId": 2,
				"PolicyTypeName": "RollOver"
			}
		],
		"SME": [
			{
				"PolicyTypeId": 0,
				"PolicyTypeName": "Fresh"
			},
			{
				"PolicyTypeId": 1,
				"PolicyTypeName": "Renewal"
			},
			{
				"PolicyTypeId": 2,
				"PolicyTypeName": "RollOver"
			}
		]
	},
	"BookingType": {
		"Direct": 1,
		"Offline": 2
	},
	"PropertyCoverType": [
		{
			"ID": 1,
			"Name": "Structure"
		},
		{
			"ID": 2,
			"Name": "Content"
		},
		{
			"ID": 3,
			"Name": "Both"
		}
	],
	"PropertyType": [
		{
			"ID": 1,
			"Name": "Individual"
		},
		{
			"ID": 2,
			"Name": "Society"
		}
	],
	"PropertyPurpose": [
		{
			"ID": 1,
			"Name": "Loan"
		},
		{
			"ID": 2,
			"Name": "Personal"
		}
	],
	"PaymentMode": {
		"Online": 1,
		"Transfer": 3001,
		"Direct Payment": 4001,
		"Cheque/DD": 5001,
		"Cash": 6001
	},
	"PaymentModeMotor": {
		"Transfer": 3001,
		"Direct Payment": 4001,
		"Cheque/DD": 5001,
		"Cash": 6001
	},
	"PaymentPeriodicity": {
		"Monthly": 1,
		"Quarterly": 2,
		"Half Yearly": 3,
		"Yearly": 4,
		"Single Premium": 5
	},
	"PaymentPeriodicityByProduct": {
		"SME": [{ "Yearly": 4 }]

	},
	"PaymentSource": ["Credit Card", "Debit Card", "Net-Banking", "Wallet"],
	"disableAgentBookingProducts": [2],
	"EMI": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 0,
			"Name": "No"
		}
	],
	"InspectionRequired": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 2,
			"Name": "No"
		}
	],
	"InspectionType": [
		{
			"ID": 1,
			"Name": "Recommended"
		},
		{
			"ID": 2,
			"Name": "Decline"
		}
	],
	"ChildEducationCost": {
		"EducationCost": [
			{
				"Title": "Doctor",
				"TitleId": 1,
				"No": "10",
				"Yes": "40"
			},
			{
				"Title": "Engineer",
				"TitleId": 2,
				"No": "8",
				"Yes": "30"
			},
			{
				"Title": "MBA",
				"TitleId": 3,
				"No": "10",
				"Yes": "40"
			},
			{
				"Title": "Pilot",
				"TitleId": 4,
				"No": "15",
				"Yes": "45"
			},
			{
				"Title": "CA",
				"TitleId": 5,
				"No": "6",
				"Yes": "24"
			},
			{
				"Title": "Fashion Designer",
				"TitleId": 6,
				"No": "6",
				"Yes": "24"
			},
			{
				"Title": "Lawyer",
				"TitleId": 7,
				"No": "8",
				"Yes": "30"
			},
			{
				"Title": "Others",
				"TitleId": 8,
				"No": "5",
				"Yes": "20"
			}
		]
	},
	"UpSell": [
		{
			"Id": 1,
			"Name": "Tenure Change"
		},
		{
			"Id": 2,
			"Name": "Sum Insured Increase"
		}
	],
	"CrossSell": [
		{
			"Id": 1,
			"Name": "Tenure Change"
		},
		{
			"Id": 2,
			"Name": "Sum Insured Increase"
		}
	],
	"IsRSA": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 0,
			"Name": "No"
		}
	],
	"IsTP": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 0,
			"Name": "No"
		}
	],
	"OptionYN": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 0,
			"Name": "No"
		}
	],
	"CoverType": [
		{
			"Id": 1,
			"Name": "E"
		},
		{
			"Id": 2,
			"Name": "ESK"
		},
		{
			"Id": 3,
			"Name": "ESKP"
		},
		{
			"Id": 0,
			"Name": "NA"
		}
	],
	"TransitTypes": [
		{
			"Id": "Single Transit",
			"Name": "Single Transit"
		},
		{
			"Id": "Annual Open",
			"Name": "Annual Open"
		},
		{
			"Id": "STOP",
			"Name": "STOP"
		},
		{
			"Id": "Project Marine",
			"Name": "Project Marine"
		}
	],
	"ShipmentTypes": [
		{
			"Id": "Inland",
			"Name": "Inland"
		},
		{
			"Id": "Import",
			"Name": "Import"
		},
		{
			"Id": "Export",
			"Name": "Export"
		},
		{
			"Id": "Merchant Trading",
			"Name": "Merchant Trading"
		}
	],
	"MarineCoverTypes": [
		{
			"Id": "1",
			"Name": "All Risk Cover"
		},
		{
			"Id": "2",
			"Name": "Basic Cover"
		}
	],
	"NotificationTypes": [
		{
			"Id": "0",
			"Name": "Other"
		},
		{
			"Id": "1",
			"Name": "Email"
		},
		{
			"Id": "2",
			"Name": "Quotes"
		},
		{
			"Id": "3",
			"Name": "Whatsapp"
		},
		{
			"Id": "4",
			"Name": "Customer"
		}
	],
	"CoInsuranceTypes": [
		{
			"Id": 1,
			"Name": "Yes"
		},
		{
			"Id": 0,
			"Name": "No"
		}
	],
	"Languages": [
		{
			"ID": 1,
			"Name": "Assamese"
		},
		{
			"ID": 2,
			"Name": "Bengali"
		},
		{
			"ID": 3,
			"Name": "Bodo"
		},
		{
			"ID": 4,
			"Name": "Dogri"
		},
		{
			"ID": 5,
			"Name": "English"
		},
		{
			"ID": 6,
			"Name": "Gujarati"
		},
		{
			"ID": 7,
			"Name": "Hindi"
		},
		{
			"ID": 8,
			"Name": "Kannada"
		},
		{
			"ID": 9,
			"Name": "Kashmiri"
		},
		{
			"ID": 10,
			"Name": "Konkani"
		},
		{
			"ID": 11,
			"Name": "Maithili"
		},
		{
			"ID": 12,
			"Name": "Malayalam"
		},
		{
			"ID": 13,
			"Name": "Marathi"
		},
		{
			"ID": 14,
			"Name": "Meitei"
		},
		{
			"ID": 15,
			"Name": "Nepali"
		},
		{
			"ID": 16,
			"Name": "Odia"
		},
		{
			"ID": 17,
			"Name": "Punjabi"
		},
		{
			"ID": 18,
			"Name": "Sanskrit"
		},
		{
			"ID": 19,
			"Name": "Santali"
		},
		{
			"ID": 20,
			"Name": "Sindhi"
		},
		{
			"ID": 21,
			"Name": "Tamil"
		},
		{
			"ID": 22,
			"Name": "Telugu"
		},
		{
			"ID": 23,
			"Name": "Urdu"
		}
	],
	"IsSTP": {
		"STP": 1,
		"NSTP": 0
	},
	"OccupationTypes": [
		{
			"Id": "Manufacturer",
			"Name": "Manufacturer"
		},
		{
			"Id": "Trader",
			"Name": "Trader"
		},
		{
			"Id": "Individual",
			"Name": "Individual"
		}
	],
	"ConstitutionOfBusinesses": [
		{
			"Id": "Foreign LLP",
			"Name": "Foreign LLP"
		},
		{
			"Id": "Foreign company registered in India",
			"Name": "Foreign company registered in India"
		},
		{
			"Id": "Government department",
			"Name": "Government department"
		},
		{
			"Id": "Hindu undivided family",
			"Name": "Hindu undivided family"
		},
		{
			"Id": "LLP Partnership",
			"Name": "LLP Partnership"
		},
		{
			"Id": "Local Authorities",
			"Name": "Local Authorities"
		},
		{
			"Id": "Non resident entity",
			"Name": "Non resident entity"
		},
		{
			"Id": "Partnership",
			"Name": "Partnership"
		},
		{
			"Id": "Private limited company",
			"Name": "Private limited company"
		},
		{
			"Id": "Proprietorship",
			"Name": "Proprietorship"
		},
		{
			"Id": "Others",
			"Name": "Others"
		}
	],
	"Inclusion": [
		{
			"Id": "1",
			"Name": "War"
		},
		{
			"Id": "2",
			"Name": "SRCC"
		},
		{
			"Id": "3",
			"Name": "ODC"
		},
		{
			"Id": "4",
			"Name": "Bulk Cargo"
		},
		{
			"Id": "5",
			"Name": "Temperate Sensitive"
		},
		{
			"Id": "6",
			"Name": "Tail-end cover"
		}
	],
	"MedicalExtensions": [
		{
			"Id": "0",
			"Name": "0"
		},
		{
			"Id": "25000",
			"Name": "25000"
		},
		{
			"Id": "50000",
			"Name": "50000"
		},
		{
			"Id": "75000",
			"Name": "75000"
		},
		{
			"Id": "100000",
			"Name": "100000"
		},
		{
			"Id": "Other",
			"Name": "Other"
		}
	],
	"WorkerTypes": [
		{
			"Id": "Skilled",
			"Name": "Skilled"
		},
		{
			"Id": "Semi-Skilled",
			"Name": "Semi-Skilled"
		},
		{
			"Id": "Un-Skilled",
			"Name": "Un-Skilled"
		},
		{
			"Id": "Other",
			"Name": "Other"
		}
	],
	"SmePropertyType": [
		{
			"ID": 1,
			"Name": "Owned"
		},
		{
			"ID": 2,
			"Name": "Tenant"
		}
	],
	"InsuredScopes": [
		{
			"Id": "Building",
			"Name": "Building"
		},
		{
			"Id": "Content",
			"Name": "Content"
		},
		{
			"Id": "Stock",
			"Name": "Stock"
		}
	],
	"DroneCategoryType":[
		{
			"Id": 1,
			"Name": "Nano"
		},
		{
			"Id": 2,
			"Name": "Micro"
		},
		{
			"Id": 3,
			"Name": "Small"
		},
		{
			"Id": 4,
			"Name": "Medium"
		}
	],
	//for all expect those which are inlcuded in BookingFromTypes
	"BookingFromTypesSME":[
		{
			"Id": "POS",
			"Name": "POS"
		},
		{
			"Id": "RFQ",
			"Name": "RFQ"
		}
	],
	//For selected sme subproducts
	"BookingFromTypes":[
		{
			"Id": "RFQ",
			"Name": "RFQ"
		},
		{
			"Id": "CJ",
			"Name": "CJ"
		}
	],
	"PolicyCategory":[
		{
			"Id": "Named Policy",
			"Name": "Named Policy"
		},
		{
			"Id": "Un-Named Policy",
			"Name": "Un-Named Policy"
		},
	],
	"BookingCategory":[
		{
			"Id": "New",
			"Name": "New"
		},
		{
			"Id": "Endorsement",
			"Name": "Endorsement"
		},
	],
	"PlanType":[
		{
			"Id": "Self",
			"Name": "Self"
		},
		{
			"Id": "Family",
			"Name": "Family"
		},
	]
	
}
export default StaticData;