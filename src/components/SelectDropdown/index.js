import React from 'react';
import PropTypes from 'prop-types';
import { Grid, MenuItem, TextField, useMediaQuery } from "@mui/material";
import makeStyles from '@mui/styles/makeStyles';
import isEqual from "lodash/isEqual";
import { useTheme } from "@mui/material/styles";

const SelectDropdown = (props) => {
    const useStyles = makeStyles((theme) => ({
        root: {
            textAlign: 'center',
            paddingTop: "0px",
            '& .MuiTextField-root': {
                // margin: theme.spacing(1),
                marginTop: '0px'
            },
            '& .MuiOutlinedInput-root': {
                height: '38px',
                textAlign: "left"
            }
        },
    }));
    const classes = useStyles();
    const theme = useTheme();

    const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
        defaultMatches: true
    });
    let { name, value = '', handleChange, show = true, label, shrink } = props;
    let { options, valueKeyInOptions, labelKeyInOptions, labelKeyInSecondOpt } = props;
    let { xs = 12, sm = 6, md = 3, lg } = props;
    let { size = "small", variant = "outlined", className, disabled } = props;
    let { native = false, fixOptionListBelow } = props;
    let OptionComponent = MenuItem;
    let MenuProps;
    if (!show) return null;
    if (!Array.isArray(options)) {
        // console.error(name, "select dropdown: options SHOULD BE AN ARRAY ")
        return null;
    }
    if (options && options.filter((o) => (valueKeyInOptions === '_all' ? isEqual(o, value) : (o[valueKeyInOptions] == value))).length === 0) {
        value = '';
    }
    if (!isDesktop) {
        native = false;
    }
    if (native) {
        OptionComponent = (optionProps) => (<option {...optionProps} />);
    }
    if (fixOptionListBelow) {
        MenuProps = {
            anchorOrigin: {
                vertical: "bottom",
                horizontal: "left"
            },
            transformOrigin: {
                vertical: "top",
                horizontal: "left"
            },
            getContentAnchorEl: null
        }
    }

    return (
        <Grid item xs={xs} sm={sm} md={md} lg={lg} className={classes.root}>
            <TextField
                id={name}
                name={name}
                select
                label={label}
                value={value}
                onChange={handleChange}
                variant={variant}
                size={size}
                fullWidth
                disabled={disabled}
                className={className}
                SelectProps={{
                    native,
                    MenuProps
                }}
                InputProps={props.InputProps}
                InputLabelProps={props.shrink ? props.shrink : { shrink: (!native && ['', undefined, null].includes(value)) ? false : true }}
            >
                {options.map((option, index) => (
                    <OptionComponent
                        key={index}
                        value={valueKeyInOptions === '_all' ? option : option[valueKeyInOptions]}
                        className={name=="OccupancyId" ? 'Widthoccupancy' : ''} // Conditional className assignment
                    >
                        {labelKeyInOptions === '_all' ? option : option[labelKeyInOptions]}
                        { labelKeyInSecondOpt && option[labelKeyInSecondOpt] ? ` (${option[labelKeyInSecondOpt]})` : ''}
                        </OptionComponent>
                ))}
            </TextField>
        </Grid>
    )
}

SelectDropdown.propTypes = {
    options: PropTypes.array.isRequired,
    valueKeyInOptions: PropTypes.string.isRequired,
    labelKeyInOptions: PropTypes.string.isRequired,
    value: PropTypes.any,
    handleChange: PropTypes.func.isRequired
}
export default SelectDropdown;