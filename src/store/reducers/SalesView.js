

import * as actionTypes from '../ActionsTypes';

//const initialState = {};
const InitialState = {
    allLeads: [],
    parentLeadId: 0,
    primaryLeadId: 0,
    leadIds: "",
    subProductId: -1,
    SetManualStamping: true,
    StampingPanel: false,
    SetProductId: 0,
    IsRenewal: 0,
    AgentStats: [],
    next5leads: [],
    notificationData: [],
    LstAgentLeads: [],
    RefreshLead: false,
    CreateBookingData: {},
    IsCallCreateBooking: false,
    RefreshAgentStats: false,
    IsAutoDone: false,
    OpenRightBarMenu: null,
    IsCallTransferVisible: false,
    RefreshCallBackDetails: false,
    ActiveCallBackDetails: {},
    IsIncentive: 0,
    IsAnswered: false,
    IsCustomerVerified: false,
    RenewalStampingFlag: 0,
    noOfLeadCardsToShow: 0,
    isSidebarDrawerOpen: false,
    sidebarDrawerOpenMenus: [],
    logoutInProcess: false,
    IsAffiliatelead: false,
    ObjRestrictCall_errorMsg: null,
    CallRestrictRejection: false,
    currentLead: 0,
    renewalStampingFlag: 0,
    RefreshProductRecommendations: false,
    ProductCountryId: 0,
    bookingcancelreason: null,
    PODTodayTalkTime: 0,
    PODHistoryTalktime: 0,
    IsPODDoneShow: true,
    IsPODDurationCount: false,
    IsShowPODlink: true,
    IsShowPODTransfer: false,
    AgentIBNumber_wfhNew: null,
    IsbookedLeadSet: false,
    IsRejectedLeadSet: false,
    ticketsData: [],
    issuedPolicies: [],  // customer history  
    refreshTicketsData: false,
    IsVcEligible: false,
    IsPrimary: [],
    FOSCity: null,
    callOnCurrentLead: false,
    openRightbarMenuMobileOptions: false,
    isAsteriskTokenVerified: true,
    AppointmentDateTime: "",
    AppointmentSummaryRefresh: false,
    FOSLeadChurnReason: null,
    IsRescheduling: false,
    RefreshCustomerId: "",
    CommentsPopUp: { LeadID:  0, show : false},
    CommentsAdded: false,
    SubStatusAdded: false,
    SubStatusPopUp: false,
    Leadsexpiry:"",
    AppointmentData:null,
    CallType:"",
    PlanPitchPopup:false,
    ISFOSIntentFlag:false,
    IsSearchBarCall:false,
    UserSelectedAddress:null,
    FOSHealthRenewalTTEligibility:false,
    NonPaymentReasonPopUp: false

}

const setLeads = (state, action) => {
    let { allLeads } = action.payload;
    allLeads = Array.isArray(allLeads) ? allLeads : [];
    return {
        ...state,
        allLeads: allLeads,
        parentLeadId: allLeads && allLeads.length > 0 && allLeads[0].ParentID,
        primaryLeadId: allLeads && allLeads.length > 0 && allLeads[0].LeadID,
        leadIds: allLeads && allLeads.length > 0 && allLeads.reduce((acc, lead) => `${acc}${lead.LeadID};`, ''),
        subProductId: allLeads && allLeads.length > 0 && allLeads[0].SubProductTypeId,
        ProductCountryId: allLeads && allLeads.length > 0 && allLeads[0].ProductCountryId,
    }
}


const setManualStamping = (state = InitialState, action) => {
    let { SetManualStamping } = action.payload;
    return {
        ...state,
        SetManualStamping
    };
}

const setRenewal = (state = InitialState, action) => {

    let { IsRenewal } = action.payload;
    return {
        ...state,
        IsRenewal
    };
}
const setBizRating = (state = InitialState, action) => {

    let { BizRating } = action.payload;
    return {
        ...state,
        BizRating
    };
}
const setPaymentOverdueCount = (state = InitialState, action) => {

    let { PaymentOverdueCount } = action.payload;
    return {
        ...state,
        PaymentOverdueCount
    };
}
const setStampingPanel = (state = InitialState, action) => {

    let { StampingPanel } = action.payload;
    return {
        ...state,
        StampingPanel
    };
}
const setRenewalStampingFlag = (state = InitialState, action) => {
    let { RenewalStampingFlag } = action.payload;
    return {
        ...state,
        RenewalStampingFlag
    };
}
const SetProductId = (state = InitialState, action) => {
    let { SetProductId } = action.payload;
    return {
        ...state,
        SetProductId
    };
}

const setAgentstats = (state = InitialState, action) => {

    let { AgentStats } = action.payload;
    return {
        ...state,
        AgentStats: AgentStats
    };
}

const setnotificationData = (state = InitialState, action) => {

    let { notificationData } = action.payload;
    return {
        ...state,
        notificationData: notificationData
    };
}

const setNext5LeadsData = (state = InitialState, action) => {

    let { next5leads } = action.payload;
    return {
        ...state,
        next5leads: next5leads
    };
}
const setLstAgentLeads = (state = InitialState, action) => {

    let { LstAgentLeads } = action.payload;
    return {
        ...state,
        LstAgentLeads: LstAgentLeads
    };
}

const setRefreshLead = (state = InitialState, action) => {

    let { RefreshLead } = action.payload;
    return {
        ...state,
        RefreshLead: RefreshLead
    };
}

const setIsIncentive = (state = InitialState, action) => {

    let { IsIncentive } = action.payload;
    return {
        ...state,
        IsIncentive: IsIncentive
    };
}

const setRefreshAgentStats = (state = InitialState, action) => {

    let { RefreshAgentStats } = action.payload;
    return {
        ...state,
        RefreshAgentStats: RefreshAgentStats
    };
}

const setCreateBookingData = (state = InitialState, action) => {

    let { CreateBookingData } = action.payload;
    return {
        ...state,
        CreateBookingData: CreateBookingData
    };
}

const setIsCallCreateBooking = (state = InitialState, action) => {

    let { IsCallCreateBooking } = action.payload;
    return {
        ...state,
        IsCallCreateBooking: IsCallCreateBooking
    };
}

const setIsAutoDone = (state = InitialState, action) => {
    let { IsAutoDone } = action.payload;
    return {
        ...state,
        IsAutoDone: IsAutoDone
    };
}

const setOpenRightBarMenu = (state = InitialState, action) => {
    let { OpenRightBarMenu } = action.payload;
    return {
        ...state,
        OpenRightBarMenu
    };
}

const setIsCallTransferVisible = (state = InitialState, action) => {
    let { IsCallTransferVisible } = action.payload;
    return {
        ...state,
        IsCallTransferVisible
    };
}

const setRefreshCallBackDetails = (state = InitialState, action) => {
    let { RefreshCallBackDetails } = action.payload;
    return {
        ...state,
        RefreshCallBackDetails
    };
}
const setActiveCallBackDetails = (state = InitialState, action) => {
    let { ActiveCallBackDetails } = action.payload;
    return {
        ...state,
        ActiveCallBackDetails
    };
}
const setIsAnswered = (state = InitialState, action) => {
    let { IsAnswered } = action.payload;
    return {
        ...state,
        IsAnswered
    };
}
const setCustomerOTPStatus = (state = InitialState, action) => {
    let { IsCustomerVerified } = action.payload;
    return {
        ...state,
        IsCustomerVerified
    };
}


const clearRedux = () => {
    return InitialState;
}

const setNoOfLeadCardsToShow = (state = InitialState, action) => {
    let noOfLeadCardsToShow = action.payload;
    return {
        ...state,
        noOfLeadCardsToShow
    };
}

const updateStateInRedux = (state = InitialState, action) => {
    let key = action.payload.key;
    let value = action.payload.value;
    return {
        ...state,
        [key]: value
    };
}

const setConnectedLeadsData = (state = InitialState, action) => {

    let { coonectedLeads } = action.payload;
    return {
        ...state,
        coonectedLeads: coonectedLeads
    };
}
const setRefreshCustomerId = (state = InitialState, action) => {

    let { RefreshCustomerId } = action.payload;
    return {
        ...state,
        RefreshCustomerId: RefreshCustomerId
    };
}
const setCallType = (state = InitialState, action) => {
    let { CallType } = action.payload;
    return {
        ...state,
        CallType: CallType
    };
}

const reducer = (state = InitialState, action) => {
    switch (action.type) {
        case actionTypes.SET_LEADS: return setLeads(state, action);
        case actionTypes.SET_MANUALSTAMPING: return setManualStamping(state, action);
        case actionTypes.SET_RENEWAL: return setRenewal(state, action);
        case actionTypes.SET_BIZRATING: return setBizRating(state, action);
        case actionTypes.SET_PAYMENTOVERDUECOUNT: return setPaymentOverdueCount(state, action);
        case actionTypes.SET_PRODUCTID: return SetProductId(state, action);
        case actionTypes.SET_AGENTSTATS: return setAgentstats(state, action);
        case actionTypes.SET_NOTIFICATIONDATA: return setnotificationData(state, action);
        case actionTypes.SET_LSTAGENTLEADS: return setLstAgentLeads(state, action);
        case actionTypes.SET_NEXT5LEADS: return setNext5LeadsData(state, action);
        case actionTypes.SET_REFRESHLEAD: return setRefreshLead(state, action);
        case actionTypes.SET_ISCALLCREATEBOOKING: return setIsCallCreateBooking(state, action);
        case actionTypes.SET_CREATEBOOKINGDATA: return setCreateBookingData(state, action);
        case actionTypes.SET_REFRESHAGENTSTATS: return setRefreshAgentStats(state, action);
        case actionTypes.SET_ISAUTODONE: return setIsAutoDone(state, action);
        case actionTypes.SET_OPENRIGHTBARMENU: return setOpenRightBarMenu(state, action);
        case actionTypes.SET_IS_CALLTRANSFER_VISIBLE: return setIsCallTransferVisible(state, action);
        case actionTypes.SET_REFRESH_CALLBACK_DETAILS: return setRefreshCallBackDetails(state, action);
        case actionTypes.SET_ACTIVE_CALLBACK_DETAILS: return setActiveCallBackDetails(state, action);
        case actionTypes.SET_ISINCENTIVE: return setIsIncentive(state, action);
        case actionTypes.SET_ISANSWERED: return setIsAnswered(state, action);
        case actionTypes.SET_CUST_OTP_STATUS: return setCustomerOTPStatus(state, action);
        case actionTypes.SET_STAMPINGPANEL: return setStampingPanel(state, action);
        case actionTypes.CLEAR_REDUX: return clearRedux(state, action);
        case actionTypes.SET_RENEWAL_STAMPING_FLAG: return setRenewalStampingFlag(state, action);

        case actionTypes.SET_NO_OF_LEADCARDSTOSHOW: return setNoOfLeadCardsToShow(state, action);
        case actionTypes.UPDATE_STATE_IN_REDUX: return updateStateInRedux(state, action);
        case actionTypes.SET_CONNECTEDLEADS: return setConnectedLeadsData(state, action);
        case actionTypes.SET_REFRESHCUSTOMERID: return setRefreshCustomerId(state, action);
        case actionTypes.SET_CALLTYPE: return setCallType(state, action);
        default:
            return state;
    }

};


export default reducer;