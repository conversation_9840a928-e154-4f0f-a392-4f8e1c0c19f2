import * as actionTypes from '../../ActionsTypes';
export const getLeads = () => {
    return {
        type: actionTypes.GET_LEADS
    };
};

export const set_allleads = (payload) => {
    return {
        type: actionTypes.SET_LEADS,
        payload: payload
    };
};

export const set_ManualStamping = (payload) => {
    return {
        type: actionTypes.SET_MANUALSTAMPING,
        payload: payload
    };
};

export const set_Renewal = (payload) => {
    return {
        type: actionTypes.SET_RENEWAL,
        payload: payload
    };
};
export const set_BizRating = (payload) => {
    return {
        type: actionTypes.SET_BIZRATING,
        payload: payload
    };
};
export const set_PaymentOverDueCount = (payload) => {
    return {
        type: actionTypes.SET_PAYMENTOVERDUECOUNT,
        payload: payload
    };
};

export const set_ProductId = (payload) => {
    return {
        type: actionTypes.SET_PRODUCTID,
        payload
    };
};

export const setStampingPanel = (payload) => {
    return {
        type: actionTypes.SET_STAMPINGPANEL,
        payload
    };
};

export const setContactedVisible = (payload) => {
    return {
        type: actionTypes.SET_CONTACTEDVISIBLE,
        payload
    };
};



export const setAgentstats = (payload) => {
    return {
        type: actionTypes.SET_AGENTSTATS,
        payload: payload
    };
};

export const setnotificationData = (payload) => {
    return {
        type: actionTypes.SET_NOTIFICATIONDATA,
        payload: payload
    };
};

export const setNext5LeadsData = (payload) => {
    return {
        type: actionTypes.SET_NEXT5LEADS,
        payload: payload
    };
};
export const getNext5LeadsData = () => {
    return {
        type: actionTypes.GET_NEXT5LEADS
    };
};
export const setLstAgentLeads = (payload) => {
    return {
        type: actionTypes.SET_LSTAGENTLEADS,
        payload: payload
    };
};

export const setRefreshLead = (payload) => {
    return {
        type: actionTypes.SET_REFRESHLEAD,
        payload: payload
    };
};
export const setIsIncentive = (payload) => {
    return {
        type: actionTypes.SET_ISINCENTIVE,
        payload: payload
    };
};

export const setIsCallCreateBooking = (payload) => {
    return {
        type: actionTypes.SET_ISCALLCREATEBOOKING,
        payload: payload
    };
};
export const setCreateBookingData = (payload) => {
    return {
        type: actionTypes.SET_CREATEBOOKINGDATA,
        payload: payload
    };
};

export const setRefreshAgentStats = (payload) => {
    return {
        type: actionTypes.SET_REFRESHAGENTSTATS,
        payload: payload
    };
};

export const setIsAutoDone = (payload) => {
    return {
        type: actionTypes.SET_ISAUTODONE,
        payload: payload
    };
};

export const setOpenRightBarMenu = (payload) => {
    return {
        type: actionTypes.SET_OPENRIGHTBARMENU,
        payload
    };
};

export const setIsCallTransferVisible = (payload) => {
    return {
        type: actionTypes.SET_IS_CALLTRANSFER_VISIBLE,
        payload
    };
};
export const setRefreshCallBackDetails = (payload) => {
    return {
        type: actionTypes.SET_REFRESH_CALLBACK_DETAILS,
        payload
    };
};
export const setActiveCallBackDetails = (payload) => {
    return {
        type: actionTypes.SET_ACTIVE_CALLBACK_DETAILS,
        payload
    };
};

export const setIsAnswered = (payload) => {
    return {
        type: actionTypes.SET_ISANSWERED,
        payload
    };
};
export const setCustomerOTPStatus = (payload) => {
    return {
        type: actionTypes.SET_CUST_OTP_STATUS,
        payload
    };
};
export const setRenewalStampingFlag = (payload) => {
    return {
        type: actionTypes.SET_RENEWAL_STAMPING_FLAG,
        payload
    };
};
export const setNoOfLeadCardsToShow = (payload) => {
    return {
        type: actionTypes.SET_NO_OF_LEADCARDSTOSHOW,
        payload
    };
};

export const updateStateInRedux = (payload) => {
    return {
        type: actionTypes.UPDATE_STATE_IN_REDUX,
        payload
    };
};

export const clearRedux = () => {
    return {
        type: actionTypes.CLEAR_REDUX,
    };
};
export const setConnectedLeadsData = (payload) => {
    return {
        type: actionTypes.SET_CONNECTEDLEADS,
        payload: payload
    };
};
export const setRefreshCustomerId = (payload) => {
    return {
        type: actionTypes.SET_REFRESHCUSTOMERID,
        payload: payload
    };
};
export const setCallType = (payload) => {
    return {
        type: actionTypes.SET_CALLTYPE,
        payload: payload
    };
};