/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import Progressive from './Progressive';
import { connect, useSelector } from 'react-redux'
import { SV_CONFIG } from "../../../appconfig";
import { CALL_API } from "../../../services";
import { setAgentstats, setLstAgentLeads, setNext5LeadsData, setnotificationData, setRefreshAgentStats, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import User from "../../../services/user.service";
import { useSnackbar } from "notistack";
import { ErrorBoundary } from "../../../hoc";
import { useInterval } from "../Main/helpers/useInterval";
import MessageStrip from "./messageStrip";
import JagSuperstar from "./JagSuperstar";
import Common from "../Main/helpers/Common";
import { useDispatch } from "react-redux";
import Search from "./Search";
import RightBarMobileMenuIcon from "./RightBarMobileMenuIcon";
import { localStorageCache } from "../../../utils/utility";
import { markVideoCallAgentIdle } from "../../../helpers/commonHelper";
import { IsApptfeedbackLead, IsCustomerAccess } from "../../../services/Common";

const updateagentCallstatus = (UserID) => {
    const input = {
        url: `customer/updateagentstatus?onCall=true`,
        method: 'POST', service: 'CustomerNotificationURL',
        requestData: { 'status': "IDLE", 'UserID': UserID }
    };
    return CALL_API(input);
}

const Header = (props) => {
    const [AgentStatsData, setAgentStatsData] = useState([]);
    const { enqueueSnackbar } = useSnackbar();
    const [notificationData, setNotificationData] = useState([]);
    const [read_notifications, setRead_notifications] = useState([]);
    const [read_notificationsNew, setread_notificationsNew] = useState([]);

    // const [unreadNotification, setUnreadNotification] = useState(0);
    let RefreshAgentStats = useSelector(state => state.salesview.RefreshAgentStats);
    let AgentIBNumber_wfhNew = useSelector(state => state.salesview.AgentIBNumber_wfhNew);
    let interval = null;

    const [BookedLeads, setBookedLeads] = useState([]);
    const [BMSUserToken, setBMSUserToken] = useState("");
    const [agentIbNumbermessage, setagentIbNumbermessage] = useState('');
    const [ConnectCallSF, setConnectCallSF] = useState(false);

    const dispatch = useDispatch();
    const isMobile = useSelector(state => state.common.isMobile);
    let showNewNotificationPanel = useSelector(state => state.salesview.showNewNotificationPanel);

    const setAuthenticationStatus = (isAuthenticated) => {
        localStorageCache.writeToCache('isTokenVerified', isAuthenticated, 30 * 1000);
        dispatch(updateStateInRedux({ key: "isAsteriskTokenVerified", value: isAuthenticated }));
    }

    const getagenstats = () => {
        // console.log("Flow1: getagentstats called", new Date());
        let UserId = User.UserId;
        const input = {
            url: SV_CONFIG["CustomerNotificationURL"][SV_CONFIG["environment"]] + "customer/getagenstats/" + UserId + "/",
            method: 'GET', service: 'custom', timeout:'s'
        };

        CALL_API(input)
            .then(function (resultData) {
                setAuthenticationStatus(true);
                if (resultData.length > 0) {
                    setAgentStatsData(resultData);
                    props.setAgentStatsDataToRedux(resultData);
                    // console.log("Flow1: getagentstats response", new Date());

                    if (resultData && resultData.length > 0) {
                        let agentdata = resultData[0];

                        // set agentstatus IDLE if agent is stuck on 'Call Initiated' for more than 15s
                        if (agentdata.hasOwnProperty('serverdate') && agentdata.serverdate != null && agentdata.hasOwnProperty('LastUpdatedOn') && agentdata.LastUpdatedOn != null) {
                            localStorage.setItem('serverdate', agentdata.serverdate);
                            var serverdate = new Date(agentdata.serverdate);
                            var lastupdatedOn = new Date(agentdata.LastUpdatedOn);
                            var timeDiff = (serverdate - lastupdatedOn) / 1000;
                            if (agentdata.status && agentdata.status.toUpperCase() === "CALLINITIATED" && timeDiff > 15) {
                                updateagentCallstatus(User.UserId).then(function (resultData) {
                                    // console.log("updateagentCallstatus ");
                                });
                            }
                        }

                        //if agent is on video call 
                        if (agentdata && agentdata.status && agentdata.status.toUpperCase() == "VIDEOMEET") {
                            localStorage.setItem("OnVideoCall", "1");
                        }
                        else {
                            localStorage.setItem("OnVideoCall", "0");
                        }
                        //if agent is on unavailable Status 
                        if (agentdata && agentdata.status && agentdata.status.toUpperCase() == "UNAVAILABLE") {
                            localStorage.setItem("AgentUnavailable", "1");
                        }
                        else {
                            localStorage.setItem("AgentUnavailable", "0");
                        }
                        //Notification
                        if (agentdata.notifications != undefined || agentdata.notifications != null) {
                            if (notificationData.length == 0) {
                                SetNotificationsDataSet(agentdata.notifications);
                            }
                            else if (agentdata.notifications.length > notificationData.length) {
                                SetNotificationsDataSet(agentdata.notifications);

                                let lastnotification = agentdata.notifications.length - 1;
                                var lastobj = agentdata.notifications[lastnotification];
                                if (lastobj["type"] === "HWPitchPlan") {
                                    enqueueSnackbar("You got a new notification for HWPitchPlan!", { variant: 'success', autoHideDuration: 3000, });
                                }
                                else {
                                    enqueueSnackbar("You got a new notification!", { variant: 'success', autoHideDuration: 3000, });
                                }
                            }
                            else {
                                SetNotificationsDataSet(agentdata.notifications);
                            }

                            //If there is some notification which is present in dataset fetched from redis , but not in dataset fetched from Mongo, then append that record in Local storage
                            var MongoNotification = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')) : [];
                            
                            // Extract records from redisnotifications that are not present in Mongonotifications
                            // // Append the missing records to Mongonotifications
                            if(MongoNotification && Array.isArray(MongoNotification))
                            {
                            agentdata.notifications.forEach(notification => {
                                const index = MongoNotification.findIndex(obj => obj.id === notification.id);
                              
                                if (index == -1) {
                                    MongoNotification.push(notification);
                                }
                              });
                            localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(MongoNotification), 5 * 60 * 60 * 1000);
                            }

                            let serverdate = new Date(agentdata.serverdate);
                            let comparetime = new Date(agentdata.serverdate);
                            let comparetime2 = new Date(agentdata.serverdate);
                            comparetime.setHours(14);
                            comparetime.setMinutes(0);
                            comparetime.setSeconds(0);

                            comparetime2.setHours(17);
                            comparetime2.setMinutes(0);
                            comparetime2.setSeconds(0);

                            let ct = (comparetime - serverdate) / 1000;
                            let ct2 = (comparetime2 - serverdate) / 1000;

                            if ((ct <= 5 && ct >= 0) || (ct2 <= 5 && ct2 >= 0)) {
                                enqueueSnackbar("Check the notifications and visit LMS", { variant: 'success', autoHideDuration: 3000, });
                            }
                        }
                        if (agentdata.read_notifications != undefined || agentdata.read_notifications != null) {
                            setRead_notifications(JSON.parse(agentdata.read_notifications));
                        }

                        if (resultData && agentdata.BookedLeads != undefined && agentdata.BookedLeads != null) {
                            setBookedLeads(agentdata.BookedLeads);
                            setBMSUserToken(resultData.BMSUserToken);
                        }

                        // nextfiveleads
                        if (Array.isArray(agentdata.Leads)) {
                            AddPrimaryMobileandStatus(agentdata.Leads)
                            props.setNext5LeadsToRedux(agentdata.Leads);
                        }
                        else {
                            props.setNext5LeadsToRedux([]);
                        }
                        // New_WFH Phone Number
                        const isWfhNew = Common.isUserWfhNew(User);
                        if (isWfhNew && agentdata && agentdata.status && agentdata.status.toUpperCase() === 'UNAVAILABLE' && AgentIBNumber_wfhNew) {
                            enqueueSnackbar(agentIbNumbermessage, {
                                variant: 'warning',
                                autoHideDuration: 10000,
                                className: "wfhNewAgentNotConnected",
                                preventDuplicate: true,
                                // content: message,
                                anchorOrigin: {
                                    vertical: 'top',
                                    horizontal: 'center',
                                }
                            })
                        }

                        markVideoCallAgentIdle(agentdata, 45)

                        //Token Compare
                        // var secretToken = window.localStorage.getItem('AsteriskToken');

                        // if (agentdata.AsteriskToken != null 
                        //     && agentdata.AsteriskToken !== secretToken 
                        //     && window.AgentCall.popwin 
                        //     && !window.AgentCall.popwin.oSipSessionCall
                        // ) {
                        //     if (agentdata.AsteriskToken == "123456789") {
                        //        window.alert("You have been logged out from the system. Please login again");
                        //     }
                        //     else {
                        //         window.alert("Session is already in progress for provided username, please logout and re-login to continue");
                        //     }

                        //     // logout from salesview 
                        //     window.localStorage.clear();
                        //     window.location.href = "/newsv/Login.aspx";
                        // }
                    }
                }
                else {
                    return [];
                }

            })
            .catch((err) => {
                if (err.status === 401) {
                    setAuthenticationStatus(false);
                }
                else {
                    setAuthenticationStatus(true);
                }
            });
    };

    const GetAgentNotification = () => {
        // if(showNewNotificationPanel)
        // {
            let NoOfDays = -2;
            if(showNewNotificationPanel)
            {
                NoOfDays = -7;
            }
            const input = {
                url: "api/WebSiteService/GetNotificationsData?NumberOfDays=" + NoOfDays,
                method: 'GET',
                service: 'MatrixCoreAPI',
            }
            CALL_API(input).then(function (resultData) {
                if (resultData) 
                {
                    let notificationsDataNew = resultData.notifications;
                    let read_notificationsNew = resultData.read_notifications;

                    if (read_notificationsNew && notificationsDataNew) 
                    {
                        notificationsDataNew.forEach((elem1, index) => {
                            read_notificationsNew.forEach((elem2, index1) => {
                                if (elem1.id == elem2) {
                                    elem1.IsRead = true;
                                }
                            });
                        })
                    }
                    dispatch(updateStateInRedux({ key: "notificationDataNew", value: notificationsDataNew }))
                    setread_notificationsNew(read_notificationsNew);
                    localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(notificationsDataNew), 5 * 60 * 60 * 1000);
                    localStorageCache.writeToCache("MongoReadNotificationData", JSON.stringify(read_notificationsNew), 5 * 60 * 60 * 1000);
                    let AsteriskToken = localStorageCache.getItem('AsteriskToken');
                    localStorage.setItem("NotifyAsterickToken",  AsteriskToken);
                }
                else {
                    return [];
                }
            })
            .catch((err) => {
                // log this
            });
        //}
    };

    // const verifyAsteriskToken = () => {
    //     verifyTokenService().then((isAsteriskTokenVerified) => {
    //         dispatch(updateStateInRedux({ key: "isAsteriskTokenVerified", value: isAsteriskTokenVerified }));
    //     }).catch((err) => {
    //         console.warn('verifyAsteriskToken ERR', err);
    //     })
    // }
    const AddPrimaryMobileandStatus = (TempLeads) => {
        let IsPrimary = [];
        TempLeads.forEach(function (element) {
            var IsPrimaryMobile = window.localStorage.getItem("IsPrimaryMobile");
            if (IsPrimaryMobile != null && IsPrimaryMobile != undefined) {
                IsPrimary = JSON.parse(IsPrimaryMobile);
            }
            if (IsPrimary.length > 0) {
                IsPrimary.forEach(function (mobile) {
                    if (mobile.LeadId == element.LeadId) {
                        element.isPrimaryMobile = mobile.IsPrimary;
                        element.status = mobile.status;
                    }
                });
            }
        })
        dispatch(updateStateInRedux({ key: "IsPrimary", value: IsPrimary }));
    }

    const SetNotificationsDataSet = (notificationDataInput) => {
        if(showNewNotificationPanel)
        {
            filterUnreadNotification(notificationDataInput);
        }
        else
        {
            setNotificationData(notificationDataInput);
        }
    }

    const filterUnreadNotification = (notificationDataInput) => {
        let readNotificatiosnData = read_notifications;
        let allNotifications = JSON.parse(JSON.stringify(notificationData));

        if(showNewNotificationPanel && read_notificationsNew && read_notificationsNew.length > 0)
        {
            readNotificatiosnData = [...read_notifications, ...read_notificationsNew]            
        }
        if(showNewNotificationPanel && notificationDataInput)
        {
            allNotifications = notificationDataInput;
        }

        allNotifications.forEach((elem1, index) => {
            readNotificatiosnData.forEach((elem2, index1) => {
                if (JSON.stringify(elem1.id) == elem2 || JSON.stringify(elem1.id) == JSON.stringify(elem2)) {
                    elem1.IsRead = true;
                }
            });
        })
        setNotificationData(allNotifications);
        let MongoNotificationData = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')) : [];
        if(MongoNotificationData && Array.isArray(MongoNotificationData))
        {
            MongoNotificationData.forEach((elem1, index) => {
                readNotificatiosnData.forEach((elem2, index1) => {
                    if (JSON.stringify(elem1.id) == elem2 || JSON.stringify(elem1.id) == JSON.stringify(elem2)) {
                        elem1.IsRead = true;
                    }
                });
            })
            localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(MongoNotificationData), 5 * 60 * 60 * 1000);
        }
    }

    // const CallAgentstats = () => {

    //     if (interval === null) {
    //         interval = setInterval(() => {
    //             getagenstats();
    //         }, 5000);
    //     }

    // }

    useInterval(() => {
        getagenstats();
        // if (User.IsWFH) {
        //     verifyAsteriskToken();
        // }
    }, 5000)

    useInterval(() => {
        if(showNewNotificationPanel)
        {
            GetAgentNotification();
        }
    }, 12000)

    // useEffect(() => {
    //     CallAgentstats();
    // }, [])

    useEffect(() => {
        if (RefreshAgentStats) {
            getagenstats();
            props.setRefreshAgentStatsToRedux(false);
            if(showNewNotificationPanel)
            {
                GetAgentNotification();
            }
        }
    }, [RefreshAgentStats])

    useEffect(() => {
        if (read_notifications.length > 0) {
            filterUnreadNotification(null);
        }
        // setUnreadNotification(notificationData.filter(item => item.IsRead !== true).length);
    }, [read_notifications])

    useEffect(() => {
        //Token Compare
        var secretToken = window.localStorage.getItem('AsteriskToken');
        var PreviousToken = window.localStorage.getItem('NotifyAsterickToken')
        if((localStorageCache.readFromCache('MongoNotificationData') == null) || (PreviousToken != secretToken))
        {
            window.localStorage.removeItem('MongoNotificationData');
            GetAgentNotification();
        }
        if(Array.isArray(User.ProductList))
        {
            User.ProductList.forEach(function (item) {
                if (item.ProductId == 131) 
                {
                    dispatch(updateStateInRedux({ key: "showNewNotificationPanel", value: true }))
                }
            });
        }
        localStorage.setItem("NotifyAsterickToken",  secretToken);
    }, [])

    useEffect(() => {
        if (notificationData.length > 0) {
            props.setNotificationDataToRedux(notificationData);
        }
    }, [notificationData])

    useEffect(() => {
        setagentIbNumbermessage(<>
            <p>You are not connected, Please call on </p>
            <p>{"0" + AgentIBNumber_wfhNew}</p>
        </>)
    }, [AgentIBNumber_wfhNew])

    useInterval(() => {
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

        if (onCall) {
            setConnectCallSF(true);
        }
        else {
            setConnectCallSF(false);
        }
    }, 1000);

    return (
        <>
            <ErrorBoundary name="Progressive">
                <Progressive />
            </ErrorBoundary>
            {isMobile &&
                <div className="headerFixOnMb">
                    <Search />
                    <ErrorBoundary name="Righbar menu icon">
                        <RightBarMobileMenuIcon />
                    </ErrorBoundary>
                </div>
            }
            {(!IsCustomerAccess() || (IsCustomerAccess() && ConnectCallSF) || IsApptfeedbackLead()) && <div className="spacehight"></div>}
            <div>
                <MessageStrip />
            </div>
            {/* <JagSuperstar /> */}
        </>
    )
}



const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {
    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
        setAgentStatsDataToRedux: (AgentStats) => dispatch(setAgentstats({ AgentStats: AgentStats })),
        setNotificationDataToRedux: (notificationData) => dispatch(setnotificationData({ notificationData: notificationData })),
        setLstAgentCallableLeadsToRedux: (LstAgentLeads) => dispatch(setLstAgentLeads({ LstAgentLeads: LstAgentLeads })),
        setNext5LeadsToRedux: (leads) => dispatch(setNext5LeadsData({ next5leads: leads })),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(Header);