import React, { useEffect, useState } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import MuiDialogTitle from "@mui/material/DialogTitle";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useTheme } from "@mui/material/styles";
import withStyles from '@mui/styles/withStyles';
import { Grid, IconButton, LinearProgress, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import TipsAndUpdatesIcon from "@mui/icons-material/TipsAndUpdates";
import PhoneIcon from "@mui/icons-material/Phone";
import Slide from "@mui/material/Slide";
import { CALL_API } from "../../../../services";
import User from "../../../../services/user.service";
import {AddLeadToPriorityQueueService, checkTimeZoneService} from "../../../../services/Common";
import { useSnackbar } from "notistack";
import { JsonToNormalDate, ToMinutesSeconds } from "../../../../utils/utility";
import dayjs from "dayjs";
import { connect } from "react-redux";
import { setRefreshAgentStats } from "../../../../store/actions/SalesView/SalesView";
import DataTable from '../../Main/actions/DataTableWithFilter';
import { SelectDropdown } from "../../../../components";

const styles = (theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(2),

    },
    closeButton: {
        position: "absolute",
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
});

const DialogTitle = withStyles(styles)((props) => {
    const { children, classes, onClose, ...other } = props;

    return (
        <MuiDialogTitle disableTypography className={classes.root} {...other}>
            <Typography variant="h6">{children}</Typography>
            {onClose ? (
                <IconButton
                    aria-label="close"
                    className={classes.closeButton}
                    onClick={onClose}
                    size="large">
                    <CloseIcon />
                </IconButton>
            ) : null}
        </MuiDialogTitle>
    );
});

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="down" ref={ref} {...props} />;
});

function AddMoreLeads(props) {
    let [rows, setRows] = useState([]);
    const { show, handleClose, LstAgentCallableLeads } = props;
    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('xl'));
    const { enqueueSnackbar } = useSnackbar();
    const totalDialRanges = ['All','0','1-2','3-5','6-10','11-15','16-20','21&Abv'];
    const talkTimeRanges = [{id:'All', name:'All'}, {id:'0-2', name:'0-2 Min'}, {id:'2-5', name:'2-5 Min'}, {id:'5-8', name:'5-8 Min'}
    , {id:'8-12', name:'8-12 Min'}, {id:'12-20', name:'12-20 Min'}, {id:'20-30', name:'20-30 Min'}, {id:'30-45', name:'30-45 Min'}
    , {id:'45&abv', name:'45 Min abv'}];
    const subStatusValues = ['All','Call me Later','Corporate Policy','Financial Issue - Budget Constraint',
    'Financial Issue - Need No Cost EMI','Financial Issue - Premium Too High','Not Responding','None',
'Payment Confirmation Awaited','Payment not received','Ringing','Unhappy with Insurer - Claims/Service Issue',
'Unhappy with Insurer - Health Checkup Issue','Unhappy with PB - Miscommitment',
'Will Renew','Will Renew in Grace'];

    const FilterDropdown = [{id:'TT', name:'Talk Time'},{id:'TD', name:'Total Dial'},{id:'SS', name:'Sub Status'}];

    let [ TDKey, setTDKey] = useState('All');
    let [ TTKey, setTTKey] = useState('All');
    let [ SSKey, setSSKey] = useState('All');
    let [ Filter, setFilter] = useState('Select Filter');
    let [ ShowTT, setShowTT] = useState(false);
    let [ ShowTD, setShowTD] = useState(false);
    let [ ShowSS, setShowSS] = useState(false);

    const columns = [
        {
            name: "Add Lead",
            sortable: true,
            cell: row => <input
            type="checkbox"
            name={row.LeadID}
            value={row.IsChecked || false}
            checked={row.IsChecked || false}
            onChange={(event) => { handleChange(event, row.LeadID) }}
            />,
            width: "75px"
            }, 
        {
          name: "Lead ID",
          selector: row=>row.LeadID,
          sortable: true,
        },
        {
          name: "Customer Name",
          selector: row=>row.CustName,
          sortable: true,
        },
        {
          name: "Country",
          cell: row => <div>{row.CountryName ? row.CountryName : "N.A"}</div>,
          sortable: true,
        },
        {
          name: "Expiry Date",
          selector: row=>row.PrevPolicyExpDate,
          sortable: true,
          cell: row => <div>{row.PrevPolicyExpDate &&(row.PrevPolicyExpDate!=='0001-01-01T05:53:00+05:53') ? dayjs(new Date(row.PrevPolicyExpDate)).format('DD/MM/YYYY h:mm a') : "N.A"}</div>
  
        },
        {
          name: "Created on",
          selector: row=>row.LeadCreatedOn,
          sortable: true,
          cell: row => <div>{row.LeadCreatedOn ? dayjs(new Date(row.LeadCreatedOn)).format('DD/MM/YYYY h:mm a') : "N.A"}</div>
        },  
        {
          name: "Revisit on",
          selector: row=>row.Revisit_ts,
          sortable: true,
          cell: row => <div>{row.Revisit && row.Revisit.ts ? dayjs(new Date(row.Revisit.ts)).format('DD/MM/YYYY h:mm a') : "N.A"}</div>
        },
        {
        name: "Talk Time",
        selector: row=>row.Call_TotalTT,
        sortable: true,
        cell: row => <div>{row.Call !== undefined && row.Call.TotalTT ? ToMinutesSeconds(row.Call.TotalTT) : "N.A"}</div>
        },
        {
        name: "Callback",
        selector: row=>row.CallBack_CBtime,
        sortable: true,
        cell: row => <div>{row.CallBack !== undefined && row.CallBack.CBtime ? dayjs(new Date(row.CallBack.CBtime)).format('DD/MM/YYYY h:mm a') : "N.A"}</div>
        },
        {
        name: "Last Called on",
        selector: row=>row.Call_calltime,
        sortable: true,
        cell: row => <div>{row.Call !== undefined && row.Call.calltime ? dayjs(new Date(row.Call.calltime)).format('DD/MM/YYYY h:mm a') : "N.A"}</div>
        },  
        {
        name: "Total Dials",
        selector: row=>row.Call_CallAttempts,    
        sortable: true,
        cell: row => <div>{row.Call !== undefined && row.Call.CallAttempts ? row.Call.CallAttempts : "N.A"}</div>
        },
        {
        name: "Lead Status",
        selector: row=>row.LeadStatus_StatusID,
        sortable: true,
        cell: row => <div>{row.LeadStatus !== undefined && row.LeadStatus.StatusID ? GetStatus(row.LeadStatus.StatusID) : "N.A"}</div>
        },   
        {
        name: "Lead Sub Status",
        selector: row=>row.LeadStatus_SubStatusName,
        sortable: true,
        cell: row => <div>{row.LeadStatus !== undefined && row.LeadStatus.SubStatusName ? row.LeadStatus.SubStatusName : "N.A"}</div>
        }                 
                      
      ];

    const handleChange = (event, LeadId) => {

        let checked = event.target.checked;
        setRows(
            rows.map((row, Index) => {
                if (row.LeadID === LeadId) {
                    row.IsChecked = checked;
                }
                return row;
            }));


        // setCheckedItems({
        //     ...checkedItems,
        //     [event.target.name]: event.target.checked
        // });
    };

    //Clear all checkbox
    const clearAll = () => {
        setRows(
            prevState => prevState.map((row) => ({ ...row, IsChecked: false }))
        );
    };

    useEffect(() => {
        setRows(Array.isArray(props.LstAgentCallableLeads) ? props.LstAgentCallableLeads : []);
    }, [props.LstAgentCallableLeads]);

    useEffect(() => {
        if (rows.length > 0 && !rows[0].hasOwnProperty('IsChecked')) {

            rows.map((row, Index) => {
                setRows(prevState =>
                    [...prevState.slice(0, Index),
                    { ...prevState[Index], IsChecked: false },
                    ...prevState.slice(Index + 1)]
                )
            });

        }


    }, [rows]);

    const AddLeadsToQueue = () => {
        let checkTimeZone = [];
        let IscheckedLeads = rows.filter((lead) => lead.IsChecked);
        if (IscheckedLeads.length > 0) {
            setTimeout(function () {
                rows.forEach((lead, key) => {
                    if (lead.IsChecked)
                        checkTimeZone.push(lead.LeadID)
                });


                if (checkTimeZone != null && checkTimeZone.length > 0) {
                    checkTimeZoneService(checkTimeZone).then(function (resultData) {
                        if (resultData != null && resultData.data != '') {

                            IscheckedLeads.forEach((items) => {
                                //let items = rows.filter((lead) => lead.IsChecked);
                                if (resultData.indexOf(items.LeadID) > -1) {
                                    if (items) {
                                        var lead = {
                                            "LeadId": items.LeadID,
                                            "Name": items.CustName,
                                            "CustomerId": items.CustID,
                                            "UserID": User.UserId,
                                            "Priority": 0,
                                            "ProductId": items.ProductID,
                                            "Reason": 'Manual added',
                                            "ReasonId": items.IsNBTSorted ? 80 : 33,
                                            "CallStatus": "",
                                            "IsAddLeadtoQueue": 1,
                                            "IsNeedToValidate":1
                                        }
                                        AddLeadToPriorityQueueService(lead).then((resultData) => {
                                            if (resultData != null) {
                                                if (resultData && resultData.message && resultData.message == "Success") {
                                                    enqueueSnackbar("Lead " + resultData.LeadID + " Added successfully", {
                                                        variant: 'success',
                                                        autoHideDuration: 3000,
                                                    });
                                                }
                                                else {
                                                    let error = (resultData && resultData.message && resultData.message !== '')
                                                        ? (resultData.LeadID + " : " + resultData.message)
                                                        : "Error while adding lead: " + resultData.LeadID;
                                                    enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                                                }
                                            }
                                        });
                                        // var reqData = {
                                        //     "UserId": User.UserId,
                                        //     "Leads": [lead]
                                        // };

                                        // const input = {
                                        //     url: `onelead/api/LeadPrioritization/ValidateAddLeadToPriorityQueue`,
                                        //     method: 'POST', service: 'MatrixCore',
                                        //     requestData: reqData
                                        // };
                                        // CALL_API(input);

                                        //props.handleClose();
                                        // var PredictiveDialLeads = localStorage.getItem('PredictiveDialLeads');
                                        // if (PredictiveDialLeads != null) {
                                        //     PredictiveDialLeads = JSON.parse(PredictiveDialLeads);
                                        //     PredictiveDialLeads.CallStatus = "";
                                        //     localStorage.setItem('PredictiveDialLeads', JSON.stringify(PredictiveDialLeads));
                                        // }
                                    }

                                }
                                else {
                                    enqueueSnackbar("Sorry lead id " + items.LeadID + " has incompatible time zone time.", { variant: 'error', autoHideDuration: 3000, });
                                }
                            });
                            props.setRefreshAgentStatsToRedux(true);
                            // window.sessionStorage.setItem("reloadNext5Lead", true);
                            props.handleClose();
                        }
                    });
                }
            }, 200);
        } else {
            enqueueSnackbar("Please Select Leads.", { variant: 'error', autoHideDuration: 3000, });
        }
    };

    const GetStatus = (x) => {
        if (x == '1') return 'New';
        else if (x == '2') return 'Valid';
        else if (x == '3') return 'Contacted';
        else if (x == '4') return 'Interested';
        else if (x == '11') return 'Prospect';
        else return '';
    }
    const handleChangeTDRange = (e) => {//;
        setTDKey(e.target.value);
    }

    const handleChangeTTRange = (e) => {
        setTTKey(e.target.value);
    }

    const handleChangeSSValue = (e) => {
            setSSKey(e.target.value);
        }

    const handleChangeFilter = (e) => {;
        setFilter(e.target.value);
        if(e.target.value == "TD"){
            setShowTD(true);
            setShowTT(false);
            setShowSS(false);

        }
        if(e.target.value == "TT"){
            setShowTD(false);
            setShowTT(true);
            setShowSS(false);

        }
        if(e.target.value == "SS"){
            setShowSS(true);
            setShowTT(false);
            setShowTD(false);

        }
       
    }

    const filterdata = (rows) => {
        let filterarray = [];
        let i = 0;
        rows.forEach(element => {
            filterarray[i] = element;
            filterarray[i]['Call_CallAttempts'] = (element.Call&&element.Call.CallAttempts)?element.Call.CallAttempts:'';
            filterarray[i]['Call_TotalTT'] = (element.Call&&element.Call.TotalTT)?element.Call.TotalTT:'';
            filterarray[i]['Call_calltime'] = (element.Call&&element.Call.calltime)?element.Call.calltime:'';
            filterarray[i]['Revisit_ts'] = (element.Revisit && element.Revisit.ts)?element.Revisit.ts:'';
            filterarray[i]['CallBack_CBtime'] = (element.CallBack&&element.CallBack.CBtime)?element.CallBack.CBtime:'';
            filterarray[i]['LeadStatus_StatusID'] = (element.LeadStatus&&element.LeadStatus.StatusID)?element.LeadStatus.StatusID:'';
            filterarray[i]['LeadStatus_SubStatusName'] = (element.LeadStatus&&element.LeadStatus.SubStatusName)?element.LeadStatus.SubStatusName:'';

            // let filteredVal = (Filter == "TD")?(element.Call&&element.Call.CallAttempts)?element.Call.CallAttempts:0:
            // (Filter == "TT")?(element.Call&&element.Call.TotalTT)?Math.floor(element.Call.TotalTT / 60):0:0;

            // let comp1 = Number(FilterArray[0]);
            // let comp2 =    (Filter == "TD")?Number(FilterArray[1]):Number(FilterArray[1])-1;     
            //   if (filteredVal >= comp1 && filteredVal <= comp2) {
            //     AgentData.push(element);
            //   }
            // console.log(filterarray);
            ++i;
            });
            // console.log(filterarray);
        let alldata = filterarray
        let that = this;
        if( Filter == 'Select Filter'){
            return alldata;
        }else if (Filter == 'SS'){
            return filterSS(alldata);

        }else{
            return filterTD(alldata);
        }
       
      }

      const filterTD = (alldata) => {
        let key = '';
        if(Filter == 'TD'){
            key = TDKey;
        }else if(Filter == 'TT'){
            key = TTKey;
        }
        if (key === "All") {
            return alldata;
          }
          let AgentData = [];
          if(key === "0"){
              alldata.forEach(element => {
            if (element.Call && element.Call.CallAttempts == 0) {
              AgentData.push(element);
            }
          });
          }
  
          if (key.indexOf('-') > -1)
          {
              const FilterArray = key.split("-");
              alldata.forEach(element => {
                let filteredVal = (Filter == "TD")?(element.Call&&element.Call.CallAttempts)?element.Call.CallAttempts:0:
                (Filter == "TT")?(element.Call&&element.Call.TotalTT)?Math.floor(element.Call.TotalTT / 60):0:0;

                let comp1 = Number(FilterArray[0]);
                let comp2 =    (Filter == "TD")?Number(FilterArray[1]):Number(FilterArray[1])-1;     
                  if (filteredVal >= comp1 && filteredVal <= comp2) {
                    AgentData.push(element);
                  }
                });
          }
          if (key.indexOf('&') > -1)
          {
              const FilterArray = key.split("&");

              alldata.forEach(element => {
                let filteredVal = (Filter == "TD")?(element.Call&&element.Call.CallAttempts)?element.Call.CallAttempts:0:
                (Filter == "TT")?(element.Call&&element.Call.TotalTT)?Math.floor(element.Call.TotalTT / 60):0:0;
             
                  if (filteredVal >= Number(FilterArray[0]) ) {
                    AgentData.push(element);
                  }
                });
          }
          return AgentData;
      }

      const filterSS = (alldata) => {
        if (SSKey === "All") {
            return alldata;
          }
          let AgentData = [];
  
                alldata.forEach(element => {
                let filteredVal = (element.LeadStatus&&element.LeadStatus.SubStatusName)?element.LeadStatus.SubStatusName:'None';
                  if (filteredVal === SSKey ) {
                    AgentData.push(element);
                  }
                });
          
          return AgentData;
      }

    const filteredData = filterdata(rows);

    return (
        <div>

            <Dialog
                fullScreen={fullScreen}
                TransitionComponent={Transition}
                open={show}
                onClose={handleClose}
                aria-labelledby="responsive-dialog-title"
                className="addmoreLeadPopup scrollFx"
            >

                <DialogTitle id="responsive-dialog-title" onClose={handleClose}>
                    {" Add More Leads"}
                </DialogTitle>
                {props.isLoading && <LinearProgress color="secondary" />}

                {rows.length === 0 && !props.isLoading? <h3 className="nodataFound">  No Data Found</h3> :
                    <DialogContent>
                        <Grid container spacing={2}>   
                            <SelectDropdown
                                    name="SelectFilter"
                                    label="Select Filter"
                                    value={Filter}
                                    options={FilterDropdown}
                                    labelKeyInOptions="name"
                                    valueKeyInOptions='id'
                                    handleChange={handleChangeFilter}
                                    sm={6} md={4} xs={12}
                                    show={true}
                                    shrink={{ shrink: true}}
                                    //disabled={isDisabled}
                                />                    
                            <SelectDropdown
                                    name="TotalDials"
                                    label="Total Dials"
                                    value={TDKey}
                                    options={totalDialRanges}
                                    labelKeyInOptions="_all"
                                    valueKeyInOptions='_all'
                                    handleChange={handleChangeTDRange}
                                    sm={6} md={4} xs={12}
                                    show={ShowTD}
                                    //disabled={isDisabled}
                                />                          
                              
                                <SelectDropdown
                                    name="TalkTime"
                                    label="Talk Time"
                                    value={TTKey}
                                    options={talkTimeRanges}
                                    labelKeyInOptions="name"
                                    valueKeyInOptions='id'
                                    handleChange={handleChangeTTRange}
                                    sm={6} md={4} xs={12}
                                    show={ShowTT}
                                    //disabled={isDisabled}
                                />
                                <SelectDropdown
                                    name="Substatus"
                                    label="Sub Statuses"
                                    value={SSKey}
                                    options={subStatusValues}
                                    labelKeyInOptions="_all"
                                    valueKeyInOptions='_all'
                                    handleChange={handleChangeSSValue}
                                    sm={6} md={4} xs={12}
                                    show={ShowSS}
                                    //disabled={isDisabled}
                                />
                                
                               
                                </Grid>
                                <div></div>
                                
                              
                                {filteredData.some(row => row.IsNBTSorted === true) && (
                                    <div className="tip-container">
                                        <div className="tip-accent-bar"></div>
                                        <div className="tip-icon">
                                            <TipsAndUpdatesIcon />
                                        </div>
                                        <div>
                                            <Typography variant="body1" className="tip-text">
                                                <span className="tip-label">Tip:</span> Leads highlighted in yellow have a <em className="tip-emphasis">higher chance</em> of picking up your call right now
                                            </Typography>
                                        </div>
                                       
                                    </div>
                                )}
                                
                                <DataTable
                                    columns={columns}
                                    data={filteredData}
                                    //defaultSortField="USERNAME"
                                    defaultSortAsc={true}
                                    conditionalRowStyles={[
                                        {
                                            when: row => row.IsNBTSorted === true,
                                            style: {
                                                background: 'linear-gradient(90deg, rgb(255 249 180) 0%, rgb(251 245 188) 49%, rgba(255, 255, 255, 1) 100%)',
                                                '&:hover': {
                                                    background: 'linear-gradient(90deg, rgb(255 249 180) 0%, rgb(251 245 188) 49%, rgba(255, 255, 255, 1) 100%)',
                                                }
                                            },
                                        }
                                    ]}
                                />
                                   

                        <div className="text-center">
                            <button onClick={clearAll} className="clearAllBtn">Clear All</button>
                            <button onClick={() => { AddLeadsToQueue() }} className="addLeadBtn">Add Leads</button>
                        </div>

                    </DialogContent>
                }
            </Dialog>

        </div>
    );
}
const mapDispatchToProps = dispatch => {

    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
    };
};

export default connect(() => ({}), mapDispatchToProps)(AddMoreLeads);