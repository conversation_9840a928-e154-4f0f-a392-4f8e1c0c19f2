import React, { useState } from "react";
import ModalPopup from "../../../../../components/Dialogs/ModalPopup";
import { Button, TextField, Box, Typography } from "@mui/material";
import { useSnackbar } from "notistack";
import { CALL_API } from "../../../../../services";
import { useQuery } from "../../../../../hooks/useQuery";
import { SendEmergencyContactConsent, VerifyEmergencyContactConsent } from "../../../../../services/Common";
import User from "../../../../../services/user.service";

export const EmergencyNumberPopup = ({ 
    open, 
    handleClose, 
    mobileNumber, 
    countryId, 
    leadId, 
    customerId,
    custMobId,
    onSuccess 
}) => {
    const [otp, setOtp] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isTriggerSent, setIsTriggerSent] = useState(false);
    const { enqueueSnackbar } = useSnackbar();
    const query = useQuery();
    let src = query.get('src') || "matrix";
    try { src = src.toLowerCase(); }
    catch { }

    const handleOtpChange = (event) => {
        const value = event.target.value;
        // Only allow numbers and max 6 digits
        if (/^[0-9]*$/.test(value) && value.length <= 6) {
            setOtp(value);
        }
    };

    const sendWhatsAppTrigger = async () => {
        setIsLoading(true);
        try {
            const response = await SendEmergencyContactConsent({
                    LeadId: leadId,
                    MobileNo: mobileNumber,
                    CustId: customerId
            });
            
            if (response && response.status) {
                setIsTriggerSent(true);
                enqueueSnackbar("Trigger sent successfully!", {
                    variant: "success",
                    autoHideDuration: 3000,
                });
            } else {
                enqueueSnackbar(response?.message || "Failed to send trigger", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            }
        } catch (error) {
            console.error("Error sending trigger:", error);
            enqueueSnackbar("Failed to send trigger", {
                variant: "error",
                autoHideDuration: 3000,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleSave = async () => {
        if (!otp || otp.length !== 6) {
            enqueueSnackbar("Please enter a valid 6-digit OTP", {
                variant: "error",
                autoHideDuration: 3000,
            });
            return;
        }

        setIsLoading(true);
        try {
            const response = await VerifyEmergencyContactConsent({
                LeadId: leadId,
                CustomerId: customerId,
                MobileNo: mobileNumber,
                UserId: User.UserId,
                Source: src,
                Otp: parseInt(otp),
                CountryId: parseInt(countryId),
                CustMobId: custMobId
            })
            
            if (response && response.status) {
                enqueueSnackbar("Number successfully moved from emergency to secondary!", {
                    variant: "success",
                    autoHideDuration: 3000,
                });
                onSuccess && onSuccess();
                handleClose();
            } else {
                enqueueSnackbar(response?.message || "Failed to process request", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            }
        } catch (error) {
            console.error("Error processing request:", error);
            enqueueSnackbar("Failed to process request", {
                variant: "error",
                autoHideDuration: 3000,
            });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <ModalPopup
            open={open}
            handleClose={handleClose}
            title=""
            className="emergencyNumberPopup"
            showCloseButton={true}
        >
            <div className="emergency-number-popup-content">
                {/* Custom Header with Icon */}
                <Box className="popup-header">
                    <Typography variant="h5" className="popup-title">
                        Emergency Number Detected
                    </Typography>
                </Box>

                {/* Message Section */}
                <Box className="message-section">
                    <Typography variant="body1" className="message-text">
                        Number <strong>{mobileNumber}</strong> is already added as an emergency contact.
                    </Typography>
                    <Typography variant="body2" className="instruction-text">
                        Please verify with OTP to add it as a secondary number.
                    </Typography>
                </Box>

                {/* Action Section */}
                <Box className="action-section">
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={sendWhatsAppTrigger}
                        disabled={isLoading || isTriggerSent}
                        className="send-trigger-btn"
                        size="large"
                    >
                        {isLoading ? "Sending..." : "Send Verification Code"}
                    </Button>
                </Box>

                {/* OTP Section */}
                {isTriggerSent && (
                    <Box className="otp-section">
                        <Typography variant="body2" className="otp-instruction">
                            Enter the 6-digit OTP received by the customer:
                        </Typography>
                        <TextField
                            label="Enter OTP"
                            variant="outlined"
                            value={otp}
                            onChange={handleOtpChange}
                            inputProps={{
                                maxLength: 6,
                                pattern: "[0-9]*",
                                style: { textAlign: 'center', fontSize: '18px', letterSpacing: '3px' }
                            }}
                            placeholder="000000"
                            fullWidth
                            className="otp-input"
                        />
                        
                        <Button
                            variant="contained"
                            color="success"
                            onClick={handleSave}
                            disabled={isLoading || !otp || otp.length !== 6}
                            className="save-btn"
                            size="large"
                            fullWidth
                        >
                            {isLoading ? "Processing..." : "Verify"}
                        </Button>
                    </Box>
                )}
            </div>
        </ModalPopup>
    );
}; 