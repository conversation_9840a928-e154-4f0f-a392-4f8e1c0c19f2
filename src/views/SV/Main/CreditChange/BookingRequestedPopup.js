import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import { useSnackbar } from 'notistack';
import DialogTitle from '@mui/material/DialogTitle';
import { Grid, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { TextInput } from '../../../../components';
import Textarea from '@mui/material/TextareaAutosize';
import { OpenReadOnlyViewMatrix,OpenLeadContentOnClick } from "../../../../services/Common";
import BookingDetails from './BookingDetails';
import BookingHistory from './BookingHistory';
import CircleIcon from '@mui/icons-material/Circle';
import User from '../../../../../src/services/user.service';
import { SetCreditChangeRequest, GetCreditChangeActivityLogs, CreateCreditChangeRequest } from '../../../../../src/layouts/SV/components/Sidebar/helper/sidebarHelper';
import HistoryToggleOffOutlinedIcon from '@mui/icons-material/HistoryToggleOffOutlined';

const determineApprovalLevel = (rowData, isAuthorisedCreator, statusID) => {
    // First level: User is the first level approver and status is 1 (Created)
    if (rowData.FirstLevelApproverUserID == User.UserId && statusID == 1) {
        return 'first';
    }
    // Second level: Authorized creator level 2 and status is 3 (First level approved)
    else if (isAuthorisedCreator == 2 && statusID == 3) {
        return 'second';
    }
    // BU level: Authorized creator level 4 and status is 7 (Second level approved)
    else if (isAuthorisedCreator == 4 && statusID == 7) {
        return 'bu';
    }
    // No approval access
    return null;
};

export default function BookingRequestedPopup(props) {

    const { open, onClose, bookingdata, allData, IsAuthorisedCreator, ProductSelect } = props;
    const { enqueueSnackbar } = useSnackbar();
    const [BookingData, setBookingData] = React.useState([]);
    const [RowWiseData, setRowWiseData] = React.useState([]);
    const [ApprovalLevel, setApprovalLevel] = React.useState(null); // 'first', 'second', 'bu', or null
    const [CurrentStatusID, setCurrentStatusID] = React.useState(0);
    const [Comments, setComments] = React.useState('');
    let [NewRequestData, setNewRequestData] = React.useState({
        RequestID: "",
        BookingID: "",
        NewAgentID: "",
        AgentTypeID: "",
        ReasonID: "",
        RequestorRemarks: "",
        ReferenceId: ""
    });
    const [isValidatedRequest, setisValidatedRequest] = React.useState(false);
    const [ActivityLogs, setActivityLogs] = React.useState([]);
    const [BookingHistoryShow, setBookingHistoryShow] = React.useState(false);
    const [IsApproveButtonClick, setIsApproveButtonClick] = React.useState(false);
    const [IsValidReRequest, setIsValidReRequest] = React.useState(false);
    const [FirstLevelReRaiseCount, setFirstLevelReRaiseCount] = React.useState(0);
    const [SecondLevelReRequestCount, setSecondLevelReRequestCount] = React.useState(0);

    React.useEffect(() => {
        if (bookingdata && Array.isArray(bookingdata) && bookingdata.length > 0) {
            setBookingData(bookingdata);
        }
        if (allData && Array.isArray(allData) && allData.length > 0) {
            setRowWiseData(allData);
            let RequestID = allData[0].RequestID;
            let BookingID = allData[0].BookingId;
            let AgentTypeID = allData[0].AgentTypeID;
            let StatusID = allData[0].StatusID;

            let CurrentRequestData = NewRequestData;
            CurrentRequestData.RequestID = RequestID;
            CurrentRequestData.BookingID = BookingID;
            CurrentRequestData.NewAgentID = allData[0].NewAdvisorUserID;
            CurrentRequestData.AgentTypeID = AgentTypeID;
            CurrentRequestData.ReasonID = allData[0].ReasonID;
            setNewRequestData(CurrentRequestData);

            setCurrentStatusID(StatusID);

            // Determine approval level based on user authorization and status
            const approvalLevel = determineApprovalLevel(allData[0], IsAuthorisedCreator, StatusID);
            setApprovalLevel(approvalLevel);

            GetCreditChangeActivityLogs(BookingID, AgentTypeID, RequestID).then((result) => {
                if (result && Array.isArray(result) && result.length > 0) {
                    setActivityLogs(result);
                }
            });

            // Compute re-request counts solely from current row's RequestorComment
            try {
                const countOccurrences = (text, needle) => {
                    if (typeof text !== 'string' || !text) return 0;
                    const escaped = needle.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(escaped, 'g');
                    const matches = text.match(regex);
                    return matches ? matches.length : 0;
                };
                const reqComment = (allData[0] && allData[0].RequestorComment) || '';
                const firstCount = countOccurrences(reqComment, 'Re-Raised Request');
                const secondCount = countOccurrences(reqComment, 'Re-Requested');
                setFirstLevelReRaiseCount(firstCount);
                setSecondLevelReRequestCount(secondCount);
            } catch (e) {
                setFirstLevelReRaiseCount(0);
                setSecondLevelReRequestCount(0);
            }

            //re-Raise TAT
            let ThresholdDays = 10;
            let givenDate = '';
            if(bookingdata && Array.isArray(bookingdata) && bookingdata.length > 0 && bookingdata[0].OfferCreatedON){
                givenDate = new Date(bookingdata[0].OfferCreatedON);
            }
            else{
                setIsValidReRequest(true);
            }

            if (!isNaN(givenDate.getTime())) {
                const currentDate = new Date();
                const diffInMs = currentDate - givenDate;
                
                const diffInDays = diffInMs / (1000 * 60 * 60 * 24); // milliseconds to days
                setIsValidReRequest(diffInDays > ThresholdDays);
            }
            
        }
        ValidateSubmitRequest();
    }, [props]);

    const handleChange = (event) => {
        const value = event.target.value;
        const name = event.target.name;
        switch (name) {
            case 'FirstLevelRemarks':
            case 'SecondLevelRemarks':
                setComments(value);
                setNewRequestData({ ...NewRequestData, RequestorRemarks: value });
                break;
            default:
                break;
        }
        ValidateSubmitRequest();
    }
    const ValidateSubmitRequest = () => {
        // For BU level, no remarks validation is required
        if (ApprovalLevel === 'bu') {
            const isValid = NewRequestData.RequestID !== '' && NewRequestData.BookingID !== 0 && NewRequestData.NewAgentID !== 0 && NewRequestData.AgentTypeID !== '' && NewRequestData.ReasonID !== 0;
            setisValidatedRequest(isValid);
        } else {
            // For first and second level, remarks are required
            const isValid = NewRequestData.RequestID !== '' && NewRequestData.BookingID !== 0 && NewRequestData.NewAgentID !== 0 && NewRequestData.AgentTypeID !== '' && NewRequestData.ReasonID !== 0 && Comments !== '' && Comments.trim().length >= 4 && Comments.trim().length <= 400;
            setNewRequestData({ ...NewRequestData, RequestorRemarks: Comments.trim() });
            setisValidatedRequest(isValid);
        }
    }

    const SubmitRequest = (Action) => {
        setIsApproveButtonClick(true);
        if (isValidatedRequest) {
            NewRequestData = { ...NewRequestData, Action: Action, CurrentStatus: CurrentStatusID };
            var Data = NewRequestData;
            SetCreditChangeRequest(Data).then((res) => {
                if (res && res.status == true) {
                    enqueueSnackbar(res.message, { variant: 'success', autoHideDuration: 2000, });
                    onClose();
                }
                else if (res && res.status != true) {
                    enqueueSnackbar(res.message, { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
                else {
                    enqueueSnackbar("Issue updating Credit Change Request, please try again", { variant: 'error', autoHideDuration: 2000, });
                    onClose();
                }
            }).catch((error) => {
                console.log("error in SaveCoreAddressUsageService");
                enqueueSnackbar("There is some error occured while creating thr request", { variant: 'error', autoHideDuration: 2000, });
                onClose();
            })
        }
    }
    const getStatusClassName = (statusID) => {
        if (statusID === 1) {
            return "created";
        } else if (statusID === 3 || statusID === 4 || statusID === 7) {
            return "approved";
        } else if (statusID === 2 || statusID === 5 || statusID === 8) {
            return "rejected";
        } else {
            return "created"; // Default class name if no other condition is met
        }
    };
    const getStatusActionName = (statusID) => {
        if (statusID === 1) {
            return "Created";
        } else if (statusID === 4) {
            return "Approved";
        } else if (statusID === 7) {
            return "Approved at First Level";
        } else if (statusID === 2) {
            return "Rejected at First Level";
        } else if (statusID === 5) {
            return "Rejected at MIS Team";
        } else if (statusID === 8) {
            return "Rejected at BU Team";
        } else if (statusID == 3 && [98,100,102,114,116,118,130,132,134].includes(NewRequestData.ReasonID)){
            return "Approved at BU Team"
        } else if (statusID === 3) {
            return "Approved at First Level";
        } else {
            return "Created"; // Default status name if no other condition is met
        }
    };

    const handleBookingHistoryShowOpen = () => {
        setBookingHistoryShow(true);
    }
    const handleCreditChangeRequestClose = () => {
        setBookingHistoryShow(false);
    }

    const UpdateAgentType = (AgentTypeID) => {
        if (AgentTypeID == 1)
            return "Primary - Call Center";
        else if (AgentTypeID == 2)
            return "Secondary - Call Center";
        else if (AgentTypeID == 3)
            return "Primary - FOS";
    }

    const ReRaiseRequest = () => {
        const MAX_REREQUESTS = 2;
        if (CurrentStatusID === 2 && FirstLevelReRaiseCount >= MAX_REREQUESTS) {
            enqueueSnackbar("You have reached the maximum number of re-raise attempts (2) for first-level declinations.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        if (CurrentStatusID === 5 && SecondLevelReRequestCount >= MAX_REREQUESTS) {
            enqueueSnackbar("You have reached the maximum number of re-request attempts (2) for second-level declinations.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        if(bookingdata[0].BookingAgentUserID == RowWiseData[0].NewAdvisorUserID && [1,3].includes(RowWiseData[0].AgentTypeID)){
            enqueueSnackbar("Booking is already mapped to this advisor.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        else if(bookingdata[0].SecondaryAgentUserID == RowWiseData[0].NewAdvisorUserID && [2].includes(RowWiseData[0].AgentTypeID)){
            enqueueSnackbar("Secondary booking is already mapped to this advisor.", { variant: 'error', autoHideDuration: 2000, });
            return;
        }
        const remarksPrefix = CurrentStatusID === 5 || CurrentStatusID === 8 ? "Re-Requested : " : "Re-Raised Request : ";
        let reRaiseRequest = {
            BookingID : RowWiseData[0].BookingId,
            AgentTypeID : RowWiseData[0].AgentTypeID,
            NewAgentID : RowWiseData[0].NewAdvisorUserID,
            ReasonID: RowWiseData[0].ReasonID,
            RequestorRemarks: remarksPrefix + RowWiseData[0].RequestorComment,
            ReferenceId : RowWiseData[0].ReferenceId > 0 ? RowWiseData[0].ReferenceId : 0
        }
        CreateCreditChangeRequest(reRaiseRequest).then((res) => {
            if (res && res.status == true) {
                enqueueSnackbar(res.message, { variant: 'success', autoHideDuration: 2000, });
                onClose();
            }
            else if (res && res.status != true) {
                enqueueSnackbar(res.message, { variant: 'error', autoHideDuration: 2000, });
                onClose();
            }
            else {
                enqueueSnackbar("Issue updating Credit Change Request, please try again", { variant: 'error', autoHideDuration: 2000, });
                onClose();
            }
        }).catch((error) => {
            console.log("error in SaveCoreAddressUsageService");
            enqueueSnackbar("There is some error occured while creating thr request", { variant: 'error', autoHideDuration: 2000, });
            onClose();
        })
    }

    // Render approval actions based on approval level
    const renderApprovalActions = () => {
        if (ApprovalLevel === 'bu') {
            return (
                <>
                    <p>BU Level approval - No remarks required</p>
                    <div>
                        <Button 
                            className={isValidatedRequest && !IsApproveButtonClick ? "CreateBtn decLineBtn" : "CreateBtn DisabledecLineBtn"} 
                            onClick={() => { SubmitRequest(2) }}
                            disabled={!isValidatedRequest}>
                            Decline
                        </Button>
                        <Button 
                            className={isValidatedRequest && !IsApproveButtonClick ? "CreateBtn approvedBtn" : "CreateBtn DisableapprovedBtn"} 
                            onClick={() => { SubmitRequest(1) }}
                            disabled={!isValidatedRequest}>
                            Approve
                        </Button>
                    </div>
                </>
            );
        } else if (ApprovalLevel === 'first' || ApprovalLevel === 'second') {
            const remarksValid = Comments && Comments.trim().length > 3 && Comments.trim().length < 400;
            const canApprove = isValidatedRequest && remarksValid;
            
            return (
                <>
                    {!canApprove ? <p>Ticket action disabled, add approver Remarks first</p> : <p></p>}
                    <div>
                        <Button 
                            className={canApprove && !IsApproveButtonClick ? "CreateBtn decLineBtn" : "CreateBtn DisabledecLineBtn"} 
                            onClick={() => { SubmitRequest(2) }}
                            disabled={!canApprove}>
                            Decline
                        </Button>
                        <Button 
                            className={canApprove && !IsApproveButtonClick ? "CreateBtn approvedBtn" : "CreateBtn DisableapprovedBtn"} 
                            onClick={() => { SubmitRequest(1) }}
                            disabled={!canApprove}>
                            Approve
                        </Button>
                    </div>
                </>
            );
        }
        return null;
    };


    return (
        <>
            
            {BookingData && Array.isArray(BookingData) && BookingData.length > 0
                && RowWiseData && Array.isArray(RowWiseData) && RowWiseData.length > 0 &&
                <>
                    <Dialog
                        open={open}
                        onClose={onClose}
                        className="CreateCreditChangeRequestPopup"
                    >
                        <DialogTitle>BookingID  - <span>{RowWiseData[0].BookingId}</span>
                            {/* <Button onClick={() => {handleBookingHistoryShowOpen()}} className="BookingHistoryBtn"><HistoryToggleOffOutlinedIcon /> Booking History</Button></DialogTitle> */}
                            <Button onClick={() => { handleBookingHistoryShowOpen() }} className="BookingHistoryBtn"><HistoryToggleOffOutlinedIcon /> View History</Button></DialogTitle>

                        <IconButton
                            aria-label="close"
                            onClick={onClose}
                            sx={{
                                position: 'absolute',
                                right: 8,
                                top: 8,
                                color: (theme) => theme.palette.grey[500],
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                        <DialogContent>
                            <Grid container spacing={2}>
                                <Grid item sm={5} md={5} xs={12} >
                                <label>Booking ID</label>
                                    <div className="SeperateLine">                                        
                                        {IsAuthorisedCreator == 2 ? 
                                            <a onClick={() => { OpenReadOnlyViewMatrix(RowWiseData[0].BookingId, RowWiseData[0].CustomerID, RowWiseData[0].ProductId) }}>{RowWiseData[0].BookingId !== undefined ? RowWiseData[0].BookingId : ""}</a>
                                        :
                                            <a onClick={() => { OpenLeadContentOnClick(RowWiseData[0].BookingId, RowWiseData[0].CustomerID, RowWiseData[0].ProductId) }}>{RowWiseData[0].BookingId !== undefined ? RowWiseData[0].BookingId : ""}</a>
                                        }
                                    </div>
                                   
                                    <label>Reference ID (Optional)</label>
                                    <div className="SeperateLine">
                                    {RowWiseData[0].ReferenceId > 0 && RowWiseData[0].ReferenceCustId > 0 && RowWiseData[0].ReferenceProdId > 0 ?
                                        (IsAuthorisedCreator == 2 ? 
                                            <a onClick={() => { OpenReadOnlyViewMatrix(RowWiseData[0].ReferenceId, RowWiseData[0].ReferenceCustId, RowWiseData[0].ReferenceProdId) }}>{RowWiseData[0].ReferenceId !== undefined ? RowWiseData[0].ReferenceId : ""}</a>
                                        :
                                            <a onClick={() => { OpenLeadContentOnClick(RowWiseData[0].ReferenceId, RowWiseData[0].ReferenceCustId, RowWiseData[0].ReferenceProdId) }}>{RowWiseData[0].ReferenceId !== undefined ? RowWiseData[0].ReferenceId : ""}</a>
                                        )
                                        : "-"}
                                       </div> 
                                    <label>New advisor</label>
                                    <TextInput
                                        name="NewAdvisor"
                                        sm={12} md={12} xs={12}
                                        value={`${RowWiseData[0].NewAdvisorUserName} - ${RowWiseData[0].NewAdvisorEmpCode}`}
                                        disabled={true}
                                    />
                                    <Grid container spacing={2}>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>Agent Type</label>
                                            <TextInput
                                                name="AgentType"
                                                sm={12} md={12} xs={12}
                                                value={UpdateAgentType(RowWiseData[0].AgentTypeID)}
                                                disabled={true}
                                            />
                                        </Grid>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>Reason for change</label>
                                            <TextInput
                                                name="ReasonforChange"
                                                sm={12} md={12} xs={12}
                                                value={RowWiseData[0].Reason}
                                                disabled={true}
                                            />
                                        </Grid>
                                    </Grid>
                                    <label>Requestor Remarks</label>
                                    <Textarea
                                        name="RequestorRemarks"
                                        minRows={3}
                                        value={RowWiseData[0].RequestorComment}
                                        disabled={true}
                                    />
                                    <Grid container spacing={2}>
                                        <Grid item sm={6} md={6} xs={12} >
                                            <label>1st Approver Remarks</label>
                                            <Textarea
                                                name="FirstLevelRemarks"
                                                minRows={3}
                                                placeholder="Enter Remarks here(4-400 characters)…"
                                                onChange={handleChange}
                                                value={(ApprovalLevel === 'first' && CurrentStatusID == 1 && !(RowWiseData[0].FirstLevelComment && RowWiseData[0].FirstLevelComment.length > 0)) ? Comments : RowWiseData[0].FirstLevelComment}
                                                disabled={!(ApprovalLevel === 'first' && CurrentStatusID == 1)}
                                                className={!(ApprovalLevel === 'first' && CurrentStatusID === 1) ? "" : "highlight-textarea"}
                                            />
                                        </Grid>
                                        <Grid item sm={6} md={6} xs={12} >
                                            {[2,117].indexOf(ProductSelect) > -1 ? <label>BU Team Remarks</label> : <label>MIS Team Remarks</label>}
                                            <Textarea
                                                name="SecondLevelRemarks"
                                                minRows={3}
                                                placeholder="Enter Remarks here(4-400 characters)…"
                                                onChange={handleChange}
                                                value={(ApprovalLevel === 'second' && CurrentStatusID == 3 && !(RowWiseData[0].SecondLevelComment && RowWiseData[0].SecondLevelComment.length > 0)) ? Comments : RowWiseData[0].SecondLevelComment}
                                                disabled={!(ApprovalLevel === 'second' && CurrentStatusID == 3)}
                                                className={!(ApprovalLevel === 'second' && CurrentStatusID === 3) ? "" : "highlight-textarea"}
                                            />
                                        </Grid>
                                    </Grid>
                                </Grid>
                                <Grid item sm={7} md={7} xs={12}>
                                    <BookingDetails data={BookingData} />
                                    {ActivityLogs && ActivityLogs.length > 0 &&
                                        <>
                                            <h4>Activity log</h4>
                                            <ul className="ActivityLog">
                                                {ActivityLogs.map((entry, index) => (
                                                    <React.Fragment key={index}>
                                                        <li>
                                                            <CircleIcon className={getStatusClassName(entry.StatusID)} />
                                                            Request {getStatusActionName(entry.StatusID)} By &nbsp;
                                                            <strong>{entry.UserName} - {entry.EmployeeCode}</strong>
                                                        </li>
                                                        <p className="dateTime">
                                                            {new Date(entry.Createdon).toLocaleDateString()} - {new Date(entry.Createdon).toLocaleTimeString()}
                                                        </p>
                                                    </React.Fragment>
                                                ))}
                                            </ul>
                                        </>
                                    }
                                </Grid>
                            </Grid>

                        </DialogContent>
                        {ApprovalLevel && 
                            <DialogActions>
                                {renderApprovalActions()}
                            </DialogActions>
                        }
                        {
                            ([2,5].includes(CurrentStatusID) && RowWiseData[0].RequestedByUserID == User.UserId) && 
                            <DialogActions>
                                {(() => {
                                    const MAX_REREQUESTS = 2;
                                    const firstRemaining = Math.max(0, MAX_REREQUESTS - FirstLevelReRaiseCount);
                                    const secondRemaining = Math.max(0, MAX_REREQUESTS - SecondLevelReRequestCount);
                                    const isExceeded = CurrentStatusID === 2
                                        ? FirstLevelReRaiseCount >= MAX_REREQUESTS
                                        : SecondLevelReRequestCount >= MAX_REREQUESTS;
                                    
                                    const getLevelName = (statusID) => {
                                        if (statusID === 2) return 'first level';
                                        if (statusID === 5) return 'second level';
                                        return 'unknown level';
                                    };
                                    
                                    return (
                                        <p>
                                            {IsValidReRequest
                                                ? 'A re-raise request is allowed within 10 days of the booking date.'
                                                : isExceeded
                                                    ? `Maximum re-raise attempts (2) reached for ${getLevelName(CurrentStatusID)}.`
                                                    : `You can re-raise the request. Remaining ${CurrentStatusID === 2 ? firstRemaining : secondRemaining} of ${MAX_REREQUESTS}.`}
                                        </p>
                                    );
                                })()}
                                <div>
                                    <Button 
                                        className={(() => {
                                            const isExceeded = CurrentStatusID === 2
                                                ? FirstLevelReRaiseCount >= 2
                                                : SecondLevelReRequestCount >= 2;
                                            return !IsValidReRequest && !isExceeded ? "CreateBtn approvedBtn" : "CreateBtn DisabledecLineBtn";
                                        })()} 
                                        onClick={() => { ReRaiseRequest() }}
                                        disabled={(() => {
                                            const isExceeded = CurrentStatusID === 2
                                                ? FirstLevelReRaiseCount >= 2
                                                : SecondLevelReRequestCount >= 2;
                                            return IsValidReRequest || isExceeded;
                                        })()}
                                    >
                                        Re-Raise Request
                                    </Button>
                                </div>
                            </DialogActions>
                        }
                    </Dialog>

                    <BookingHistory open={BookingHistoryShow} onClose={handleCreditChangeRequestClose} bookingData={NewRequestData} />
                </>

            }

        </>
    );
}