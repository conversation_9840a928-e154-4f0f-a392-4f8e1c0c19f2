import React, { useState } from 'react';
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Typography,
    Checkbox,
    TextField,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    CircularProgress
} from '@mui/material';
import { GetRejectLeads, GetRejectLeadsInput } from '../../../../services/Common';
import { useSnackbar } from "notistack";
import User from "../../../../../src/services/user.service";
import rootScopeService from '../../../../services/rootScopeService';

const LeadRejectionPrompt = ({
    open,
    onClose,
    customerOpenLeads,
    loadingOpenLeads,
    onProceedWithBooking
}) => {
    const { enqueueSnackbar } = useSnackbar();
    const [selectedLeadsForRejection, setSelectedLeadsForRejection] = useState([]);
    const [rejectionNote, setRejectionNote] = useState('');
    const [rejecting, setRejecting] = useState(false);

    const handleLeadSelection = (leadId, isSelected) => {
        if (isSelected) {
            setSelectedLeadsForRejection(prev => [...prev, leadId]);
        } else {
            setSelectedLeadsForRejection(prev => prev.filter(id => id !== leadId));
        }
    };

    const handleSelectAll = (isSelected) => {
        if (isSelected) {
            // Select all leads
            const allLeadIds = customerOpenLeads.map(lead => lead.LeadId);
            setSelectedLeadsForRejection(allLeadIds);
        } else {
            // Deselect all leads
            setSelectedLeadsForRejection([]);
        }
    };

    const isAllSelected = customerOpenLeads.length > 0 && selectedLeadsForRejection.length === customerOpenLeads.length;
    const isIndeterminate = selectedLeadsForRejection.length > 0 && selectedLeadsForRejection.length < customerOpenLeads.length;

    const handleRejectSelectedLeads = async () => {
        if (selectedLeadsForRejection.length === 0) {
            enqueueSnackbar("Please select at least one lead to reject.", { variant: 'warning', autoHideDuration: 3000 });
            return;
        }

        try {
            setRejecting(true);
            const reasonData =  [];
            let LeadIds = selectedLeadsForRejection.join(",");
            let res = GetRejectLeadsInput(LeadIds, 2430, 131, User.UserId, 0, false, reasonData);
            const response = await GetRejectLeads(res.input, res.isCoreApi);
            
            if (response && response.Data) {
                enqueueSnackbar(`Successfully rejected ${LeadIds}.`, { variant: 'success', autoHideDuration: 3000 });
                handleClose();
                // Complete booking flow after successful lead rejection
                onProceedWithBooking();
            } else {
                enqueueSnackbar("Failed to reject selected leads.", { variant: 'error', autoHideDuration: 3000 });
            }
        } catch (error) {
            console.error('Error rejecting leads:', error);
            enqueueSnackbar("Error occurred while rejecting leads.", { variant: 'error', autoHideDuration: 3000 });
        } finally {
            setRejecting(false);
        }
    };

    const handleCloseWithoutAction = () => {
        handleClose();
        // Complete booking flow without rejecting any leads
        onProceedWithBooking();
    };

    const handleClose = () => {
        setSelectedLeadsForRejection([]);
        setRejectionNote('');
        setRejecting(false);
        onClose();
    };

    return (
        <Dialog 
            open={open} 
            maxWidth="md" 
            fullWidth
            disableEscapeKeyDown
            className="lead-rejection-prompt"
            onClose={(event, reason) => {
                if (reason !== 'backdropClick' && reason !== 'escapeKeyDown') {
                    handleClose();
                }
            }}
        >
            <DialogTitle className="dialog-title">
                <Typography variant="h6" className="title-text">
                    Booking Completed - Other Open Leads Found
                </Typography>
                <Typography variant="body2" color="textSecondary">
                    Your booking has been successfully completed! This customer has other open leads. You can now choose to reject selected leads or skip this step.
                </Typography>
            </DialogTitle>
            
            <DialogContent className="dialog-content">
                {loadingOpenLeads ? (
                    <div className="loading-container">
                        <CircularProgress />
                    </div>
                ) : (
                    <>
                        <div className="success-banner">
                            <Typography variant="body2" className="success-text">
                                ✅ Booking Successfully Completed! Manage other open leads below (optional).
                            </Typography>
                        </div>
                        <TableContainer component={Paper} className="table-container table-container-scrollable">
                            <Table size="small" className="table table-responsive">
                                <TableHead>
                                    <TableRow>
                                        <TableCell className="select-all-cell">
                                            <div className="select-all-container">
                                                <Checkbox
                                                    checked={isAllSelected}
                                                    indeterminate={isIndeterminate}
                                                    onChange={(e) => handleSelectAll(e.target.checked)}
                                                    inputProps={{ 'aria-label': 'Select all leads' }}
                                                    size="small"
                                                />
                                                <span className="select-all-text">Select All</span>
                                            </div>
                                        </TableCell>
                                        <TableCell className="lead-id-cell">Lead ID</TableCell>
                                        <TableCell className="header-cell">Company Name</TableCell>
                                        <TableCell className="date-cell">CreatedOn</TableCell>
                                        <TableCell className="header-cell">Product Type</TableCell>
                                        <TableCell className="status-cell">Status</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {customerOpenLeads.map((lead) => (
                                        <TableRow key={lead.LeadId}>
                                            <TableCell className="body-cell body-cell-select">
                                                <Checkbox
                                                    checked={selectedLeadsForRejection.includes(lead.LeadId)}
                                                    onChange={(e) => handleLeadSelection(lead.LeadId, e.target.checked)}
                                                    size="small"
                                                />
                                            </TableCell>
                                            <TableCell className="body-cell-text body-cell-lead-id">{lead.LeadId}</TableCell>
                                            <TableCell className="body-cell-text body-cell-company-name">{lead.CompanyName || 'N/A'}</TableCell>
                                            <TableCell className="body-cell-text body-cell-date">
                                                {lead.CreatedOn ? new Date(lead.CreatedOn).toLocaleDateString() : 'N/A'}
                                            </TableCell>
                                            <TableCell className="body-cell-text body-cell-product">{lead.ProductName || 'N/A'}</TableCell>
                                            <TableCell className="body-cell-text body-cell-status">{lead.StatusName || 'N/A'}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                        
                        {/* <TextField
                            fullWidth
                            multiline
                            rows={2}
                            label="Rejection Note (Optional)"
                            variant="outlined"
                            value={rejectionNote}
                            onChange={(e) => setRejectionNote(e.target.value)}
                            placeholder="Enter reason for rejecting selected leads..."
                            size="small"
                        /> */}
                    </>
                )}
            </DialogContent>
            
            <DialogActions className="dialog-actions">
                <Button 
                    onClick={handleCloseWithoutAction}
                    color="secondary"
                    disabled={rejecting}
                    size="medium"
                >
                    Cancel
                </Button>
                <Button 
                    onClick={handleRejectSelectedLeads}
                    color="primary"
                    variant="contained"
                    disabled={rejecting || selectedLeadsForRejection.length === 0}
                    startIcon={rejecting ? <CircularProgress size={20} /> : null}
                    size="medium"
                >
                    {rejecting ? 'Rejecting Leads...' : 'Reject Selected Leads & Continue'}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default LeadRejectionPrompt; 