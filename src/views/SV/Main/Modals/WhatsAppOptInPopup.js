import React from "react";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { Button, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import "./WhatsAppOptInPopup.scss";

const WhatsAppOptInPopup = (props) => {
  const {
    open,
    handleClose,
    handleOptInRequest,
    isRequestInProgress = false,
    parentLeadId
  } = props;

  return (
    <ModalPopup
      open={open}
      handleClose={handleClose}
      className="whatsapp-optin-popup"

    >
      <IconButton onClick={props.handleClose} size="large">
        <CloseIcon />
      </IconButton>
      <div className="whatsapp-optin-container">
        {/* <div className="header">
          <CloseIcon className="close-icon" onClick={handleClose} />
        </div> */}

        
          <p>
            The customer must be missing important updates from Policybazaar.
            You can send them an SMS with a link to confirm their WhatsApp opt-in
          </p>

        <div className="actions">
          <Button
            variant="contained"
            color="primary"
            className="optin-button"
            onClick={() => handleOptInRequest(parentLeadId)}
            disabled={isRequestInProgress}
          >
            Request WhatsApp Opt-In via SMS
          </Button>
        </div>
      </div>
    </ModalPopup>
  );
};

export default WhatsAppOptInPopup;