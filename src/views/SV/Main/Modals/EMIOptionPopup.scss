.EMIOptionPopup {
    .MuiDialog-scrollPaper {
        justify-content: right;
    }

    .MuiDialog-paperWidthSm {
        width: 600px;
        max-height: 100%;
        height: 100%;
        border-radius: 12px;
        margin-right: 0px;

        .MuiDialogContent-root {
            padding: 24px !important;
        }

        th {
            font: normal normal 500 12px/22px Roboto;
            color: #253858;
            padding: 12px;
            background-color: #f8f9fa;
            border-bottom: 2px solid #DFE1E6;
        }

        td {
            font: normal normal 500 14px/20px Roboto;
            color: #253858;
            padding: 12px;
            border-bottom: 1px solid #DFE1E6;
            transition: background-color 0.2s;

            &:hover {
                background-color: #f8f9fa;
            }

            span {
                font: normal normal normal 12px/20px Roboto;
                color: #666;
                display: block;
                margin-top: 4px;
            }
        }

        .MuiFormGroup-root {
            margin-bottom: 24px;
        }

        .MuiFormControlLabel-root {
            margin: 8px 0;
        }
    }

    .form-section {
        padding: 24px;
        margin-bottom: 24px;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease;
        position: relative;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .section-title {
            color: #0065ff;
            font-weight: 600;
            margin-bottom: 16px;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 16px;
            
            &::before {
                content: '';
                width: 4px;
                height: 100%;
                background-color: #0065ff;
                position: absolute;
                left: 0;
                top: 0;
                border-radius: 2px;
            }
        }

        .section-note {
            color: #666;
            font-size: 0.875rem;
            margin: 0 0 24px;
            font-style: italic;
            padding: 12px 16px;
            background-color: #f8f9fa;
            position: relative;
            padding-left: 16px;
            border-radius: 4px;

            &::before {
                content: '';
                width: 4px;
                height: 100%;
                background-color: #0065ff;
                opacity: 0.5;
                position: absolute;
                left: 0;
                top: 0;
                border-radius: 2px;
            }
        }
    }

    .eligibility-status {
        margin: 16px 0;
        
        .status-box {
            padding: 12px 16px;
            border-radius: 6px;
            text-align: left;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: transform 0.3s ease;

            &:hover {
                transform: translateY(-2px);
            }

            &.eligible {
                background-color: #E3FCEF;
                border: 1px solid #36B37E;

                h6 {
                    color: #36B37E;
                    font-weight: 600;
                }

                .status-note {
                    color: #2D8F69;
                }
            }

            &.not-eligible {
                background-color: #FFE3E3;
                border: 1px solid #FA5252;

                h6 {
                    color: #FA5252;
                    font-weight: 600;
                }

                .status-note {
                    color: #C92A2A;
                }
            }
        }
    }

    .disabled-section {
        background-color: #F8F9FA;
        border: 1px solid #DEE2E6;

        .section-title {
            color: #868E96;
        }

        .section-note {
            color: #ADB5BD;
            border-left-color: #DEE2E6;
        }
    }

    .Text-Center {
        text-align: center;
        margin-top: 32px;
    }

    .checkEmiBtn {
        background: #0065ff;
        border-radius: 8px;
        padding: 12px 24px;
        min-width: 180px;
        border: none;
        color: #ffffff;
        font: normal normal 600 14px/21px Roboto;
        cursor: pointer;
        outline: none;
        margin-top: 32px;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &:hover {
            background: #0052cc;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 101, 255, 0.2);
        }

        &:active {
            transform: translateY(0);
        }

        &:disabled {
            background: #ddd;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    }

    .useExitMobileNo {
        position: relative;
        margin: 16px;

        .MuiFormControlLabel-label {
            color: #253858;
            font-family: Roboto;
            font-size: 14px;
            font-weight: 500;
        }

        .MuiCheckbox-root {
            padding: 9px;
            transition: all 0.2s ease;

            &:hover {
                background-color: rgba(0, 101, 255, 0.04);
            }

            svg {
                font-size: 20px;
                color: #BBBBBB;
                transition: color 0.2s ease;
            }
        }

        .Mui-checked {
            svg {
                color: #0065FF;
            }
        }
    }

    .timerBtn {
        font: normal normal 14px Roboto;
        color: #253858;
        padding: 8px 16px;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin: 16px 0;
        transition: all 0.3s ease;

        &:hover {
            background: #e9ecef;
        }

        svg {
            font-size: 18px;
            margin-right: 8px;
            color: #0065ff;
        }

        p {
            margin: 0;
            display: flex;
            align-items: center;
        }

        &.disabled {
            color: #adb5bd;
            background: #f1f3f5;
            border-color: #dee2e6;
        }
    }

    .pan-note {
        color: #dc3545;
        font-size: 13px;
        margin: 16px;
        font-family: Roboto;
        display: flex;
        align-items: center;
        
        &::before {
            content: '*';
            color: #dc3545;
            margin-right: 4px;
            font-weight: bold;
        }
    }

    // Accordion Styles
    .MuiAccordion-root {
        border-radius: 8px !important;
        margin-bottom: 16px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        transition: box-shadow 0.3s ease !important;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }

        &::before {
            display: none;
        }

        .MuiAccordionSummary-root {
            padding: 0 24px;
            min-height: 64px;
            
            &:hover {
                background-color: #f8f9fa;
            }
        }

        .MuiAccordionSummary-content {
            font: normal normal 500 16px/26px Roboto;
            color: #253858;
        }

        .MuiAccordionDetails-root {
            padding: 24px;
            background-color: #fff;
        }
    }

    // Radio Button Styles
    .radio-group {
        .MuiFormControlLabel-root {
            margin: 8px 16px 8px 0;
            transition: all 0.2s ease;

            &:hover {
                .MuiRadio-root {
                    background-color: rgba(0, 101, 255, 0.04);
                }
            }
        }

        .MuiRadio-root {
            padding: 9px;
            
            svg {
                font-size: 20px;
                transition: color 0.2s ease;
            }
        }

        .Mui-checked {
            svg {
                color: #0065FF;
            }
        }
    }

    // Status Labels
    h5 {
        margin: 24px 0;
        font: normal normal 500 16px/26px Roboto;

        label {
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &:not(.noEligble) {
                background-color: #E3FCEF;
                color: #36B37E;
            }
        }

        .noEligble {
            background-color: #FFE3E3;
            color: #FA5252;
        }
    }

    // Input Fields Enhancement
    .MuiTextField-root {
        .MuiInputBase-root {
            border-radius: 8px;
            transition: all 0.2s ease;

            &:hover {
                background-color: #f8f9fa;
            }

            &.Mui-focused {
                background-color: #fff;
                box-shadow: 0 0 0 2px rgba(0, 101, 255, 0.2);
            }
        }

        .MuiInputLabel-root {
            color: #495057;
            
            &.Mui-focused {
                color: #0065ff;
            }
        }
    }
}