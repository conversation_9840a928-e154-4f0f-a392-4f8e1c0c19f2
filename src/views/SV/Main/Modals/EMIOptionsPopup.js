import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import React from "react";
import { Shopse } from "../Modals/Shopse";
import { ShopseNTB } from "../Modals/ShopseNTB";
import { AxioEligibility } from "../Modals/AxioEligibility";
import PropTypes from "prop-types";
import { Box, Tab, Tabs } from "@mui/material";
import './EMIOptionPopup.scss';

function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`full-width-tabpanel-${index}`}
            aria-labelledby={`full-width-tab-${index}`}
            {...other}
        >
            {value === index && <Box p={3}>{children}</Box>}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
};

export const EMIOptionsPopup = (props) => {
    const { handleClose, open } = props;
    const [activeTab, setActiveTab] = React.useState(0);

    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    return (
        <ModalPopup className="EMIOptionPopup" title="Shopse / Axio EMI Eligibility" open={open} handleClose={handleClose}>

            <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                className="tabs"
            >
                <Tab label="Shopse" />
                <Tab label="Axio" />
                <Tab label="Shopse LCE" />

            </Tabs>

            <TabPanel value={activeTab} index={0}>
                <Shopse />
            </TabPanel>

            <TabPanel value={activeTab} index={1}>
                <AxioEligibility />
            </TabPanel>

            <TabPanel value={activeTab} index={2}>
                <ShopseNTB />
            </TabPanel>

        </ModalPopup>
    );
}
