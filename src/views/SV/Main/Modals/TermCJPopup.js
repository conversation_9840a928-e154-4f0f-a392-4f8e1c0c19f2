import React, {useState,useEffect} from "react";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import User from "../../../../services/user.service";
import {Button} from '@mui/material';
import { SV_CONFIG } from "../../../../appconfig";

const TermCJPopup = (props) => {
    let lead = props.lead;
    const [CJURL, setCJURL] = useState('');
    const [IFrameURL, setIFrameURL] = useState('');

    useEffect(() => {
        if(props.open == true)
        {
            if(lead)
            {
                let cjlink = ''
                if (lead.ContinueJourneyURL) 
                  cjlink = lead.ContinueJourneyURL;
                else 
                  cjlink = SV_CONFIG["TermCJ"];

                if (User.LanguageID && User.LanguageID != 0) {
                  setCJURL(cjlink + "&LanguageID=" + User.LanguageID)
                } else {
                  setCJURL(cjlink);
                }
                
                if(lead.EnquiryID && lead.EnquiryID > 0 && lead.ContinueJourneyURL)
                {
                    let CJ = new URL(lead.ContinueJourneyURL);
                    let params = CJ ? new URLSearchParams(CJ.search) : '';
                    let refId = params ? params.get('refId') : '';
                    let custId = params ? params.get('custId') : '';
                    
                    if(refId && custId && SV_CONFIG["TermCJSmartPanelURL"])
                        setIFrameURL(`${SV_CONFIG["TermCJSmartPanelURL"]}/smart-panel?refId=${refId}&custId=${custId}`);                  
                }   
            }
        }
    },[props.open]);

    const handleOpenCJURL = () => {
        if(CJURL){
            window.open(CJURL);
        }
    }

    return(
        <ModalPopup className="improvedContinueJourneyPopup" open={props.open} handleClose={props.handleClose}>
           <div className="popupWrapper">
               <div className="popup-header">
                   <button
                       className="continue-journey-btn"
                       onClick={handleOpenCJURL}
                   >
                       Continue Journey
                   </button>
                   <IconButton 
                       onClick={props.handleClose} 
                       className="close-button"
                       size="large"
                   >  
                       <CloseIcon />
                   </IconButton>
               </div>
               
               <div className="iframe-container">
                   <iframe
                       src={IFrameURL}
                       title="Smart Panel"
                       style={{ width: '100%', height: '100%' }}
                   />
               </div>
           </div>
        </ModalPopup>
    )
}

export default TermCJPopup;