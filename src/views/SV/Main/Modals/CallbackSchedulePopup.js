
import React from "react";
import ModalPopup from '../../../../components/Dialogs/ModalPopup';
import { Box, Typography, IconButton } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import WarningIcon from '@mui/icons-material/Warning';
import "./CallbackSchedulePopup.scss";
import { CONFIG } from "../../../../appconfig";

const CallbackSchedulePopup = (props) => {
  const {
    open,
    handleClose,
  } = props;

  return (
    <ModalPopup
      open={open}
      handleClose={handleClose}
      className="callback-schedule-popup"
      disableBackdropClick={false}
      showCloseButton={false}
    >
      <IconButton className="close-icon" onClick={handleClose}>
          <CloseIcon />
        </IconButton>
      <Box className="callback-popup-container">
        

        <Box className="popup-header">
          <Box className="icon-circle">
            <img src={CONFIG.PUBLIC_URL + "/images/warning.svg"} alt="Warning" />
          </Box>
          
          <Box className="text-container">
            <Typography variant="h5" className="popup-heading">
              Schedule a callback?
            </Typography>
            
            <Typography variant="body1" className="popup-subtext">
              You have been on the call for 20 minutes
            </Typography>
          </Box>
        </Box>
        
        <Box className="highlight-banner">
          <Typography variant="body2" className="banner-text">
            <img src={CONFIG.PUBLIC_URL + "/images/star.png"} alt="star" /> Conversion increases by 3x on scheduled calls
          </Typography>
        </Box>
      </Box>
    </ModalPopup>
  );
};

export default CallbackSchedulePopup;
