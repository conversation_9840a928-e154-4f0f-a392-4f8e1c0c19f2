import { Checkbox, FormControlLabel, Grid, Paper, Typography } from '@mui/material';
import { TextInput } from '../../../../components';
import React, { useEffect, useState } from "react";
import { GetAxioEligibleOffers, GetEncryptedMobileNo } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import rootScopeService from "../../../../services/rootScopeService";
import { toUpper } from 'lodash';
import { SV_CONFIG } from "../../../../appconfig";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from "dayjs";
import './EMIOptionPopup.scss';

export const AxioEligibility = (props) => {
    let [AxioDetails, setAxioDetails] = useState(undefined);
    let [showAxioDetails, setshowAxioDetails] = useState(false);
    let [IsExistMobileNo, setIsExistMobileNo] = useState(true);
    let [isdisabled, setisdisabled] = useState(false);
    let [InputMobile, setInputMobile] = useState("");
    let [EncMobile, setEncMobile] = useState("");
    let [InputAmount, setInputAmount] = useState("");
    let [Fullname, setFullname] = useState("");
    let [Emailaddress, setEmailaddress] = useState("");
    let [PANcard, setPANcard] = useState("");
    let [Dateofbirth, setDateofbirth] = useState("");
    const { enqueueSnackbar } = useSnackbar();
    const LeadID = rootScopeService.getLeadId()
    let ShowAxioEMI = SV_CONFIG["ShowAxioEMI"];

    const validateEmail = function (email) {
        // eslint-disable-next-line no-useless-escape
        var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/; if (!emailReg.test(email)) { return false; } else {
            return true;
        }
    }

    const handleChange = (e) => {
        let { name, value, type } = e.target;
        const regx = /^[0-9\b]+$/;
        if (name == "Mobilenumber") {
            if (value !== "" && !regx.test(value)) {
                enqueueSnackbar("Only Numbers allowed", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            } else {
                setInputMobile(value);
            }
        }
        if (name == "Amount") {
            if (value !== "" && !regx.test(value)) {
                enqueueSnackbar("Only Numbers allowed", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            } else {
                setInputAmount(value);
            }
        }
        if (name == "Fullname") {
            setFullname(value);
        }
        if (name == "Emailaddress") {
            setEmailaddress(value);
        }
        if (name == "PANcard") {
            setPANcard(toUpper(value).trim());
        }
        if (name == "Dateofbirth") {
            setDateofbirth(value);
        }
        if (name == "IsExistMobileNo") {
            value = e.target.checked
            setIsExistMobileNo(value);
            if (e.target.checked == false) { setInputMobile("") }
            else { setInputMobile(EncMobile) }
        }

    }
    const GetAxioEligibleOffer = (MobileNo, Amount) => {
        GetAxioEligibleOffers(MobileNo, Amount, IsExistMobileNo == true ? 1 : 0, LeadID).then((result) => {
            if (result != null && result != undefined) {
                setshowAxioDetails(true);
                setAxioDetails(result);
            }
        }).catch((e) => {
            console.log(e);
        })
        setisdisabled(false);
    }

    const GetEncryptedMobile = () => {
        GetEncryptedMobileNo(LeadID).then((result) => {
            if (result) {
                setInputMobile(result);
                setEncMobile(result);
            }
        }).catch((e) => {
            console.log(e);
        })
    }

    const FetchDetails = () => {
        setisdisabled(true);
        const dateregx = /^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$/;
        let panNoRegex = /[a-zA-Z]{5}[0-9]{4}[a-zA-Z]{1}$/;
        let alphanumericRegex = /^([a-zA-Z0-9]+)$/;
        if (!(IsExistMobileNo) && ((InputMobile == "" || InputMobile == undefined) || !((InputMobile.toString()).length == 10))) {
            enqueueSnackbar("Please enter valid 10 digit Mobile Number", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else if (InputAmount == 0 || InputAmount < 1000) {
            enqueueSnackbar("Please enter valid Amount", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else if ((!alphanumericRegex.test(PANcard) || (PANcard.length !== 10)) && (!panNoRegex.test(PANcard))) {
            enqueueSnackbar("PAN should be in valid format", {
                variant: "error",
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else if (Emailaddress && !validateEmail(Emailaddress)) {
            enqueueSnackbar("Please enter valid Email Address", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else if (!Dateofbirth) {
            enqueueSnackbar("Please enter Date Of Birth", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else if (Dateofbirth !== "" && !dateregx.test(Dateofbirth)) {
            enqueueSnackbar("Enter valid date", {
                variant: "error",
                autoHideDuration: 3000,
            });
            setisdisabled(false);
        }
        else {
            const reqData = {
                Mobilenumber: InputMobile,
                Amount: InputAmount,
                Fullname: Fullname,
                Emailaddress: Emailaddress,
                PANcard: PANcard,
                Dateofbirth: Dateofbirth,
                IsExistMobileNo: IsExistMobileNo,
                LeadID: LeadID
            };
            GetAxioEligibleOffer(reqData);

        }
    }

    useEffect(() => {
        if (!showAxioDetails) {
            GetEncryptedMobile();
        }
    }, [showAxioDetails]);

    return (<div className="EMIOptionPopup">
        {ShowAxioEMI && <>
            {!showAxioDetails && <>
                <Paper elevation={3} className="form-section">
                    <Typography variant="h6" className="section-title">
                        Personal Details
                    </Typography>
                    <Typography className="section-note">
                        *Please ensure all details are accurate for EMI eligibility check
                    </Typography>
                <Grid container spacing={2}>
                    <TextInput
                        name="Fullname"
                        label="Full name"
                        sm={6} md={6} xs={12}
                        handleChange={handleChange}
                        value={Fullname}
                    />
                    <TextInput
                        name="Emailaddress"
                        label="Email address"
                        sm={6} md={6} xs={12}
                        handleChange={handleChange}
                        value={Emailaddress}
                    />
                    <TextInput
                        name="Mobilenumber"
                        label="Mobile number"
                        sm={6} md={6} xs={12}
                        handleChange={handleChange}
                        value={InputMobile}
                        pattern="[0-9]"
                        maxLength="12"
                        disabled={IsExistMobileNo}
                    />
                    <FormControlLabel className="useExitMobileNo"
                        name="IsExistMobileNo"
                        control={<Checkbox color="primary" />}
                        label="Use existing mobile number"
                        value={IsExistMobileNo}
                        onChange={handleChange}
                        checked={IsExistMobileNo}
                    />
                    <TextInput
                        name="PANcard"
                        label="PAN card"
                        sm={6} md={6} xs={12}
                        handleChange={handleChange}
                        value={PANcard}
                    />
                    <Grid item sm={6} md={6} xs={12}>
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                            <DatePicker
                                label="Date of birth"
                                value={Dateofbirth ? dayjs(Dateofbirth, "DD-MM-YYYY") : null}
                                onChange={(newValue) => {
                                    const formattedDate = newValue ? newValue.format("DD-MM-YYYY") : "";
                                    handleChange({ target: { name: "Dateofbirth", value: formattedDate } });
                                }}
                                slotProps={{
                                    textField: {
                                        fullWidth: true,
                                    }
                                }}
                                format="DD-MM-YYYY"
                            />
                        </LocalizationProvider>
                    </Grid>
                    <TextInput
                        name="Amount"
                        label="Amount"
                        sm={6} md={6} xs={12}
                        handleChange={handleChange}
                        value={InputAmount}
                        pattern="[0-9]"
                    />
                </Grid>
                </Paper>
                <div className="Text-Center">
                    <button 
                        disabled={isdisabled} 
                        className={isdisabled ? "disabledInput" : "checkEmiBtn"} 
                        onClick={FetchDetails}
                    >
                        {isdisabled ? 'Processing...' : 'Check EMI eligibility'}
                    </button>
                </div>
            </>}
            {showAxioDetails && AxioDetails != undefined && AxioDetails != null && 
                <Paper elevation={3} className="form-section">
                    <Typography variant="h6" className="section-title">
                        EMI Eligibility Result
                    </Typography>
                    <Typography variant="h5" gutterBottom>
                        Amount: &#8377;{InputAmount}
                    </Typography>
                    <div className="eligibility-status">
                        {AxioDetails == true ? (
                            <div className="status-box eligible">
                                <Typography variant="h6">ELIGIBLE</Typography>
                                <Typography className="status-note">
                                    You are eligible for EMI options
                                </Typography>
                            </div>
                        ) : (
                            <div className="status-box not-eligible">
                                <Typography variant="h6">NOT ELIGIBLE</Typography>
                                <Typography className="status-note">
                                    Unfortunately, you are not eligible at this time
                                </Typography>
                            </div>
                        )}
                    </div>
                    <div className="Text-Center">
                        <button className="checkEmiBtn" onClick={() => setshowAxioDetails(false)}>
                            Check Another Amount
                        </button>
                    </div>
                </Paper>
            }
        </>}
        {!ShowAxioEMI && 
            <Paper elevation={3} className="form-section disabled-section">
                <Typography variant="h6" className="section-title">
                    Service Unavailable
                </Typography>
                <Typography className="section-note">
                    Axio Eligibility check is currently disabled. Please try again later.
                </Typography>
            </Paper>
        }
    </div>
    );
}
