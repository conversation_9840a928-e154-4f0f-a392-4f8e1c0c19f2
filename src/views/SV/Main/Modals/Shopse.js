import { Accordion, AccordionDetails, AccordionSummary, Checkbox, FormControlLabel, Grid, Paper, Radio, RadioGroup, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography } from '@mui/material';
import { TextInput } from '../../../../components';
import React, { useEffect, useState } from "react";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { GetShopseEligibleOffers, GetEncryptedMobileNo } from "../../../../services/Common";
import { useSnackbar } from "notistack";
import rootScopeService from "../../../../services/rootScopeService";
import './EMIOptionPopup.scss';

export const Shopse = (props) => {
    let [ShopseDetails, setShopseDetails] = useState(undefined);
    let [showShopseDetails, setshowShopseDetails] = useState(false);
    let [IsExistMobileNo, setIsExistMobileNo] = useState(true);
    let [InputMobile, setInputMobile] = useState("");
    let [EncMobile, setEncMobile] = useState("");
    let [InputAmount, setInputAmount] = useState("");
    let [ShowCC, setShowCC] = useState(false);
    let [ShowDC, setShowDC] = useState(false);
    let [ShowCardless, setShowCardless] = useState(false);
    let [ShowNTB, setShowNTB] = useState(false);
    const { enqueueSnackbar } = useSnackbar();
    let [expanded, setExpanded] = React.useState(0);
    const LeadID = rootScopeService.getLeadId();

    const handleChange = (e) => {
        let { name, value, type } = e.target;
        const regx = /^[0-9\b]+$/;

        switch (name) {
            case "Mobilenumber":
            if (value !== "" && !regx.test(value)) {
                enqueueSnackbar("Only Numbers allowed", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            } else {
                setInputMobile(value);
            }
                break;

            case "Amount":
            if (value !== "" && !regx.test(value)) {
                enqueueSnackbar("Only Numbers allowed", {
                    variant: "error",
                    autoHideDuration: 3000,
                });
            } else {
                setInputAmount(value);
            }
                break;

            case "IsExistMobileNo":
                value = e.target.checked;
            setIsExistMobileNo(value);
                if (!e.target.checked) {
                    setInputMobile("");
                } else {
                    setInputMobile(EncMobile);
                }
                break;

            case "cardtype":
                setShowCC(value === "CC");
                setShowDC(value === "DC");
                setShowCardless(value === "Cardless");
                setShowNTB(value === "NTB");
                break;

            default:
                break;
        }
    };

    const handleBankChange = (bankname) => {
        if (expanded === bankname) {
            setExpanded(0);
        } else {
            setExpanded(bankname);
            setShowCC(false);
            setShowDC(false);
            setShowCardless(false);
            setShowNTB(false);
        }
    };

    const GetShopseEligibleOffer = (MobileNo, Amount) => {
        GetShopseEligibleOffers(MobileNo, Amount, IsExistMobileNo ? 1 : 0, LeadID)
            .then((result) => {
            if (result) {
                setshowShopseDetails(true);
                setShopseDetails(result);
            }
            })
            .catch((e) => {
            console.log(e);
            });
    };

    const GetEncryptedMobile = () => {
        GetEncryptedMobileNo(LeadID)
            .then((result) => {
            if (result) {
                setInputMobile(result);
                setEncMobile(result);
            }
            })
            .catch((e) => {
            console.log(e);
            });
    };

    const FetchDetails = () => {
        if (!IsExistMobileNo && (!InputMobile || InputMobile.toString().length !== 10)) {
            enqueueSnackbar("Please enter valid 10 digit Mobile Number", {
                variant: 'error',
                autoHideDuration: 3000,
            });
        } else if (!InputAmount || InputAmount < 1000) {
            enqueueSnackbar("Please enter valid Amount (minimum ₹1,000)", {
                variant: 'error',
                autoHideDuration: 3000,
            });
        } else {
            GetShopseEligibleOffer(InputMobile, InputAmount);
    }
    };

    const Recheck = () => {
        setshowShopseDetails(false);
        setIsExistMobileNo(true);
    };

    useEffect(() => {
        if (!showShopseDetails) {
            GetEncryptedMobile();
        }
    }, [showShopseDetails]);

    return (
        <div className="EMIOptionPopup">
            {!showShopseDetails && (
                <Paper elevation={3} className="form-section">
                    <Typography variant="h6" className="section-title">
                        EMI Eligibility Check
                    </Typography>
                    <Typography className="section-note">
                        Please provide your details to check EMI eligibility
                    </Typography>
            <Grid container spacing={2}>
                <TextInput
                    name="Mobilenumber"
                    label="Mobile number"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={InputMobile}
                    pattern="[0-9]"
                    maxLength="12"
                    disabled={IsExistMobileNo}
                            required={!IsExistMobileNo}
                />
                        <FormControlLabel
                            className="useExitMobileNo"
                    name="IsExistMobileNo"
                    control={<Checkbox color="primary" />}
                    label="Use existing mobile number"
                    value={IsExistMobileNo}
                    onChange={handleChange}
                    checked={IsExistMobileNo}
                />
                <TextInput
                    name="Amount"
                    label="Amount"
                    sm={6} md={6} xs={12}
                    handleChange={handleChange}
                    value={InputAmount}
                    pattern="[0-9]"
                            required={true}
                            helperText="Minimum amount ₹1,000"
                />
            </Grid>
            <div className="Text-Center">
                        <button className="checkEmiBtn" onClick={FetchDetails}>
                            Check EMI eligibility
                        </button>
                    </div>
                </Paper>
            )}

            {showShopseDetails && ShopseDetails && (
                <>
                    <Paper elevation={3} className="form-section" style={{ padding: '16px' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                            <Typography variant="subtitle1" style={{ fontWeight: 500 }}>
                                EMI Eligibility Result
                            </Typography>
                            <Typography variant="subtitle1" style={{ color: '#666' }}>
                                Amount: <strong>&#8377;{InputAmount}</strong>
                            </Typography>
                        </div>
                        <div className="eligibility-status">
                            {ShopseDetails.Eligible ? (
                                <div className="status-box eligible">
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                        <Typography variant="subtitle1" style={{ fontWeight: 600, color: '#36B37E' }}>
                                            ELIGIBLE
                                        </Typography>
                                        <Typography variant="body2" style={{ color: '#2D8F69' }}>
                                            Select a bank below to view EMI options
                                        </Typography>
                                    </div>
                                </div>
                            ) : (
                                <div className="status-box not-eligible">
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                        <Typography variant="subtitle1" style={{ fontWeight: 600, color: '#FA5252' }}>
                                            NOT ELIGIBLE
                                        </Typography>
                                        <Typography variant="body2" style={{ color: '#C92A2A' }}>
                                            Not eligible for EMI options at this time
                                        </Typography>
                                    </div>
                                </div>
                            )}
            </div>
                    </Paper>

                    {ShopseDetails.Eligible && ShopseDetails.bankAvailable.map((details, index) => (
                        <Accordion
                            key={index}
                            expanded={expanded === details.BankName}
                            onChange={() => handleBankChange(details.BankName)}
                        >
                    <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                                aria-controls={`panel${index}-content`}
                                id={`panel${index}-header`}
                    >
                        {details.BankName}
                    </AccordionSummary>
                    <AccordionDetails>
                                <RadioGroup
                                    row
                                    onChange={handleChange}
                                    name="cardtype"
                                    className="radio-group"
                                >
                                    {details.DC && (
                                        <FormControlLabel
                                            value="DC"
                                            control={<Radio />}
                                            checked={ShowDC}
                                            label="Debit"
                                        />
                                    )}
                                    {details.CC && (
                                        <FormControlLabel
                                            value="CC"
                                            control={<Radio />}
                                            checked={ShowCC}
                                            label="Credit"
                                        />
                                    )}
                                    {details.Cardless && (
                                        <FormControlLabel
                                            value="Cardless"
                                            control={<Radio />}
                                            checked={ShowCardless}
                                            label="Cardless"
                                        />
                                    )}
                                    {details.NTB && (
                                        <FormControlLabel
                                            value="NTB"
                                            control={<Radio />}
                                            checked={ShowNTB}
                                            label="NTB"
                                        />
                                    )}
                        </RadioGroup>

                                {(ShowCC || ShowDC || ShowCardless || ShowNTB) && (
                            <TableContainer>
                                        <Table aria-label="EMI options table">
                                    <TableHead>
                                        <TableRow>
                                            <TableCell align="left">Tenure</TableCell>
                                            <TableCell align="left">Int. amount</TableCell>
                                            <TableCell align="left">Discount</TableCell>
                                            <TableCell align="right">Total Payable</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                                {ShowDC && details.DC && details.DC.map((data, idx) => (
                                                    <TableRow key={`dc-${idx}`}>
                                                        <TableCell align="left">
                                                            <b>&#8377;{data.EMI} x {data.Tenure}</b>
                                                            <br />
                                                            <span>{data.Offertype} ({data.Interest}%)</span>
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.TotalAmount + data.Discount - InputAmount}
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.Discount}
                                                        </TableCell>
                                                        <TableCell align="right">
                                                            <b>&#8377;{data.TotalAmount}</b>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                                {ShowCC && details.CC && details.CC.map((data, idx) => (
                                                    <TableRow key={`cc-${idx}`}>
                                                        <TableCell align="left">
                                                            <b>&#8377;{data.EMI} x {data.Tenure}</b>
                                                            <br />
                                                            <span>{data.Offertype} ({data.Interest}%)</span>
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.TotalAmount + data.Discount - InputAmount}
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.Discount}
                                                        </TableCell>
                                                        <TableCell align="right">
                                                            <b>&#8377;{data.TotalAmount}</b>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                                {ShowCardless && details.Cardless && details.Cardless.map((data, idx) => (
                                                    <TableRow key={`cardless-${idx}`}>
                                                        <TableCell align="left">
                                                            <b>&#8377;{data.EMI} x {data.Tenure}</b>
                                                            <br />
                                                            <span>{data.Offertype} ({data.Interest}%)</span>
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.TotalAmount + data.Discount - InputAmount}
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.Discount}
                                                        </TableCell>
                                                        <TableCell align="right">
                                                            <b>&#8377;{data.TotalAmount}</b>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                                {ShowNTB && details.NTB && details.NTB.map((data, idx) => (
                                                    <TableRow key={`ntb-${idx}`}>
                                                        <TableCell align="left">
                                                            <b>&#8377;{data.EMI} x {data.Tenure}</b>
                                                            <br />
                                                            <span>{data.Offertype} ({data.Interest}%)</span>
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.TotalAmount + data.Discount - InputAmount}
                                                        </TableCell>
                                                        <TableCell align="left">
                                                            &#8377;{data.Discount}
                                                        </TableCell>
                                                        <TableCell align="right">
                                                            <b>&#8377;{data.TotalAmount}</b>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                                )}
                    </AccordionDetails>
                </Accordion>
            ))}

                <div className="Text-Center">
                        <button className="checkEmiBtn" onClick={Recheck}>
                            Check Another Amount
                        </button>
                </div>
            </>
            )}
        </div>
    );
};