.callback-schedule-popup {
  .MuiDialog-paperScrollPaper {
    width: 530px;
    border-radius: 28px;
  }

  .close-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    color: #666;
    padding: 4px;
    cursor: pointer;
    background-color: transparent;

    svg {
      font-size: 1.4rem;
    }
  }

  .callback-popup-container {
    padding: 15px;
    text-align: center;
    margin: 0 auto;
    position: relative;



    .popup-header {
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      flex-direction: row;
      margin-bottom: 20px;
      gap: 20px;

      .icon-circle {
        img {
          width: 60px;
        }
      }

      .text-container {
        flex: 1;
        text-align: left;
        padding-top: 5px;

        .popup-heading {
          font-family: Roboto;
          font-weight: 600;
          line-height: 100%;
          color: #000;
          font-size: 24px;
          letter-spacing: 0px;
          margin-bottom: 10px;

        }

        .popup-subtext {
          font-family: Roboto;
          font-weight: 400;
          font-style: Regular;
          font-size: 15px;
          line-height: 100%;
          letter-spacing: 0px;
          color: #000;

        }
      }
    }


    .highlight-banner {
      background: #FAE6FF;
      border-radius: 8px;
      padding: 12px 20px 10px;
      width: 100%;
      display: inline-block;
      margin-bottom: 20px;

      .banner-text {
        color: #530F63;
        font-family: Roboto;
        font-weight: 500;
        font-size: 15px;
        line-height: 100%;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 8px;

        .star-icon {
          width: 16px;
          height: 16px;
        }
      }
    }

    .cta-container {
      margin-top: 20px;
      
      .schedule-button {
        background-color: #1976d2;
        color: white;
        font-family: Roboto;
        font-weight: 600;
        font-size: 16px;
        padding: 12px 24px;
        border-radius: 8px;
        text-transform: none;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        
        &:hover {
          background-color: #1565c0;
          box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
        }
      }
    }



  }
}

