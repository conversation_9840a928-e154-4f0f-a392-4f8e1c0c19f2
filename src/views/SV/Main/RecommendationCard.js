import React, { useEffect, useState } from "react";
import rootScopeService from "../../../services/rootScopeService";
import { connect, useSelector } from "react-redux";
import { CALL_API } from "../../../services";
import { useSnackbar } from "notistack";
import { setCreateBookingData, setIsCallCreateBooking } from "../../../store/actions/SalesView/SalesView";
import dayjs from "dayjs";
import { Tooltip } from "@mui/material";
import { SV_CONFIG } from "../../../appconfig";
import { currency } from "../../../utils/utility";


const RecommendationCard = (props) => {
    const { data, User, Plans, Suppliers, allLeads } = props;
    const [SelectionType, setSelectionType] = useState('');;
    const [customerInterest, setCustomerInterest] = useState(data.Rating);
    const { enqueueSnackbar } = useSnackbar();
    var invalids = [undefined, null, 0, "", "0"];
    let [SetManualStamping] = useSelector(({ salesview }) => [salesview.SetManualStamping]);



    // Get currency display for premium based on lead's CurrencyId
    const getCurrencyDisplay = (premium) => {
        if (premium == null || premium === undefined) return 'NA';
        
        // Get the current product ID
        const productId = rootScopeService.getProductId();
        
        // Only apply currency conversion for investment products (ProductId 115)
        if (productId === 115 && Array.isArray(allLeads)) {
            // Find the lead that matches this recommendation
            const lead = allLeads.find(lead => lead.LeadID === data.MatrixLeadId);
            
            // Show $ for CurrencyId 1, otherwise show ₹
            if (lead?.CurrencyId === 1) {
                return currency(premium);
            } else {
                return currency(premium, "INR");
            }
        }
        
        // For all other products, always show ₹
        return currency(premium, "INR");
    }

    useEffect(() => {
        SetSelectionType();
    }, []);

    const SetSelectionType = () => {
        if (data.SelectionType === 1) {
            setSelectionType('System Selection');
        }
        else if (data.SelectionType === 2) {
            setSelectionType('Agent Selection');
        }
        else if (data.SelectionBeforeCall && data.SelectionType === 3) {
            setSelectionType('Customer Selection');
        }
        else if (data.SelectionType === 4) {
            setSelectionType('UpSell');
        }
        else if (data.SelectionType === 3) {
            setSelectionType('BeforeCall ');
        }
        else if (data.SelectionType === 5) {
            setSelectionType('CRT Selection');
        }
        else if (data.SourcePage == "") {
            setSelectionType(data.SourcePage);
        }
    }

    const CreateOrder = (leadId, supplierId, planId, Premium, SumInsured) => {
        var subproductId = 1;
        // var plans = Plans.filter((item) => {
        //     return (
        //         item.ProductId === rootScopeService.getProductId() &&
        //         item.OldSupplierId === supplierId &&
        //         item.OldPlanId === planId
        //     )
        // });
        // if (plans != null && plans.length > 0) {
        //     subproductId = plans[0].SubProductTypeID;
        // }
        if (SumInsured === 0) {
            SumInsured = '';
        }
        props.setIsCallCreateBookingToRedux(true);
        props.setCreateBookingDataToRedux({ leadId: leadId, SupplierId: supplierId, PlanId: planId, SubProductTypeID: subproductId, Premium: Premium, SumInsured: SumInsured });
    }

    var leadCollection = useSelector(state => state.salesview.allLeads);


    const SaveRating = (recommendation, Index) => {

        var changedPR = [];
        setCustomerInterest(Index);
        changedPR.push({
            "RecId": recommendation.RecId,
            "PlanId": recommendation.PlanId,
            "OldPlanId": recommendation.OldPlanId,
            "PlanName": recommendation.PlanName,
            "ProductId": rootScopeService.getProductId(),
            "Rating": Index,
            "RequireDocCount": 0,
            "SelectionType": recommendation.SelectionType,
            "SupplierId": recommendation.SupplierId,
            "OldSupplierId": recommendation.OldSupplierId,
            "SupplierDisplayName": recommendation.SupplierDisplayName,
            "IsNew": true,
            "IsActive": true,
            "Premium": recommendation.Premium,
            "SumInsured": recommendation.SumInsured,
            "IsUpdate": recommendation.RecId > 0 ? true : false
        });

        props.SavePR(changedPR, recommendation.MatrixLeadId).then((response) => {
            if (response && response.IsSaved) {
                enqueueSnackbar("Rating Saved successfully", { variant: 'success', autoHideDuration: 3000, });
                //Todo need to check  for term SubProductType will zero
                let SubProductType = 1;
                props.getProductRecommendations();
            }

        }, function (reason) {
            console.log("Error saving PR.");
        });
        //Update LeadStatus  according to rating
        MarkLeadStatus(recommendation);
    }




    const MarkLeadStatus = (recommendation) => {
        let markstatus = 0;
        let Leads = "";
        leadCollection.forEach((vdata, key) => {
            if (vdata.StatusMode === 'P' && vdata.LeadID !== recommendation.MatrixLeadId) {
                Leads = leadCollection + vdata.LeadID + ',';
            }
        });

        //var IsStamping = rootScopeService.getManualStamping();
        //To do
        var IsStamping = SetManualStamping;
        if (recommendation.Rating > 4) {
            markstatus = 11;
        }
        else if (recommendation.Rating <= 2) {
            markstatus = 2;
        }
        else if (recommendation.Rating > 2)
            markstatus = 4;

        /*mark all leads contacted in case of interested or prospect markstatus*/
        if (recommendation.Rating > 2 && IsStamping && SV_CONFIG["UpdateLeadsStatusServiceExp"]==true) {

            var reqData = {
                Leads: Leads,
                StatusId: 3,
                SubStatusId: 0,
                UserID: User.UserId
            };
            const _input = {
                url: `LeadDetails/UpdateLeadsStatus`,
                method: 'POST',
                service: 'core',
                requestData: reqData
            }
            CALL_API(_input).then((resultData) => {
                //TODo
                //rootScopeService.setLeadRefresh();
            }, function () {

            });
        }
        /*mark all leads contacted end*/
        let data = leadCollection.filter((item) => item.LeadID == recommendation.MatrixLeadId);
        if (data != null && data !== undefined && data.length > 0) {
            if (markstatus <= data[0].StatusId) {
                markstatus = 0;
            }
        }

        if (markstatus !== 0 && IsStamping && SV_CONFIG["UpdateLeadsStatusServiceExp"]==true) {
            var reqData = {
                Leads: recommendation.MatrixLeadId,
                StatusId: markstatus,
                SubStatusId: 0,
                UserID: User.UserId
            };
            const _input = {
                url: `LeadDetails/UpdateLeadsStatus`,
                method: 'POST',
                service: 'core',
                requestData: reqData
            }
            CALL_API(_input).then((response) => {
                //Refresh leads
            });
        }

    }


    return (

        <div className="recomend-block">

            <p className={SelectionType.toLowerCase() == "customer selection" ? "customer-selection" : "Other-selection"}>{SelectionType}</p> 
            {(data.SelectionBeforeCall && data.SelectionType === 3 && data.ProductId === 7  && data.SourcePage && data.SourcePage.toLowerCase() == "wishlistselection") ? <p className="wishlist"> | &nbsp; WishList  </p>:""}            
            <div className="row">
                <div className="inner-block plan">
                    {data.SupplierDisplayName}<br />
                    ({data.PlanName})
                </div>
                {data.ProductId === 114 &&
                <div className="inner-block productPageInfo">
                    <p className="label"> Product Page Info</p>
                    <p className="date">{(data.SourcePage)? data.SourcePage : 'N.A'}</p>
                </div>
                }  
                <div className="inner-block select">
                    <p className="label">Selected on</p>
                    <p className="date">{dayjs(data.CreatedOn).format('DD/MM/YYYY h:mm:ss a')}</p>
                </div>
           
                <div className="inner-block interest">
                    <p className="heading">Customer’s Interest</p>
                    <div className="grey-block">
                        <span className={"icon one " + (customerInterest === 1 ? 'active' : '')} onClick={() => SaveRating(data, 1)}></span>
                        <span className={"icon two " + (customerInterest === 2 ? 'active' : '')} onClick={() => SaveRating(data, 2)}></span>
                        <span className={"icon three " + (customerInterest === 3 ? 'active' : '')} onClick={() => SaveRating(data, 3)}></span>
                        <span className={"icon four " + (customerInterest === 4 ? 'active' : '')} onClick={() => SaveRating(data, 4)}></span>
                        <span className={"icon five " + (customerInterest === 5 ? 'active' : '')} onClick={() => SaveRating(data, 5)}></span>
                    </div>
                </div>
                <div className="inner-block booking">
                    {!(SelectionType.toLowerCase() == 'crt selection') && <p className="heading">Premium<span className="premium">{getCurrencyDisplay(data.Premium)}</span></p>}
                    {!data.HasBooking && !(SelectionType.toLowerCase() == 'crt selection') && <a className="book" onClick={() => CreateOrder(data.MatrixLeadId, data.OldSupplierId, data.OldPlanId, data.Premium, data.SumInsured)}>Create Booking</a>}
                    {(SelectionType.toLowerCase() == 'crt selection') && (data.Remarks) && <Tooltip title={data.Remarks}><button>View Comments</button></Tooltip>}
                </div>
            </div>
        </div>

    )
}

const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {
    return {
        setCreateBookingDataToRedux: (data) => dispatch(setCreateBookingData({ CreateBookingData: data })),
        setIsCallCreateBookingToRedux: (value) => dispatch(setIsCallCreateBooking({ IsCallCreateBooking: value }))
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(RecommendationCard);

