import React, { useState } from "react";
import { useSelector } from "react-redux";
import { CONFIG } from "../../../appconfig";
import { Card, Typography, Chip, IconButton, Tooltip } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import "./MotorLeadSummaryCard.scss";

// Common constant for invalid/empty values
const INVALID_VALUES = ["NA", "N/A", "Not Available", null, ""];

const TruncatedText = ({ text, maxLength = 60 }) => {
    const textString = String(text || '');
    
    if (!textString || textString.length <= maxLength) {
        return <span>{textString}</span>;
    }
    
    const truncatedText = textString.slice(0, maxLength) + '...';
    
    return (
        <Tooltip title={textString} arrow placement="top">
            <span>{truncatedText}</span>
        </Tooltip>
    );
};

const renderFieldRow = (title, fieldValue) => {
    if (INVALID_VALUES.includes(fieldValue) || !fieldValue) return null;
    
    const fieldLabel = title.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    if (fieldLabel.toLowerCase() === 'premium') return null;
    
    // Handle nested objects
    if (typeof fieldValue === 'object' && fieldValue !== null) {
        const validEntries = Object.entries(fieldValue).filter(([key, value]) => 
            value && !INVALID_VALUES.includes(value)
        );
        
        if (validEntries.length === 0) return null;
        
        return (
            <div key={title} className="lead-summary-row">
                <Typography className="lead-summary-label">
                    {fieldLabel}
                </Typography>
                <Typography className="lead-summary-value">
                    {validEntries.map(([key, value], index) => (
                        <div key={index} style={{ marginBottom: index < validEntries.length - 1 ? '2px' : '0' }}>
                            <TruncatedText text={`${value} - ${key}`} maxLength={25} />
                        </div>
                    ))}
                </Typography>
            </div>
        );
    }
    
    return (
        <div key={title} className="lead-summary-row">
            <Typography className="lead-summary-label">
                {fieldLabel}
            </Typography>
            <Typography className="lead-summary-value">
                <TruncatedText text={fieldValue} />
            </Typography>
        </div>
    );
};

const renderSection = (sectionData, config) => {
    if (!sectionData || !Object.values(sectionData).some(value => value && !INVALID_VALUES.includes(value))) {
        return null;
    }

    const { className, title, titleClassName } = config;
   
    return (
        <div className={className}>
            {title && (
                <Typography className={titleClassName}>
                    {title}
                </Typography>
            )}
            {Object.keys(sectionData).map((fieldTitle) => 
                renderFieldRow(fieldTitle, sectionData[fieldTitle])
            )}
        </div>
    );
};

const CustomerPitchDetails = React.memo((props) => {
    const CustomerIntentsList = useSelector(({ salesview }) => salesview.CustomerIntentsList);

    if (!CustomerIntentsList || !CustomerIntentsList.lead_summary) {
        return null
    }
    const leadSummary = CustomerIntentsList.lead_summary;

    return <>
       
       <div className={props.notHelpfulForm ? "feedbackForm lead-summary-content" : "lead-summary-content"}>
            {props.notHelpfulForm && <p>Please let us know which Intent is incorrect</p>}

            {leadSummary["vehicle_details"] && Object.values(leadSummary["vehicle_details"]).some(value => value && !INVALID_VALUES.includes(value)) && (
                <div className="lead-summary-section">
                    {(
                        (leadSummary["vehicle_details"]["vehicle_type"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["vehicle_type"])) 
                        || (leadSummary["vehicle_details"]["manufacturing_year"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["manufacturing_year"])) 
                        || (leadSummary["vehicle_details"]["fuel_type"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["fuel_type"]))
                    ) && (
                        <div className="lead-summary-header">
                            <div>
                                {leadSummary["vehicle_details"]["vehicle_type"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["vehicle_type"]) && (
                                    <Typography className="lead-summary-title">
                                        {leadSummary["vehicle_details"]["vehicle_type"]}
                                    </Typography>
                                )}
                                <div className="lead-summary-chips">
                                    {leadSummary["vehicle_details"]["manufacturing_year"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["manufacturing_year"]) && (
                                        <Chip
                                            label={`${leadSummary["vehicle_details"]["manufacturing_year"]} Model`}
                                            size="small"
                                            className="lead-summary-chip"
                                        />
                                    )}
                                    {leadSummary["vehicle_details"]["fuel_type"] && !INVALID_VALUES.includes(leadSummary["vehicle_details"]["fuel_type"]) && (
                                        <Chip
                                            label={leadSummary["vehicle_details"]["fuel_type"]?.replace(/\//g, ' + ')}
                                            size="small"
                                            className="lead-summary-chip"
                                        />
                                    )}
                                </div>
                            </div>
                            <img src={CONFIG.PUBLIC_URL + "/images/TechnologyIcon.svg"} alt="technology" />
                        </div>
                    )}

                    {Object.keys(leadSummary["vehicle_details"])
                        .filter(title => !["vehicle_type", "manufacturing_year", "fuel_type"].includes(title))
                        .map((title) => renderFieldRow(title, leadSummary["vehicle_details"][title]))
                    }
                </div>
            )}

            {renderSection(leadSummary["policy_details"], {
                className: "lead-summary-section",
                title: "Policy Details",
                titleClassName: "lead-summary-section-title"
            })}

            {renderSection(leadSummary["customer_requirement"], {
                className: "customer-requirement-section",
                title: "Customer Requirement",
                titleClassName: "customer-requirement-title"
            })}

            {renderSection(leadSummary["customer_concerns"], {
                className: "customer-concerns-section",
                title: "Customer Concerns",
                titleClassName: "customer-requirement-title"
            })}
        </div>
    </>
})

const CardLayout = (props) => {
    return <Card className="motor-lead-summary-card">
        <div className="lead-summary-modal-header">
            <Typography className="lead-summary-modal-title">
                Lead Summary
            </Typography>
            <div className="lead-summary-modal-actions">
                {/* {!props.showBackButton &&
                    <img src={CONFIG.PUBLIC_URL + "/images/outbound.svg"} alt="outbound" onClick={() => { 
                        props.setCardState(views.SUMMARY) 
                        props.SetOpenCallwisePopup(true)
                    }} />
                } */}

                {props.showBackButton &&
                    <IconButton
                        size="small"
                        className="lead-summary-modal-icon"
                        onClick={() => {props.setCardState(views.SUMMARY) }}
                    >
                        <CloseIcon fontSize="small" />
                    </IconButton>
                }
            </div>
        </div>
        {props.children}
    </Card>
}

const MotorLeadSummaryCard = () => {
    const [cardState, setCardState] = useState(views.SUMMARY);
    const [openCallwisePopup, SetOpenCallwisePopup] = useState(false);
    return <div>
        <CardLayout showBackButton={cardState !== views.SUMMARY} setCardState={setCardState} SetOpenCallwisePopup={SetOpenCallwisePopup}>
            {cardState === views.FEEDBACK && <>
                <CustomerPitchDetails notHelpfulForm />
            </>}    
            {cardState === views.SUMMARY && <>
                <ul>
                    <CustomerPitchDetails />
                </ul>
            </>}
        </CardLayout >
    </div>
}

export default MotorLeadSummaryCard;

const views = {
    SUMMARY: "SUMMARY",
    SUBMIT_FEEDBACK: "SUBMIT_FEEDBACK"
}