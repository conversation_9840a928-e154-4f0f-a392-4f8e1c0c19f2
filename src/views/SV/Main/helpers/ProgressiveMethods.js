import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import pluck from 'underscore/modules/pluck';
import User from "../../../../services/user.service";
import { CALL_API } from "../../../../services";
import rootScopeService from "../../../../services/rootScopeService";
import { connect } from 'react-redux'
import { setConnectedLeadsData, setIsAnswered, setIsAutoDone, setNext5LeadsData, setRefreshAgentStats, setRefreshLead, updateStateInRedux, setCallType } from "../../../../store/actions/SalesView/SalesView";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import Common from "./Common";
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import CloseIcon from '@mui/icons-material/Close';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import { useSnackbar } from "notistack";
import { useInterval } from "./useInterval";
import { SV_CONFIG } from "../../../../appconfig";
import masterService from "../../../../services/masterService";
import { gaEventTracker, isNonProgressiveWfhCalling } from "../../../../helpers";
import { AddLeadToPriorityQueueService, GetCustomerComment, connectCallSFservice, UpdateCallStatusService, IsAswatUser, IsCustomerAccess, matrixLogout, SaveCallIntent,SetCustomerComment } from "../../../../services/Common";
import { localStorageCache } from "../../../../utils/utility";
import { GetOneLeadService, CheckIsMonthlyModeTeam } from "../../../../services/Common";
import { getSearchObjFromLS, isRecentIBCall, SetLastCalledLead, pushRecentIBLeadId, IsVCActive } from "../../../../helpers/commonHelper";
import { Grid } from "@mui/material";
import {CallDispositionReasonPopup } from "../../Main/Modals/CallDispositionReasonPopup";

const Modals = {
    AswatSelection: "AswatSelection"
}

const ProgressiveComponent = (props) => {
    //const { next5leads } = props;
    const [IsRefreshPage, setIsRefreshPage] = useState(false);
    const [ProgressiveScheduler, setProgressiveSchduler] = useState(5000);
    const [count, setCount] = useState(0);
    const [next5leads, setNext5leads] = useState([]);
    const [InSchedular, setInSchedular] = useState(window.localStorage.getItem("InSchedular") == "true" ? true : false);
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();
    const [processLead, setProcessLead] = useState(null);
    const [ConnectedLead, setConnectedLead] = useState(null);
    const [AnsweredLead, setAnsweredLead] = useState(null);
    const [NotAnsweredLead, setNotAnsweredLead] = useState(null);
    const [customerComment, setCustomerComment] = useState([]);
    const [nextAttemptNo, setNextAttemptNo] = useState(false);
    const [nextAttemptSkipDuration, setNextAttemptSkipDuration] = useState(1);
    const [nextAttemptSkipTO, setnextAttemptSkipTO] = useState(null);
    const [AttemptExceedMessage, setAttemptExceedMessage] = useState();
    //var initNextCall = 0;
    const [initNextCall, setinitNextCall] = useState(0);
    const next5leadsfromRedux = useSelector(state => state.salesview.next5leads);
    const resultData = useSelector(state => state.salesview.AgentStats);
    const logoutInProcess = useSelector(state => state.salesview.logoutInProcess);
    const callOnCurrentLead = useSelector(state => state.salesview.callOnCurrentLead);
    const isAsteriskTokenVerified = useSelector(state => state.salesview.isAsteriskTokenVerified)
    const CommentsPopUp = useSelector(state => state.salesview.CommentsPopUp.show);
    // const CommentsAdded = useSelector(state => state.salesview.CommentsAdded);
    let [CommentsAdded, SubStatusAdded, SubStatusPopUp, Leadsexpiry, PlanPitchPopup, NonPaymentReasonPopUp, LastCallDuration] = useSelector(state => {
        let { CommentsAdded, SubStatusAdded, SubStatusPopUp, Leadsexpiry, PlanPitchPopup,NonPaymentReasonPopUp, LastCallDuration } = state.salesview;
        return [CommentsAdded, SubStatusAdded, SubStatusPopUp, Leadsexpiry, PlanPitchPopup, NonPaymentReasonPopUp, LastCallDuration]
    });
    const dispatch = useDispatch();
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const agentdata = resultData[0];

    const ProductId = rootScopeService.getProductId();
    const [AnsweredTimer, setAnsweredTimer] = useState(null);
    const IsAutoDone = useSelector(state => state.salesview.IsAutoDone);
    const parentLeadId = useSelector(state => state.salesview.parentLeadId);
    const AgentIBNumber_wfhNew = useSelector(state => state.salesview.AgentIBNumber_wfhNew);
    // const CallType = useSelector(state => state.salesview.CallType);

    //title, actions, open, handleClose, children
    const [modalTitle, setModalTitle] = useState(null);
    const [modalActions, setModalActions] = useState(null);
    const [modalOpen, setModalOpen] = useState(false);
    // const [modalChildren, setModalChildren] = useState(null);
    const [modalType, setModalType] = useState(null);
    const [donotShowAswatPopupUntil, setDonotShowAswatPopupUntil] = useState(new Date().getTime());
    const [opensvByAPICalled, setopensvByAPICalled] = useState(false);
    const [InAPICall, setInAPICall] = useState(false);
    const [servertimeup, setServerTimeUp] = useState(false);
    // const [OneLeadCallingNumbers, setOneLeadCallingNumbers] = useState({});
    const [ThirdPartyTimeOut, setThirdPartyTimeOut] = useState(0);
    const [predictiveDialParams, setPredictiveDialParams] = useState({ lead: null, IsAttempt: null, IsCheckPrimary: null });
    const Ispriority = rootScopeService.getPriority();
    const allLeads = useSelector(state => state.salesview.allLeads);
    const [micPermGiven, setMicPermGiven] = useState(false);
    const [micOffLogout, setMicOffLogout] = useState(false);


    var answerRestTime = 60000;

    const getLeads = () => {
        setNext5leads(next5leadsfromRedux && next5leadsfromRedux != undefined ? next5leadsfromRedux : []);
    }
    useInterval(() => {
        if (!User.IsWFH && !logoutInProcess
            && (User.CallingCompany !== "WFH" || SV_CONFIG.WEBPHONE_WFH_CASE)
            // && Ispriority && User.IsProgressive
            // && !IsAswatUser()
        ) {
            try {
                if (localStorageCache.readFromCache('isTokenVerified') === null) {
                    localStorageCache.writeToCache('isTokenVerified', true, 60 * 1000);
                }
                if (micPermGiven) {
                    window.ShowCallWindow(User.EmployeeId);
                }
            }
            catch { }
        };
        const isWfhNew = Common.isUserWfhNew(User);
        if (User.IsWFH && isWfhNew) {
            if (!(AgentIBNumber_wfhNew && AgentIBNumber_wfhNew > 0)) {
                getAgentIbNumberService(User.EmployeeId).then((res) => {
                    if (res && res.status) {
                        dispatch(updateStateInRedux({ key: "AgentIBNumber_wfhNew", value: res.data }));
                    }
                })
            }
        }
    }, 10000);

    useEffect(() => {
        if (callOnCurrentLead) {
            manualDial()
            dispatch(updateStateInRedux({ key: "callOnCurrentLead", value: false }));
        }
    }, [callOnCurrentLead])
    useEffect(() => {
        getLeads();
        let indexConnected = -1;
        let indexAnswered = -1;
        let indexNotAnswered = -1;

        let leads = next5leadsfromRedux;

        var items = pluck(leads, 'CallStatus');
        indexAnswered = items.indexOf('Answered');
        indexNotAnswered = items.indexOf('NotAnswered');
        indexConnected = items.indexOf('Connected');

        if (indexConnected > -1) {
            setConnectedLead(leads[indexConnected]);
            props.setConnectedLeadToRedux(leads[indexConnected]);
            // setConnectedLead(leads);
        } else {
            setConnectedLead(null);
        }
        if (indexAnswered > -1) {
            props.setIsAnsweredToRedux(true);
            setAnsweredLead(leads[indexAnswered]);
        } else {
            setAnsweredLead(null);
        }
        if (indexNotAnswered > -1) {
            setNotAnsweredLead(leads[indexNotAnswered]);
        } else {
            setNotAnsweredLead(null);
        }


    }, [next5leadsfromRedux])

    const validateWebphoneConditions = () => {
        let isRegistered = window.checkforRegistraion ? window.checkforRegistraion() : false;

        if (!isRegistered || isRegistered === "false") {
            console.log("Un-Registered");
            enqueueSnackbar("Webphone is not registered yet", { variant: 'warning', autoHideDuration: 3000 });

            // LeadSchedular = false;
            // localStorage.setItem("ActiveSchdular", "0");
            return false;
        }
        else if (window.IsMicrophoneEnabled && !window.IsMicrophoneEnabled()) {
            enqueueSnackbar("Please allow Microphone to enable calls", { variant: 'error', autoHideDuration: 3000, });
            //localStorage.setItem("ActiveSchdular", "0");
            return false;
        }
        return true;
    }

    const manualDial = () => {
        // currently using for non-progressive user, via call btn
        var LeadData = next5leadsfromRedux.find(function (x) {
            return (x.LeadId === parentLeadId)
        })
        if (LeadData) {
            const lead = {
                "LeadId": parentLeadId,
                // "Name": LeadData.Name,
                // "CustomerId": LeadData.CustomerID,
                // "UserID": User.UserId,
                // "Priority": 1,
                // "ProductId": LeadData.ProductID,
                // "Reason": 'manualDial',
                // "ReasonId": 32,
                // "CallStatus": "",
                "IsAddLeadtoQueue": 0,
                "IsNeedToValidate": 1
            }
            // if lead is present in queue, call directly
            AddLeadToPriorityQueueService(lead).then((resultData) => {
                if (resultData != null) {
                    if (resultData && resultData.message && resultData.message == "Success" && resultData.status > 0) {
                        predictiveDial(LeadData, false, false);
                    }
                    else {
                        let error = (resultData && resultData.message && resultData.message != '')
                            ? (resultData.LeadID + " : " + resultData.message)
                            : "Cannot call on this lead!: " + resultData.LeadID;
                        enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                    }
                }
            });
        }
        else {
            // if lead is not present in queue, add to queue and initiate call
            const leadData = allLeads.length > 0 ? allLeads[0] : null;
            if (leadData === null) {

                enqueueSnackbar("No lead opened to call!", { variant: 'error', autoHideDuration: 2000, });
                return;
            }
            const priorityLead = {
                "LeadId": leadData.ParentID,
                "Name": leadData.Name,
                "CustomerId": leadData.CustomerID,
                "UserID": User.UserId,
                "Priority": 1,
                "ProductId": leadData.ProductID,
                "Reason": 'Manual added',
                "ReasonId": 32,
                "CallStatus": "",
                "IsAddLeadtoQueue": 1,
                "IsNeedToValidate": 1
            }
            AddLeadToPriorityQueue(priorityLead).then((resultData) => {
                if (resultData != null) {
                    if (resultData && resultData.message && resultData.message == "Success") {
                        predictiveDial(priorityLead, false, false);
                    }
                    else {
                        let error = (resultData && resultData.message && resultData.message != '')
                            ? (resultData.LeadID + " : " + resultData.message)
                            : "Unable to add lead in priority:  " + resultData.LeadID;
                        enqueueSnackbar(error, { variant: 'error', autoHideDuration: 2000, });
                    }
                }
            })

        }

    }

    const handleSetInSchedular = (val) => {
        window.localStorage.setItem("InSchedular", val);
        setInSchedular(val);
    }

    const SchedularContentCustomerAccess = () => {
        let leads = next5leads;

        if (!(leads && leads.length > 0)) {
            return;
        }

        var items = pluck(leads, 'CallStatus');
        let indexInitiated = items.indexOf('CallInitiated');
        let indexConnected = items.indexOf('Connected');

        var sinitNextCall = sessionStorage.getItem('initNextCall') ? parseInt(sessionStorage.getItem('initNextCall')) : 1;
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

        if (!onCall && (initNextCall > sinitNextCall)) {
            // cases where callStatus is present but call is not connected for 10-15 seconds
            var callindex = -1;
            let status = " ";
            let lead = null;

            if(indexInitiated > -1){
                status = " "
                callindex = indexInitiated;
                lead = leads[callindex];
                if (lead.Reason && lead.Reason.toLowerCase().indexOf('inbound') > -1) {
                    // mark IB call "Answered" ... 
                    // IB call status might not get updated from backend
                    // and " "/null makes a OB call on same lead
                    status = "Answered";
                }
            }
            if(indexConnected > 0){
                // mark "Answered", to prevent dual call issue
                callindex = indexConnected;
                status = "Answered";
            }
            if(callindex >= 0){
                lead = leads[callindex];
                lead.CallStatus = status;
                // console.log("UpdateCallStatus: ", leads[callindex].LeadId, " ", User.UserId)
                UpdateCallStatus(leads[callindex].LeadId, status, User.UserId);
                setinitNextCall(0);
            }
        }
        else {
            //initNextCall = initNextCall + 1;
            setinitNextCall(prev => prev + 1)
        }
    }

    const SchedularContent = () => {
        let leads = next5leads;

        if (!(leads && leads.length > 0)) {
            handleSetInSchedular(false);
            return;
        }


        console.log("ProgressiveLeads: ", next5leads);

        let items = pluck(leads, 'CallStatus');
        const indexAnswered = items.indexOf('Answered');
        const indexInitiated = items.indexOf('CallInitiated');
        const indexNotAnswered = items.indexOf('NotAnswered');
        const indexConnected = items.indexOf('Connected');
        let RevisitPaymentCallback = false;
        let sendLead = true;


        const callInitiatedAtTime = items.filter(function (x) {
            return x === 'CallInitiated'
        }).length;
        const totalStatus = items.filter(function (x) {
            return x
        }).length;

        const sinitNextCall = sessionStorage.getItem('initNextCall') ? parseInt(sessionStorage.getItem('initNextCall')) : 1;
        const onCall = window.localStorage.getItem("onCall") === "true" ? true : false;


        if (!onCall && (initNextCall > sinitNextCall)) {
            // cases where callStatus is present but call is not connected for 10-15 seconds
            var callindex = -1;
            let status = " ";
            let lead = null;

            if(indexInitiated > -1){
                status = " "
                callindex = indexInitiated;
                lead = leads[callindex];
                if (lead.Reason && lead.Reason.toLowerCase().indexOf('inbound') > -1) {
                    // mark IB call "Answered" ... 
                    // IB call status might not get updated from backend
                    // and " "/null makes a OB call on same lead
                    status = "Answered";
                }
            }
            if(indexConnected > 0){
                // mark "Answered", to prevent dual call issue
                callindex = indexConnected;
                status = "Answered";
            }
            if(callindex >= 0){
                lead = leads[callindex];
                lead.CallStatus = status;
                // console.log("UpdateCallStatus: ", leads[callindex].LeadId, " ", User.UserId)
                UpdateCallStatus(leads[callindex].LeadId, status, User.UserId);
                setinitNextCall(0);
            }
        }
        else {
            //initNextCall = initNextCall + 1;
            setinitNextCall(prev => prev + 1)
        }


        if ((totalStatus !== leads.length) && !RevisitPaymentCallback) {
            sendLead = true;
        }


        if (indexConnected > -1) {
            sendLead = false;
        }
        if (indexInitiated > -1 && onCall) {
            sendLead = false;
        }

        if (indexInitiated < 0 && indexConnected < 0 && indexAnswered < 0 && indexNotAnswered < 0) {
            sendLead = true;
        }

        setCount(count + 1);
        // console.log('sendLead', sendLead)
        if (sendLead) {
            if (indexConnected >= 0) {
                try {
                    gaEventTracker("DUAL_CALL_CONNECTED_LEAD", JSON.stringify({ event: "Inside sendLead", connected: leads[indexConnected], empId: User.EmployeeId }), "DUAL_CALL_0");
                }
                catch { }
            }

            try {
                if (SV_CONFIG.enableCallFullLogs) {
                    gaEventTracker("CALL_FULL_LOGS", JSON.stringify({ event: "Inside sendLead", leads, ts: new Date() }), User.EmployeeId);
                }
            }
            catch { }

            if (indexNotAnswered > -1 && indexConnected < 0 && indexAnswered < 0 && indexInitiated < 0) {
                if (leads[indexNotAnswered].Reason.toLowerCase().indexOf("back") > -1 && leads[indexNotAnswered].Reason.toLowerCase().indexOf("2") > -1) {
                    sendLead = false;
                    RevisitPaymentCallback = true;
                    var event = {
                        data: {
                            message: 'RevisitPaymentCallback',
                            msg: 'RevisitPaymentCallback',
                            lead: leads[indexNotAnswered]
                        }
                    }

                    ProgressiveFeatures(event);

                } else {
                    //Rebind
                    GetOneLead(User.UserId, ProductId).then((result) => {
                        getNextLeads()
                    })
                }
            }
            // End Case
            else if (totalStatus === leads.length && indexConnected < 0 && indexInitiated < 0 && indexAnswered < 0) {
                sendLead = false;

                //queueLogin();
                //Rebind
                GetOneLead(User.UserId, ProductId).then((result) => {
                    getNextLeads()
                })
                handleSetInSchedular(false);
                return;
            }
            // Call Initiate
            else if (leads !== null && leads.length > 0 && callInitiatedAtTime < 1 && indexAnswered < 0 && (SV_CONFIG.CallWhenConnected || indexConnected < 0)) {
                setinitNextCall(0);
                props.setIsAutoDoneToRedux(false);
                var PriorityLeads = leads.filter(function (x) {
                    return (x.CallStatus === null || x.hasOwnProperty("CallStatus") === false || x.CallStatus === "") && x.Priority > 0
                })
                // Priority Call
                if (PriorityLeads.length > 0) {
                    var event = {
                        data: {
                            message: 'predictiveDial',
                            msg: 'CallInitiated',
                            lead: PriorityLeads[0],
                            pid: Math.random()
                        }
                    }

                    ProgressiveFeatures(event);


                    setCount(0);
                    handleSetInSchedular(false);
                    if (indexConnected >= 0) {
                        try {
                            gaEventTracker("DUAL_CALL_CONNECTED_LEAD", JSON.stringify({ event: "Called on priority lead", connected: leads[indexConnected], nowCalling: PriorityLeads[0]?.LeadId, empId: User.EmployeeId }), "DUAL_CALL_1");
                        }
                        catch { }
                    }
                    return;
                }
                // Normal Calls
                else {

                    for (var element of leads) {

                        if (element.hasOwnProperty("EmailOnly") && element.EmailOnly === true) {
                            window.localStorage.setItem("IsEmailOnly", true);
                            //IsEmailOnlyTime = window.localStorage.setItem("IsEmailOnlyTime", new Date().getTime());
                            handleSetInSchedular(false);
                            return;
                        } else {
                            window.localStorage.setItem("IsEmailOnly", false);
                        }

                        if (!element.hasOwnProperty('CallStatus') || element.CallStatus === "" || element.CallStatus === null) {
                            var event = {
                                data: {
                                    message: 'predictiveDial',
                                    msg: 'CallInitiated',
                                    lead: element,
                                    pid: Math.random()
                                }
                            }
                            ProgressiveFeatures(event);

                            setCount(0);
                            handleSetInSchedular(false);

                            if (indexConnected >= 0) {
                                try {
                                    gaEventTracker("DUAL_CALL_CONNECTED_LEAD", JSON.stringify({ event: "Called on normal lead", connected: leads[indexConnected], nowCalling: element, empId: User.EmployeeId }), "DUAL_CALL_2");
                                }
                                catch { }
                            }

                            return;
                        }
                    }
                }
            } else {
                if (count > 8 && callInitiatedAtTime > 0) {
                    sendLead = true;
                    setCount(0);
                }
            }
        }
    }

    const RunProgressiveSchedular = (next5leads) => {
        // let InSchedularSS = window.localStorage.getItem("InSchedular") === "true" ? true : false;
        // if (InSchedularSS) {
        //     return;
        // }

        //console.log("RunProgressiveSchedular called at ", new Date());

        let onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

        if (!isAsteriskTokenVerified) {
            // Authentication failed
            if (!onCall) {
                // if user is not on call, logout user
                if (agentdata && agentdata.AsteriskToken == "123456789") {
                    window.alert("You have been logged out from the system. Please login again");
                }
                else {
                    window.alert("Session is already in progress for provided username, please logout and re-login to continue");
                }

                // logout from salesview 
                dispatch(updateStateInRedux({ key: "logoutInProcess", value: true }));
                setTimeout(() => {
                    try {
                        window.AgentCall.popwin.WindowLogout();
                    }
                    catch (e) { }
                    window.localStorage.clear();
                    window.location.href = "/PGV/Login.aspx";
                }, 1000);
                return;
            }
            else {
                // if user is on call, show a message.. 
                let message = "Seems like you have logged in from another device, functionalities will not work";
                if (agentdata && agentdata.AsteriskToken == "123456789") {
                    message = "You have been logged out from the system. functionalities will not work";
                }
                enqueueSnackbar(message, {
                    variant: "default",
                    persist: true,
                    className: 'LogoutMsgAlert',
                    anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'center'
                    },
                })
            }
        }


        let _serverTimeUp = false;
        if (agentdata && (agentdata.isCallAllowed == true || agentdata.isCallAllowed == "true")) {
            setServerTimeUp(false);
            _serverTimeUp = false;
        }
        else {
            // setServerTimeUp(true);
            //if (AppConfig.servertimeup.Groups.indexOf($scope.User.GroupId) > -1 || AppConfig.servertimeup.EmployeeId.indexOf($scope.User.EmployeeId) > -1) {
            if (SV_CONFIG.servertimeup.Groups.indexOf(User.GroupId) > -1) {
                setServerTimeUp(true);
                _serverTimeUp = true;
            }
            else {
                setServerTimeUp(false);
                _serverTimeUp = false;
            }
        }

        if (_serverTimeUp === true) {
            return;
        }


        //If agent is on videocall then return;
        if (localStorage.getItem("OnVideoCall") === "1" 
            || (agentdata && agentdata.status && agentdata.status.toUpperCase()==='VIDEOMEET')
            || IsVCActive(20)
        ) {
            enqueueSnackbar('VideoCall mode is Active', {
                variant: 'info', autoHideDuration: 3000
            });
            return;
        }

        // If FOS Self agents are on field appointment then return;
        if(agentdata && agentdata.status && agentdata.status.toUpperCase() === "FOS_APPOINTMENT") {
            return;
        }

        //If agent Company is NewWFH and statusUnavailable then return;
        if ((Common.isUserWfhNew(User)) && localStorage.getItem("AgentUnavailable") === "1") {
            enqueueSnackbar('Please call on your assigned number', {
                variant: 'error', autoHideDuration: 3000
            });
            return;
        }
        if ((Common.isUserWebphoneNew(User)) && localStorage.getItem("AgentUnavailable") === "1") {
            enqueueSnackbar('Webphone is not connected yet! Please wait!', {
                variant: 'warning', autoHideDuration: 3000
            });
            return;
        }
        // console.log("Entered");
        // console.log(next5leads);
        var indexConnected = -1;
        var indexAnswered = -1;
        var indexInitiated = -1;
        var indexNotAnswered = -1;

        let leads = next5leads;

        var items = pluck(leads, 'CallStatus');
        indexAnswered = items.indexOf('Answered');
        indexInitiated = items.indexOf('CallInitiated');
        indexNotAnswered = items.indexOf('NotAnswered');
        indexConnected = items.indexOf('Connected');



        if (indexConnected > -1) {
            setConnectedLead(leads[indexConnected]);
            // setConnectedLead(leads);
        } else {
            setConnectedLead(null);
        }
        if (indexAnswered > -1) {
            setAnsweredLead(leads[indexAnswered]);
        } else {
            setAnsweredLead(null);
        }
        if (indexNotAnswered > -1) {
            setNotAnsweredLead(leads[indexNotAnswered]);
        } else {
            setNotAnsweredLead(null);
        }

        let countValidCallStatus =
            [indexAnswered, indexInitiated, indexNotAnswered, indexConnected].filter(item => item > -1).length;

        if (countValidCallStatus > 1) {
            // openMultipleCallPopup();
            props.setRefreshLeadToRedux();
        }

        //console.log("onCall", onCall);


        handleSetInSchedular(true);

        let IsPause = window.localStorage.getItem("IsPause") === "true" ? true : false;
        if (IsPause) {
            handleSetInSchedular(false);
            return;
        }

        var IsEmailOnlyTime = window.localStorage.getItem("IsEmailOnlyTime");
        if (
            window.localStorage.getItem("IsEmailOnly") === "true" &&
            IsEmailOnlyTime &&
            ((new Date()).getTime() - IsEmailOnlyTime) < 120000
        ) {
            handleSetInSchedular(false);
            return;
        }

        if (User.IsWFH === false && !Common.isUserWebphoneNew(User)) {
            if (!validateWebphoneConditions()) {
                return;
            }
        }
        else {
            dialerAPI_getLeadIdService().then((res) => {
                if (res.status === 200) {
                    window.localStorage.setItem("onCall", true);
                    if (res.data && res.data.customerAnswered) {
                        // console.log("showFeedback");
                        //$scope.showFeedback();
                    }

                    if (res.data && res.data.customerAnswered && (res.data.leadId !== 0 || res.data.leadId !== "0") && indexConnected === -1) {
                        UpdateCallStatus(res.data.leadId, "Connected", User.UserId);
                        // OpenConnectedLead(leads);
                        localStorage.setItem("isShowDoneBtn", false)
                    }

                    let ServiceLead = { leadId: res.data.leadId, callType: res.data.callType }
                    window.localStorage.setItem("ServiceLead", JSON.stringify(ServiceLead));
                    window.localStorage.setItem("dialerAPI_getLeadId", JSON.stringify(res));
                    window.localStorage.setItem("CallStatus", res.data.status);
                    window.localStorage.setItem("OnCallLeadId", res.data.leadId);
                    handleSetInSchedular(false);
                    props.setCallTypeToRedux(res.data.callType);

                    return;
                }
                else if (res.status === 0) {
                    window.localStorage.setItem("onCall", true);
                    window.localStorage.setItem("ServiceLead", null);

                    enqueueSnackbar("There is some connectivity issue, Kindly check your internet.", { variant: 'error', autoHideDuration: 2000, });
                    window.localStorage.setItem("dialerAPI_getLeadId", JSON.stringify(res));
                    handleSetInSchedular(false);

                    return;
                }
                else if (res.status === 404) {
                    window.localStorage.setItem("onCall", false);
                    window.localStorage.setItem("ServiceLead", null);
                    if (indexConnected > -1) {
                        var dialerAPI_getLeadId = (localStorage.getItem("dialerAPI_getLeadId") == null || localStorage.getItem("dialerAPI_getLeadId") == "") ? {} : JSON.parse(localStorage.getItem("dialerAPI_getLeadId"));

                        if (dialerAPI_getLeadId.status === 200 && dialerAPI_getLeadId.data && dialerAPI_getLeadId.data.customerAnswered) {
                            UpdateCallStatus(dialerAPI_getLeadId.data.leadId, "Answered", User.UserId);

                        }
                        else if (dialerAPI_getLeadId.status === 200 && dialerAPI_getLeadId.data && dialerAPI_getLeadId.data.customerAnswered != true) {
                            UpdateCallStatus(dialerAPI_getLeadId.data.leadId, "NotAnswered", User.UserId);

                        }
                    }
                    console.log("connectCallSF -> removed from localstorage inside dialerAPI_getLeadIdService at 477", new Date(), "");
                    localStorage.setItem("dialerAPI_getLeadId", JSON.stringify(res));
                    
                    localStorage.removeItem('ConnectCallSF');

                }

                // SchedularContent();
            });


        }
        if (onCall) {
            localStorage.setItem("isShowDoneBtn", false);
            handleSetInSchedular(false);
            return;
        }
        else {
            // show icon for new calls
            localStorage.setItem("ShowDTMFIcon", true);
        }

        var dispositionCode = window.localStorage.getItem("dispositionCode");
        if ((dispositionCode === "403" || dispositionCode === "503") && window.AgentCall.popwin && !window.AgentCall.popwin.oSipSessionCall) {
            window.AgentCall.popwin.sipRegister();
            setTimeout(function () {
                window.localStorage.setItem("dispositionCode", "");
                localStorage.setItem("ActiveSchdular", "0");
            }, 1500);
            handleSetInSchedular(false);
            return;
        }
        if (User.IsProgressive) {
            SchedularContent();
        } else if (IsCustomerAccess()) {
            SchedularContentCustomerAccess();
        }

    }

    const ProgressiveFeatures = (event) => {
        console.log("ProgressiveFeatures event : ", event);
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
        let IsPause = window.localStorage.getItem("IsPause") === "true" ? true : false;
        switch (event.data.message) {
            case "predictiveDial":
                // Check for Call Session Exist or Not
                if (!onCall) {
                    var AnswerCallTime = window.localStorage.getItem('AnswerCallTime')
                    if (AnswerCallTime != "") {
                        AnswerCallTime = new Date(AnswerCallTime).getTime();
                        if (((new Date()).getTime() - AnswerCallTime) / 1000 < 30) {
                            AutoDone();
                            localStorage.setItem("isShowDoneBtn", true);
                            return;
                        }
                    }


                    // Method for Execute Call
                    predictiveDial(event.data.lead, false, false);
                    setTimeout(function () {
                        getNextLeads()
                    }, 1000)
                }
                break;
            case "NoLeadFound":
                if (!onCall) {
                    AutoDone();
                    localStorage.setItem("isShowDoneBtn", true);
                    // queueLogin();
                }
                break;
            case "AutoDone":
                if (!onCall) {
                    AutoDone();
                    // window.localStorage.setItem("onCall", false);
                    localStorage.setItem("isShowDoneBtn", true);
                    if (!AnsweredTimer) {
                        // console.log("Flow1: _AnsweredTimer Start: ", new Date());
                        let _AnsweredTimer = setTimeout(function () {
                            // console.log("Flow1: _AnsweredTimer Inside: ", new Date());

                            if (!IsPause) {
                                GetOneLead(User.UserId, ProductId);
                            }
                            window.localStorage.removeItem('AnswerCallTime');
                            //queueLogin();
                            GetOneLead(User.UserId, ProductId).then((result) => {

                                getNextLeads()
                            })
                            setAnsweredTimer(null);
                            localStorage.setItem("isShowDoneBtn", true)
                        }, 60000);
                        setAnsweredTimer(_AnsweredTimer);
                    }
                }
                break;
            case "RevisitPaymentCallback":
                if (!onCall) {
                    showRevisitPaymentCallBack(event.data);
                }
                break;
            default:
                break;

        }

    }

    const showRevisitPaymentCallBack = function (event) {
        if (!modalOpen) {
            setModalOpen(true);
            setModalType("RevisitPaymentCallBack");
            setModalTitle("");
            setTimeout(function () {
                GetOneLead(User.UserId, ProductId);
                setModalOpen(false);
            }, 30000);
        }
    }
    const showAswatSelectionPopup = (lead, IsAttempt, IsCheckPrimary) => {
        const currTime = new Date().getTime();
        if (!modalOpen && currTime > donotShowAswatPopupUntil) {
            setPredictiveDialParams({
                lead, IsAttempt, IsCheckPrimary
            })
            setModalType(Modals.AswatSelection);
            setModalOpen(true);
            setModalTitle("");
        }
    }
    const checkAswatPopupResponse = (lead, useAswatPhone) => {
        if (!IsAswatUser()) return false;

        if (IsAswatUser()
            && modalOpen == true
            && modalType == Modals.AswatSelection
            && lead.ReasonId == 32
        ) {
            // close popup if scheduler runs again and popup is open,
            // call via normal webphone
            setModalOpen(false);
            setModalType(null);
            setDonotShowAswatPopupUntil(new Date().getTime() + 6 * 1000);
            // setTimeout(() => {
            //     // timeout, so that popup do not open again in 
            //     // cases where popup is closed just before the next 
            //     // scheduler cycle
            //     setModalType(null);
            // }, 3000);
            useAswatPhone = useAswatPhone || false;
        }
        if (IsAswatUser() && useAswatPhone && !window.checkForAswatRegistration()) {
            enqueueSnackbar("Aswatphone not registered, calling via webphone.", { variant: 'warning', autoHideDuration: 3000 });
            useAswatPhone = false;
        }
        return useAswatPhone;
    }

    const predictiveDial = (lead, IsAttempt, IsCheckPrimary, useAswatPhone = null) => {

        if (lead) {
            const revisitPopupProdIds = Array.isArray(SV_CONFIG.revisitPopupProdIds) ? SV_CONFIG.revisitPopupProdIds : [];
            if (revisitPopupProdIds.includes(ProductId) && lead.Reason.toLowerCase().includes("revisit") && !lead.Reason.toLowerCase().includes("2") && IsAttempt === false) {
                nextAttemptModalOpen(lead.Name + "( " + lead.LeadId + " ) is revisit customer, Do you want to call this?", lead, 3);
            }
            else if (IsAswatUser()
                && modalType !== Modals.AswatSelection
                && modalOpen != true
                && useAswatPhone == null
                && lead.ReasonId == 32
            ) {
                showAswatSelectionPopup(lead, IsAttempt, IsCheckPrimary);
            }
            else {
                useAswatPhone = checkAswatPopupResponse(lead, useAswatPhone);
                if (IsCheckPrimary == true || !modalOpen) {
                    CheckPrimaryMob(lead, true);
                }
                if (User.IsWFH || Common.isUserWebphoneNew(User)) {
                    //if (localStorage.getItem("callingapicalled") == "0") {
                    dialerAPI_getLeadIdService()
                        .then((result) => {
                            if (result && result.status === 404) {
                                // localStorage.setItem("callingapicalled", "1");
                                //connectCall(lead, false); 
                                connectCall(lead, IsAttempt);
                            }
                        });
                }
                // }
                else {
                    connectCall(lead, IsAttempt, useAswatPhone);
                }
            }
        }
    }
    const thirdPartyTimeoutFn = (ConnectCallSFdata, timeoutType = 1) => {
        sessionStorage.setItem('initNextCall', 3);
        if (timeoutType === 1) {
            // type = 1, in case 3rd party calling api fails initiate call from webphone 

            _ThirdPartyTimeOut = setTimeout(function () {
                let onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
                if (!onCall) {
                    ConnectCallSFdata.IsThirdParty = false;
                    window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFdata));
                    // if (User.CallingCompany === "")
                    try {
                        window.ConnectCall(User.EmployeeId, ConnectCallSFdata.Phone, ConnectCallSFdata);
                    } catch (e) {
                        console.error(e)
                    }
                }
            }, 8000);
        }
        else {
            // type = 2, wfh 20 sec timer, set current lead status= " " and switch to next lead
            _ThirdPartyTimeOut = setTimeout(function () {
                // call API here
                dialerAPI_getLeadIdService().then(function (resultData) {
                    if (resultData.status === 404) {
                        UpdateCallStatus(ConnectCallSFdata.LeadID, " ", User.UserId);
                        ConnectCallSFdata.IsThirdParty = false;
                        window.localStorage.setItem('ConnectCallSF', JSON.stringify(ConnectCallSFdata));

                    }
                }, function () {

                });
            }, 20000);
        }
        setThirdPartyTimeOut(_ThirdPartyTimeOut);
    }
    const UpdateCallStatus = (LeadID, Status, UserId) => {
        if (next5leads) {
            let _next5leads = [...next5leads]
            _next5leads.forEach(function (element) {
                if (element.LeadId == LeadID) {
                    element.CallStatus = Status;
                }
            })
            setNext5leads(_next5leads);
            props.setNext5LeadsToRedux(_next5leads);
            UpdateCallStatusService(LeadID, Status, UserId);
            // if(Status == "Connected")
            // {
            //     OpenConnectedLead(next5leads);
            // }
        }

    }
    let _ThirdPartyTimeOut = null;
    const connectCall = (lead, IsAttempt, useAswatPhone) => {
        if (User.IsWFH === false && !Common.isUserWebphoneNew(User)) {
            if (!validateWebphoneConditions()) {
                return;
            }
        }

        var newItem = {
            'leadId': lead.LeadId,
            'CallStatus': 'CallInitiated',
            'Date': new Date()
        };
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;

        if (!onCall) {

            if (SV_CONFIG.skipNewLeadIB && lead.ReasonId === 3 && isRecentIBCall(lead.LeadId)) {
                // ignore new Lead Priority, if IB done recently
                UpdateCallStatus(lead.LeadId, " ", User.UserId);
                return;
            }

            console.log("connectCallSF -> called at", new Date());
            dispatch(updateStateInRedux({ key: "LastCallDuration", value:0 }));
            window.localStorage.removeItem("CallDispositionPopup");
            connectCallSF(lead, IsAttempt, useAswatPhone).then((result) => {
                console.log("connectCallSF -> Entered in then at", new Date(), "response : ", result);

                // setTimeout(function () {
                //     localStorage.setItem("callingapicalled", "0");
                // }, 2500);

                if (result.Status === "CallInitiated") {
                    console.log("connectCallSF -> CallInitiated", new Date());

                    // window.localStorage.setItem("onCall", true);
                    UpdateCallStatus(result.LeadID, "CallInitiated", User.UserId);
                    localStorage.setItem('PredictiveDialLeads', JSON.stringify(newItem));
                    sessionStorage.setItem('initNextCall', 2);
                    result.CallInitTime = JSON.stringify(new Date()).replace(/"/g, '');
                    result.CallId = Math.random();
                    result.lead = lead;
                    result.EmployeeId = User.EmployeeId;
                    window.localStorage.setItem('ConnectCallSF', JSON.stringify(result));
                    console.log("connectCallSF -> localstorage set", result);
                    // if (AppConfig.InternationalCountryCode.indexOf(result.CountryCode) > -1) {
                    if (IsAswatUser()) {
                        let ASWATCALLDATA = {};
                        ASWATCALLDATA.empId = User.EmployeeId;
                        ASWATCALLDATA.CallInitTime = new Date();
                        ASWATCALLDATA.LeadID = result.LeadID;
                        ASWATCALLDATA.SF = result.SF;
                        localStorage.setItem("ASWATCALLDATA", JSON.stringify(ASWATCALLDATA));
                    }
                    if (ThirdPartyTimeOut) {
                        clearTimeout(ThirdPartyTimeOut);
                    }

                    if (!result.IsThirdParty && User.CallingCompany === "") {
                        try {
                            window.ConnectCall(User.EmployeeId, result.Phone, result);
                        } catch (e) { console.error(e) }

                        console.log("ConnectCall Called")
                    } else {
                        if (User.IsWFH === false && !Common.isUserWebphoneNew(User)) {
                            // thirdPartyTimeoutFn(result, 1);
                        }
                        else {
                            thirdPartyTimeoutFn(result, 2);

                        }
                    }
                    handleSetInSchedular(false);
                    return;

                }
                if (result.Status == "Attempts exceed") {
                    let msg = ("Attempt exceed " + lead.Name + "( " + lead.LeadId + " )" + ", Do you want to proceed with next attempt?");
                    nextAttemptModalOpen(msg, lead, 1);

                }
                if (result.Status == "Email Only Customer.") {
                    window.localStorage.setItem("IsEmailOnly", true);
                    window.localStorage.setItem("IsEmailOnlyTime", new Date().getTime());
                    //$rootScope.hideSkip = false;
                    setTimeout(function () {
                        SkipMe(result.LeadID,
                            24,
                            User.UserId);
                    }, 120000);
                    handleSetInSchedular(false);
                }

            });

            try {
                if (SV_CONFIG.enableCallFullLogs) {
                    gaEventTracker("CALL_FULL_LOGS", JSON.stringify({ event: "predictiveDial", lead, ts: new Date() }), User.EmployeeId);
                }
            } catch { }
        }
    }

    const nextAttemptModalOpen = function (msg, Lead, type) {

        if (!modalOpen) {
            setModalOpen(true);
            setProcessLead(Lead);
            setAttemptExceedMessage(msg);
            setModalTitle(msg);
            setModalType("AttemptExceed");

            // GetCustomerComment({
            //     CustomerId: Lead.CustomerId,
            //     ProductId: ProductId,
            //     Leads: Lead.LeadId
            // }).then((result) => {

            //     if (result && result.GetCustomerCommentResult && result.GetCustomerCommentResult.Data) {
            //         setCustomerComment(result.GetCustomerCommentResult.Data.slice(0, 5));
            //     }

            // })

            var requestData = { CustomerId: Lead.CustomerId, ProductId: ProductId, Leads: Lead.LeadId };
            GetCustomerComment(parentLeadId, requestData).then((result) => {
                if (result) {
                    setCustomerComment(result.slice(0, 5));
                }
            });


            //Need to discuss with mohit
            setnextAttemptSkipTO(setTimeout(function () {
                if (type == 1)
                    setNextAttemptNo(true);
                else
                    setNextAttemptNo(false);
                nextAttemptSkip();
                setModalOpen(false);
            }, 30000))

        }


    };
    const OneLeadGetcallingdetails = function (CustomerId, LeadId, callback) {

        masterService.GetCallingDetails(CustomerId, LeadId).then(function (resultData) {
            // setOneLeadCallingNumbers(resultData)

            callback(resultData);
        }, function () {
            // setOneLeadCallingNumbers({});
            callback(null);
        });
    };

    const CheckPrimaryMob = function (lead, IsAttemps) {
        let data = { lead };
        if (data == null) {
            return;
        }
        if (!data.lead) {
            return;
        }
        var customerId = data.lead.CustomerId;

        if (customerId == 0) {
            // topNavService.SkipMe(data.lead.LeadId, 5, rootScopeService.getUserId(), true);
            enqueueSnackbar(data.lead.LeadId + " has Invalid customerId", {
                variant: 'error',
                autoHideDuration: 3000,
            });
            return;
        }

        // $scope.PredictiveData = data;
        // $scope.ProgressiveCustomerId = customerId;
        // $scope.PredictiveCall(data);
        OneLeadGetcallingdetails(customerId, data.lead.LeadId, function (resData) {
            if (resData == null) {
                enqueueSnackbar('Please add mobile number on this lead', {
                    variant: 'error', autoHideDuration: 3000
                });
                return;
            }
            var IsPrimaryArray = [];
            if (resData != null && resData.length > 1) {
                // Check for Iscallable
                var IsCallable = false;

                for (let i = 0, len = resData.length; i < len; i++) {
                    if (resData[i].IsCallable == 1) {
                        IsCallable = true;
                        // IsPrimaryArray = [...IsPrimary, { LeadId: data.lead.LeadId, status: true, IsPrimary: (resData[i].IsPrimary == 1) }]
                        IsPrimaryArray.push({ LeadId: data.lead.LeadId, status: true, IsPrimary: (resData[i].IsPrimary == 1) });
                        window.localStorage.setItem("IsPrimaryMobile", JSON.stringify(IsPrimaryArray));
                        dispatch(updateStateInRedux({ key: "IsPrimary", value: IsPrimaryArray }));
                        // $scope.PredictiveCall(data, IsAttemps);
                        break;
                    }
                }

                if (!IsCallable) {
                    enqueueSnackbar('Please set a callable number using add details.', {
                        variant: 'error',
                        autoHideDuration: 5000
                    })
                    // $scope.mobileNoModalOpen(data);
                    // $scope.getComments(customerId, data.lead.LeadId);
                    // console.log("mobileNoModalOpen.");
                }

            } else {

                IsPrimaryArray.push({ LeadId: data.lead.LeadId, status: true, IsPrimary: true });
                // IsPrimaryArray = [...IsPrimary, { LeadId: data.lead.LeadId, status: true, IsPrimary: (resData[i].IsPrimary == 1) }]
                window.localStorage.setItem("IsPrimaryMobile", JSON.stringify(IsPrimaryArray));
                dispatch(updateStateInRedux({ key: "IsPrimary", value: IsPrimaryArray }));
                // $scope.PredictiveCall(data, IsAttemps);
                console.log("Call initiated..");
            }
        });

    };

    const nextAttempt = function (e) {
        if (e.target.value == "Yes") {
            predictiveDial(processLead, true, true);
            setModalOpen(false);
            clearTimeout(nextAttemptSkipTO);
        } else {
            setNextAttemptNo(true);
            setNextAttemptSkipDuration("1");
        }
    }

    const nextAttemptSkip = function (e) {
        if (nextAttemptNo) {
            SkipMe(processLead.LeadId,
                nextAttemptSkipDuration,
                User.UserId
            )
            UpdateCallStatus(processLead.LeadId, "Skipped", User.UserId);

        } else {
            predictiveDial(processLead, true, false);
        }
        clearTimeout(nextAttemptSkipTO);
        setModalOpen(false);
        handleSetInSchedular(false);
    }

    // const attemptExceedChildrenBind = function () {


    //     setModalChildren(attemptExceedChildren);
    // }

    const getAttemptSkipDuration = function (e) {
        setNextAttemptSkipDuration(e.target.value);
    }

    const AutoDone = () => {
        handleSetInSchedular(false);
        props.setIsAutoDoneToRedux(true);
        clearInterval(AnsweredTimer);
        setAnsweredTimer(null);
        // document.getElementById("btnDone").click();
    }

    const getNextLeads = () => {
        props.setRefreshAgentStatsToRedux(true);
        // const input = {
        //     url: 'customer/GetUserNext5Leads/' + User.UserId, method: 'GET', service: 'CustomerNotificationURL'
        // };
        // CALL_API(input).then((result) => {
        //     if (result && result.Leads) {
        //         result.Leads.forEach(function (element) {
        //             element.EncryptSVURL = "../SalesView/" + btoa(element.CustomerId + "/" + element.ProductId + "/" + element.LeadId);
        //         });
        //         props.setNext5LeadsToRedux(result.Leads);
        //     }
        // });
    }

    const CallbackPopupResult = function (val) {
        if (val == 1) {
            // AutoDone()
            setModalOpen(false)

            setTimeout(function () {
                GetOneLead(User.UserId, ProductId);
            }, 30000);
        } else {
            // Pause the system if call gets connected and open SV
            GetOneLead(User.UserId, ProductId);
            setModalOpen(false);
        }
    }
    const handleAswatInput = (useAswatPhone) => {
        if (predictiveDialParams.lead) {
            setModalOpen(false);
            setModalType(null);
            setDonotShowAswatPopupUntil(new Date().getTime() + 6 * 1000);
            predictiveDial(predictiveDialParams.lead, predictiveDialParams.IsAttempt, predictiveDialParams.IsCheckPrimary, useAswatPhone)
            setPredictiveDialParams({ lead: null, IsAttempt: false, IsCheckPrimary: false })
        }
    }
    // const openMultipleCallPopup = () => {
    //     //show multiple call popup
    //     let connectCallSF = window.localStorage.getItem('ConnectCallSF');
    //     connectCallSF = connectCallSF ? JSON.parse(connectCallSF) : null;
    //     if (connectCallSF === null) return;

    //     const action = key => (
    //         <>
    //             {connectCallSF.lead
    //                 ? <a
    //                     onClick={() => {
    //                         Common.OpenSalesView(connectCallSF.lead.CustomerId, connectCallSF.lead.ProductId, connectCallSF.lead.LeadId)
    //                     }}
    //                 >
    //                     VIEW DETAILS
    //                 </a>
    //                 : null
    //             }
    //         </>
    //     );
    //     const message = <>
    //         <p>You are currently connected to </p>
    //         {/* <div>{connectCallSF.lead ? connectCallSF.lead.Name : ""}({connectCallSF.LeadID})</div> */}
    //         <p>{connectCallSF.lead ? connectCallSF.lead.Name : ""}({connectCallSF.LeadID})</p>
    //     </>
    //     enqueueSnackbar(message, {
    //         variant: 'success',
    //         autoHideDuration: 3000,
    //         className: "multipleCallPopup",
    //         action,
    //         preventDuplicate: true,
    //         anchorOrigin: {
    //             vertical: 'top',
    //             horizontal: 'center',
    //         }
    //     })
    // }
    const AddLeadToPriorityQueue = (lead) => {
        props.setNext5LeadsToRedux([...next5leads, lead]);

        return AddLeadToPriorityQueueService(lead);
    }

    const removeUnconnectedLeadStatus = (connectedLeadId) => {
        try {
            next5leads.forEach((lead) => {
                if (lead.LeadId != connectedLeadId) {
                    if (lead.CallStatus) {
                        UpdateCallStatus(lead.LeadId, " ", User.UserId);
                    }
                }
            })
        }
        catch (e) {
            console.error(e);
        }
    }
    const opensvByAPI = () => {
        // open SV for IB calls 
        try {
            if (opensvByAPICalled)
                return;
            let opensvleadid = localStorage.getItem("opensvleadid");
            let calltype = localStorage.getItem("calltype");
            let ServiceLead = { leadId: opensvleadid, callType: calltype };
            window.localStorage.setItem("ServiceLead", JSON.stringify(ServiceLead));
            // $rootScope.ServiceLead = { leadId: opensvleadid, callType: calltype };
            let Reason = "Inbound";
            let ReasonId = 34;

            props.setCallTypeToRedux(calltype);

            if (["IB_OB"].indexOf(calltype) > -1) {
                Reason = "Predictive Outbound";
                ReasonId = 37;
            }
            if (["CTCOB_IB"].indexOf(calltype) > -1) {
                Reason = "CTCOB_IB";
                ReasonId = 37;
            }

            if (opensvleadid != null && opensvleadid != "" && opensvleadid != undefined) {
                dispatch(updateStateInRedux({ key: "LastCallDuration", value:0 }));
                setopensvByAPICalled(true);
                pushRecentIBLeadId(opensvleadid);
                const input = {
                    url: `api/SalesView/GetLeadCustomerDetails/${opensvleadid}`,
                    method: 'GET', service: 'MatrixCoreAPI',
                };
                CALL_API(input).then(function (JsonData) {
                    console.log("JsonData", JsonData)
                    let lead = {
                        "LeadId": JsonData.LeadData.LeadID,
                        "Name": "IB Lead",
                        "CustomerId": JsonData.LeadData.CustID,
                        "UserID": User.UserId,
                        "Priority": 0,
                        "ProductId": JsonData.LeadData.ProductID,
                        "Reason": Reason,
                        "ReasonId": ReasonId,
                        "CallStatus": "Connected",
                        "IsAddLeadtoQueue": 1,
                        "IsNeedToValidate": 0
                    }
                    AddLeadToPriorityQueue(lead);

                    // $scope.IsPredictiveCall = true;
                    // queueLogout();
                    localStorage.removeItem("opensvleadid");
                    setopensvByAPICalled(false);
                }, function () { });
            }
        } catch (e) {

        }
    }
    const openSVFailSafe = () => {
        // check disposition and connectCallSF, and update status to "connected" for correct lead (if status is different)
        // and when callstatus is "connected" we open leads salesview 
        const handleLeadOpen = () => {
            // var connectCallSFobj = Common.getConnectCallSFFromLS();                    
            let RemoteFriendlyLeadId = window.localStorage.getItem('RemoteFriendlyLeadId');
            let leadId = RemoteFriendlyLeadId;
            let gaDump = { message: "", Priority: "", leadId, UserId: User.UserId };
            // if (!RemoteFriendlyLeadId && connectCallSFobj && ((connectCallSFobj.LeadId==leadId) || (connectCallSFobj.lead && (connectCallSFobj.lead.LeadId==leadId)))) {
            //     leadId = connectCallSFobj && (connectCallSFobj.LeadId || (connectCallSFobj.lead && connectCallSFobj.lead.LeadId));
            // }
            if (!leadId) return;
            leadId = parseInt(leadId)
            let LeadData = next5leadsfromRedux.find(function (x) {
                return (x.LeadId === leadId)
            })
            if (LeadData) {
                // if lead already in priority update status to connected
                if (LeadData.CallStatus !== 'Connected')
                    UpdateCallStatus(leadId, "Connected", User.UserId);
            }
            else {
                gaDump.message += "Lead not found in PRIORITY";
                gaDump.Priority = next5leadsfromRedux;
                // if lead not in priority add to priority and update status to connected
                let _LstAgentLeads = getSearchObjFromLS();

                LeadData = _LstAgentLeads.find(lead => (lead && lead.LeadID === leadId));
                if (LeadData && LeadData.CustID) {
                    const lead = {
                        "LeadId": leadId,
                        "Name": LeadData.CustName,
                        "CustomerId": LeadData.CustID,
                        "UserID": User.UserId,
                        "Priority": 1,
                        "ProductId": LeadData.ProductID,
                        "Reason": 'Manual added',
                        "ReasonId": 32,
                        "CallStatus": "Connected",
                        "IsAddLeadtoQueue": 1,
                        "IsNeedToValidate": 0
                    };
                    AddLeadToPriorityQueue(lead);
                }
                else {
                    gaEventTracker("CALLING_LEAD_NOT_OPENED", `${leadId}_${User.UserId}_${new Date()}`);
                }
                gaEventTracker("CALLING_LEAD_NOT_OPENED_1", JSON.stringify(gaDump));
            }
        }


        let _displayCallStatus = window.localStorage.getItem('callDisposition')

        _displayCallStatus = _displayCallStatus ? _displayCallStatus.toLowerCase() : '';

        switch (_displayCallStatus) {
            case "in call":
                handleLeadOpen();
                break;

            default: break;
        }
    }
    const fnChangeCallStatus = () => {
        try {
            if (InAPICall)
                return;
            var callstatus = localStorage.getItem("callstatus");
            // console.log("callstatus", callstatus);
            if (callstatus != null && callstatus != "" && callstatus != undefined) {
                setInAPICall(true);
                const lead = {
                    "LeadId": parentLeadId,
                    "Name": "Predictive Lead",
                    "CustomerId": rootScopeService.getCustomerId(),
                    "UserID": User.UserId,
                    "Priority": 0,
                    "ProductId": rootScopeService.getProductId(),
                    "Reason": 'Predictive',
                    "ReasonId": 34,
                    "CallStatus": callstatus,
                    "IsAddLeadtoQueue": 1,
                    "IsNeedToValidate": 0
                }
                AddLeadToPriorityQueue(lead);
                localStorage.removeItem("callstatus");
                setInAPICall(false);
            }
        } catch (e) {

        }
    };

    const action = key => (
        <>
            <CloseIcon onClick={() => { closeSnackbar(key) }} />
        </>
    );
    const UpdateExclusiveBenifit = (isExclusiveBenifit, ParentLeadID) => {
        const reqData = {
            "LeadId": ParentLeadID,
            "isExclusiveBenifit": isExclusiveBenifit ? 1 : 0,
            "customerID": rootScopeService.getCustomerId()
        };
        const input = {
            url: `api/HealthRenewal/UpdateExclusiveBenifit`,
            method: "POST",
            service: "MatrixCoreAPI",
            timeout: 's',
            requestData: reqData
        }
        CALL_API(input).then((result) => {
            if (result) {
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 2000,
                });
            }
        });
        setModalOpen(false);
    }
    const showPlanPitchPopup = function (msg) {
        if (!modalOpen) {
            setModalOpen(true);
            setModalType("PlanPitchPopupShow");
            setModalTitle(msg);
        }
    }

    const MicrophoneEnablePerm = () => {
        if(navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia){
            let micPermissionPopupClicked = false;
            
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function (stream) {
                    micPermissionPopupClicked = true;
                    localStorage.setItem('secsMicNotEnabled', 0);
                    setMicPermGiven(true);

                    stream.getTracks().forEach(function(track) {
                        track.stop();
                    });
                })
                .then(() => navigator.mediaDevices.enumerateDevices())
                .catch(function (err) {
                    micPermissionPopupClicked = true;
                    let currentValue = parseInt(localStorage.getItem('secsMicNotEnabled'));
                    currentValue+=5;
                    localStorage.setItem('secsMicNotEnabled', currentValue);

                    console.warn('No mic for you!  ', err)
                    setMicPermGiven(false);
                })
            if (!micPermissionPopupClicked) {
                let currentValue = parseInt(localStorage.getItem('secsMicNotEnabled'));
                currentValue+=5;
                localStorage.setItem('secsMicNotEnabled', currentValue);
                setMicPermGiven(false);
            }
        }
        if (User.RoleId === 13 && SV_CONFIG && !SV_CONFIG.DISABLE_LOGOUT_ON_MICOFF && (parseInt(localStorage.getItem('secsMicNotEnabled')) > (SV_CONFIG.MIC_DISABLED_LOGOUT_SEC || 30)) && !micOffLogout) {
            matrixLogout(3000, 18, "You are logged out as the microphone is off");
            setMicOffLogout(true);
        }
    }

    const formattedMessage = (message) => {
        return message.split("\n").map((line, index) => (
        <React.Fragment key={index}>
          {line.trim()}
          <br />
        </React.Fragment>
      ));
    }

    useEffect(() => {
        if (IsAutoDone === 0) {
            // to clear time set to 0
            clearInterval(AnsweredTimer);
            props.setIsAutoDoneToRedux(false);
            // setAnsweredTimer(null);
        }
    }, [IsAutoDone])

    useEffect(() => {
        localStorage.setItem('secsMicNotEnabled', 0);
        //const interval = 
        setTimeout(() => {
            setIsRefreshPage(true)
        }, 11000);
        // return () => clearInterval(interval);
    }, []);

    useInterval(() => {
        opensvByAPI();
        openSVFailSafe();
        fnChangeCallStatus();

        if (PlanPitchPopup == true) {
            const leadData = allLeads.length > 0 ? allLeads[0] : null;
            if (ConnectedLead && (ConnectedLead.LeadId == leadData.ParentID)) {
                let NFOMessageArr = SV_CONFIG["NFO_MessagePopup"];
                let msg = "";
                if (Array.isArray(NFOMessageArr)) {
                    // First, check for matching GroupId
                    let groupMatchMessage = null;
                    if (Array.isArray(User.UserGroupList)) {
                        groupMatchMessage = NFOMessageArr.find(msgObj =>
                            msgObj.GroupId &&
                            User.UserGroupList.some(grp => {
                                const userGroupId = grp.GroupId;
                                if (Array.isArray(msgObj.GroupId)) {
                                    // Check if any group ID in msgObj.GroupId matches userGroupId
                                    return msgObj.GroupId.includes(userGroupId);
                                } else {
                                    // Simple direct match
                                    return userGroupId == msgObj.GroupId;
                                }
                            })
                        );
                    }
                
                    if (groupMatchMessage && groupMatchMessage.NFO_Message) {
                        msg = formattedMessage(groupMatchMessage.NFO_Message);
                    } else {
                        // Fallback to ProductId match
                        let productMatchMessage = NFOMessageArr.find(msgObj =>
                            msgObj.ProductId && msgObj.ProductId == rootScopeService.getProductId()
                        );
                
                        if (productMatchMessage && productMatchMessage.NFO_Message) {
                            msg = formattedMessage(productMatchMessage.NFO_Message);
                        }
                    }
                }
                
                if(msg !== "") {
                showPlanPitchPopup(msg);
                }
            }
        }
    }, 1000);

    useInterval(() => {
        MicrophoneEnablePerm();
    }, 5000)

    useInterval(() => {
        // console.log("useInterval: ", next5leads);
        // console.log("useInterval: ", new Date());
        if (Ispriority && User.RoleId === 13) {
            RunProgressiveSchedular(next5leads);
        }
    }, ProgressiveScheduler);
    useEffect(() => {
        if ([3, 14].indexOf(ProductId) > -1) {
            setProgressiveSchduler(15000);
        }

        if (User.IsWFH == true) {
            if (!Common.isUserWfhNew(User)) {
                setProgressiveSchduler(15000);
            }
        }
        //if (JSON.stringify(next5leads) !== JSON.stringify(next5leadsPRV)) {
        // if (next5leads && next5leads.length > 0) {
        // console.log("setInterval: RunProgressiveSchedular");
        // console.log("next5leads", next5leads);

        //const interval = setInterval(() => RunProgressiveSchedular(next5leads), ProgressiveScheduler);
        // }
        if (!(next5leads && next5leads.length > 0)) {

            if (IsRefreshPage) {
                enqueueSnackbar("Your leads are end for now, Please click on done button", {
                    variant: 'success', autoHideDuration: 3000, anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'right',
                    },
                    preventDuplicate: true
                });
                localStorage.setItem("isShowDoneBtn", true)
                // window.localStorage.setItem("onCall", false)
            }

        }
        //return () => { console.log("ClearInterval: RunProgressiveSchedular"); clearInterval(interval); }

        //}



    }, [next5leads]);

    useEffect(() => {
        try { clearTimeout(ThirdPartyTimeOut); } catch { }
        if (ConnectedLead && ConnectedLead !== "null") {
            window.localStorage.removeItem("CallConnectData");
            window.localStorage.removeItem("CallDispositionPopup");
            dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));
            dispatch(updateStateInRedux({ key: "showCallIntentPopup", value: false }));
            const _currentConnectCallSF = Common.getConnectCallSFFromLS();
            if (!_currentConnectCallSF || !_currentConnectCallSF.lead) {
                const CallInitTime = _currentConnectCallSF && _currentConnectCallSF.CallInitTime;
                let ConnectCallSFData = null;
                try {
                    ConnectCallSFData = {
                        EmployeeId: User.EmployeeId,
                        empId: User.EmployeeId,
                        userId: User.UserId,
                        LeadID: ConnectedLead.LeadId,
                        lead: ConnectedLead,
                        Status: ConnectedLead.CallStatus,
                        CallInitTime: CallInitTime || ConnectedLead.ts || new Date().toISOString()
                    }
                }
                catch { }
                if (ConnectCallSFData) {
                    const data = JSON.stringify(ConnectCallSFData)
                    localStorage.setItem('ConnectCallSF', data);
                }
            }
            //remove other lead statuses, so that only connected Lead is highlighted
            removeUnconnectedLeadStatus(ConnectedLead.LeadId);

            Common.OpenSalesView(ConnectedLead.CustomerId, ConnectedLead.ProductId, ConnectedLead.LeadId, null, ConnectedLead.Reason, ConnectedLead.ReasonId);
            props.setRefreshLeadToRedux(true);
            // clear auto done timer, if any
            props.setIsAutoDoneToRedux(0);
            localStorage.removeItem("RemoteFriendlyLeadId");
            window.localStorage.setItem("ConnectedLeadProductID", ConnectedLead.ProductId);

        }
    }, [JSON.stringify(ConnectedLead)]);

    // const OpenConnectedLead = (leads) => {
    //     var indexConnected = -1;
    //     var items = pluck(leads, 'CallStatus');
    //     indexConnected = items.indexOf('Connected');
    //     if (indexConnected != -1) {
    //         let _ConnectedLead = leads[indexConnected];
    //         Common.OpenSalesView(_ConnectedLead.CustomerId, _ConnectedLead.ProductId, _ConnectedLead.LeadId, null, _ConnectedLead.Reason, _ConnectedLead.ReasonId);
    //         props.setRefreshLeadToRedux(true);
    //     }
    // }


    // let _displayCallStatus = window.localStorage.getItem('callDisposition')
    // useEffect(() => {
    //     switch (_displayCallStatus) {
    //         case "In Call":
    //             OpenConnectedLead(next5leads);
    //             break;
    //         default  : break;

    //     }

    // }, [_displayCallStatus]);

    useEffect(() => {
        if (NotAnsweredLead && NotAnsweredLead != "null") {
            window.localStorage.removeItem("CallConnectData");
            dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));
            dispatch(updateStateInRedux({ key: "showCallIntentPopup", value: false }));
            handleSetInSchedular(false);
            //RunProgressiveSchedular(next5leads);
            window.localStorage.setItem("isShowDoneBtn", true);
            setModalOpen(false);
            // window.localStorage.setItem("onCall", false);
            if (Array.isArray(SV_CONFIG.popNotAnsweredLeadProds) && SV_CONFIG.popNotAnsweredLeadProds.includes(ProductId)) {
                GetOneLead(User.UserId, ProductId).then((result) => {
                    getNextLeads()
                })
            }
            SetLastCalledLead(NotAnsweredLead.LeadId);
            NotConnectedAutoStamping(NotAnsweredLead.LeadId);
        }
    }, [JSON.stringify(NotAnsweredLead)]);

    const NotConnectedAutoStamping = (NotAnsweredLeadID) =>
    {
        let ParentLeadID = 0;
        let CustomerID = 0;
        let CurrentStatusID = 0;
        try
        {

        if (Array.isArray(allLeads) && allLeads.length > 0)
        {
            ParentLeadID = allLeads[0].ParentID;
            CustomerID = allLeads[0].CustomerID;
            let parentarray = allLeads.filter(function (x) {
                return (x.LeadID === ParentLeadID)});
            CurrentStatusID = parentarray[0].StatusId;
        };
        let IsSubStatusMarked = window.localStorage.getItem("CallDispositionPopup") == NotAnsweredLeadID ? true : false;
        if(ParentLeadID == NotAnsweredLeadID && CheckIsMonthlyModeTeam() == true && IsSubStatusMarked == false 
            && [106].indexOf(rootScopeService.getProductId()) > -1)
            {
                const requestData = {
                    CustomerID: CustomerID,
                    ParentID: ParentLeadID,
                    UserID: User.UserId,
                    roleId: User.RoleId,
                    planList: [],
                    LeadID: ParentLeadID,
                    StatusId: CurrentStatusID,
                    SubStatusId: 2415,
                    IsMonthyModeCD : true
                    }
                    setSMELeadsStatusService(requestData).then((resultData) => {
                    if (resultData && resultData.Data) {
                        if(resultData.Data.IsSaved == true)
                        {
                        window.localStorage.setItem("CallDispositionPopup",ParentLeadID);
                        enqueueSnackbar("Substatus marked automatically", { variant: 'success', autoHideDuration: 3000 });
                        }
                        else
                        {
                        enqueueSnackbar("Failed to save Substatus.", { variant: 'error', autoHideDuration: 3000 });
                        }
                    }
                    });
            }
        }
        catch(ex)
        {

        }
            

    }

    const setSMELeadsStatusService = (requestData) => {
        const input = {
          url: "coremrs/api/LeadDetails/SetSMELeadsStatus",
          method: "POST",
          service: "MatrixCoreAPI",
          requestData
        };
        return CALL_API(input);
      }

    useEffect(() => {
        // localStorage.setItem("isShowDoneBtn", true);
        if (AnsweredLead && AnsweredLead != "null") {
            const _currentConnectCallSF = Common.getConnectCallSFFromLS();
            const CallInitTime = _currentConnectCallSF && _currentConnectCallSF.CallInitTime;
            window.localStorage.setItem("ShowRiderAttachmentPopup", "true")
            if(CallInitTime!=null || LastCallDuration)
            {  
                // CCTEC-4803 - Show Popup to Monthly Mode team on call hangup to mark Non Payment Reason when answered call is of more than 20 seconds
                let ParentLeadID = 0;
                let CallDuration = 0;
                let CustomerID = 0;
                let CurrentStatusID = 0;
                let CallConnectData = undefined;
                let ExactCallDuration = 0;

                try
                {
                CallConnectData = window.localStorage.getItem("CallConnectData") != undefined ? JSON.parse(window.localStorage.getItem("CallConnectData")) : undefined;
                if(CallInitTime != null){
                    const CurrentTime = JSON.stringify(new Date()).replace(/"/g, '');
                    CallDuration = Math.abs((new Date(CurrentTime) - new Date(CallInitTime)) / 1000);
                    if(CallConnectData == undefined || 
                        (CallConnectData && CallConnectData.leadid == AnsweredLead.LeadId && 
                        CallConnectData.CallEndTime!= null &&  (CallConnectData.CallEndTime - new Date() > 2* 60 * 1000 )))
                    {
                        let json = {leadid: AnsweredLead.LeadId,CallEndTime: CurrentTime};
                        CallConnectData = JSON.stringify(json);
                        window.localStorage.removeItem("CallConnectData");
                        window.localStorage.setItem("CallConnectData", CallConnectData);
                    }
                }
               
                let IsSubStatusMarked = window.localStorage.getItem("CallDispositionPopup") == AnsweredLead.LeadId ? true : false;
                if (Array.isArray(allLeads) && allLeads.length > 0)
                {
                    ParentLeadID = allLeads[0].ParentID;
                    CustomerID = allLeads[0].CustomerID;
                    let parentarray = allLeads.filter(function (x) {
                        return (x.LeadID === ParentLeadID)});
                    CurrentStatusID = parentarray[0].StatusId;
                };

                CallConnectData = window.localStorage.getItem("CallConnectData") != undefined ? JSON.parse(window.localStorage.getItem("CallConnectData")) : undefined;
                ExactCallDuration = CallConnectData != undefined ? Math.abs((new Date(CallConnectData.CallEndTime) - new Date(CallInitTime)) / 1000) : 0;
                if(ParentLeadID == AnsweredLead.LeadId && CheckIsMonthlyModeTeam() == true && IsSubStatusMarked == false 
                    && [106].indexOf(rootScopeService.getProductId()) > -1 && ExactCallDuration < 15 && ExactCallDuration > 0)
                {
                    const requestData = {
                        CustomerID: CustomerID,
                        ParentID: ParentLeadID,
                        UserID: User.UserId,
                        roleId: User.RoleId,
                        planList: [],
                        LeadID: ParentLeadID,
                        StatusId: CurrentStatusID,
                        SubStatusId: 2415,
                        IsMonthyModeCD : true
                      }
                      setSMELeadsStatusService(requestData).then((resultData) => {
                        if (resultData && resultData.Data) {
                          if(resultData.Data.IsSaved == true)
                          {
                            window.localStorage.setItem("CallDispositionPopup",ParentLeadID);
                            enqueueSnackbar("Substatus marked automatically", { variant: 'success', autoHideDuration: 3000 });
                          }
                          else
                          {
                            enqueueSnackbar("Failed to save Substatus.", { variant: 'error', autoHideDuration: 3000 });
                          }
                        }
                      });
                }
            
                if(ParentLeadID == AnsweredLead.LeadId && CheckIsMonthlyModeTeam() == true && IsSubStatusMarked == false 
                    && [106].indexOf(rootScopeService.getProductId()) > -1 && ExactCallDuration > 15)
                {
                    dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: true }));
                    dispatch(updateStateInRedux({ key: 'showCallIntentPopup', value: false }));
                    let UserId = User.UserId;
                    var requestData = {
                        "CustomerId": rootScopeService.getCustomerId(),
                        "ProductId": rootScopeService.getProductId(),
                        ParentLeadID,
                        UserId,
                        "Comment": "Monthly Mode Call Disposition PopupShow",
                        "EventType": 66
                    }
                        SetCustomerComment(requestData);
                }
                else
                {
                    dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));

                    if(
                        SV_CONFIG && !SV_CONFIG["DisableCallIntentPopup"] 
                        && (
                            (
                                Array.isArray(SV_CONFIG["ShowCallIntentPopupAgents"]) && SV_CONFIG["ShowCallIntentPopupAgents"].includes(User.EmployeeId)
                            )
                            || (
                                Array.isArray(User.ProductList) && Array.isArray(SV_CONFIG['ShowCallIntentPopupPrduct']) && User.ProductList.some(product => SV_CONFIG['ShowCallIntentPopupPrduct'].includes(product.ProductId))
                            )
                            || (
                                Array.isArray(User.GroupList) && Array.isArray(SV_CONFIG['ShowCallIntentPopupGrps']) && User.GroupList.some(grps => SV_CONFIG['ShowCallIntentPopupGrps'].includes(grps.GroupId))
                            )
                        ) 
                        && (
                            (CallDuration >= (SV_CONFIG["callIntntPopupMinCallDuration"] || 40))
                            || (LastCallDuration >= (SV_CONFIG["callIntntPopupMinCallDuration"] || 40))
                        )
                    ){
                        dispatch(updateStateInRedux({ key: 'showCallIntentPopup', value: true }));
                        dispatch(updateStateInRedux({ key: "callIntentLeadId", value: AnsweredLead.LeadId}))

                        try {
                            const requestData = {
                                "LeadId": AnsweredLead.LeadId,
                                "UserId": User.UserId,
                                "Intent": null
                            }
                            SaveCallIntent(requestData);
                        } catch(err) {}
                        
                    } else {
                        dispatch(updateStateInRedux({ key: 'showCallIntentPopup', value: false }));
                    }
                }
                }
                catch(ex)
                {

                }
            }
            let CommentsKey, SubstatusKey;
            if (CommentsPopUp && !CommentsAdded) {
                let message = "Please put up the comments"
                CommentsKey = enqueueSnackbar(message, {
                    variant: 'success',
                    persist: false,
                    className: "commentsPopup",
                    preventDuplicate: true,
                    action,
                    autoHideDuration: 45000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'center',
                    }
                })
            }
            dispatch(updateStateInRedux({ key: 'CommentsPopUp', value: { LeadID: 0, show: false } }));
            dispatch(updateStateInRedux({ key: 'CommentsAdded', value: false }));
            if (SubStatusPopUp && !SubStatusAdded && Leadsexpiry != "") {
                let message = "Please mark sub status, enter comments & move to next lead."
                SubstatusKey = enqueueSnackbar(message, {
                    variant: 'success',
                    persist: false,
                    className: "commentsPopup",
                    preventDuplicate: true,
                    action,
                    autoHideDuration: 20000,
                    anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'center',
                    }
                })
            }
            setTimeout(() => {
                closeSnackbar(SubstatusKey);
                closeSnackbar(CommentsKey);
            }, 40000);
            dispatch(updateStateInRedux({ key: 'SubStatusPopUp', value: false }));
            dispatch(updateStateInRedux({ key: 'SubStatusAdded', value: false }));
            dispatch(updateStateInRedux({ key: 'PlanPitchPopup', value: false }));
            setModalOpen(false);

            // if (!AnsweredTimer) {
            if (ProductId == 114) {
                answerRestTime = 120000;
            }
            // window.localStorage.setItem("onCall", false);
            // console.log("Flow1: answwered timer start");
            if (IsAutoDone !== 0) {
                // console.log("Flow1: _AnsweredTimer useeffect Start: ", new Date());

                let _AnsweredTimer = setTimeout(function () {
                    // console.log("Flow1: _AnsweredTimer useeffect Inside: ", new Date());

                    // let IsPause = window.localStorage.getItem("IsPause") === "true" ? true : false;
                    // if (IsPause == false) {
                    AutoDone();

                    //GetOneLead(User.UserId, ProductId);
                    getNextLeads();
                    window.localStorage.removeItem('AnswerCallTime');
                    setAnsweredTimer(null);
                    // localStorage.setItem("isShowDoneBtn", false);
                    // }
                }, answerRestTime);
                // }
                setAnsweredTimer(_AnsweredTimer);
            }

            SetLastCalledLead(AnsweredLead.LeadId);
        }
    }, [JSON.stringify(AnsweredLead)]);

    useEffect(() => {
        dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));
        dispatch(updateStateInRedux({ key: "showCallIntentPopup", value: false }));
    }, [allLeads,RefreshLead]);

    let PlanPitchPopupBody = <div className="modal-body">
        <ButtonGroup variant="contained" color="primary" aria-label="contained primary button group">
            <Button onClick={() => { UpdateExclusiveBenifit(1, parentLeadId) }} >Yes</Button>
            <Button onClick={() => { UpdateExclusiveBenifit(0, parentLeadId) }}>No</Button>
        </ButtonGroup>
    </div>

    let attemptExceedChildren = <div className="viewport">
        <FormControl >
            <RadioGroup onChange={nextAttempt}>
                <FormControlLabel value="Yes" control={<Radio />} label="Yes" />
                <FormControlLabel value="No" control={<Radio />} label="No" />
            </RadioGroup>
        </FormControl>

        {nextAttemptNo && <div>
            <FormControl >
                <Select
                    value={nextAttemptSkipDuration}
                    onChange={getAttemptSkipDuration}>
                    <MenuItem value={"1"}>1 Hr</MenuItem>
                    <MenuItem value={"2"}>2 Hr</MenuItem>
                    <MenuItem value={"24"}>24 Hr</MenuItem>
                </Select>
            </FormControl>
            <Button onClick={nextAttemptSkip} className="skipBtn">Skip</Button>
            {/* <button class="add_button" type="button" onClick={nextAttemptSkip}>Skip</button> */}
        </div>
        }
        {
            (customerComment) && <ul>
                {customerComment.map((item, index) => (
                    <li key={index}>
                        {item.Comments}
                    </li>
                ))
                }
            </ul>
        }

    </div>



    let revisitChildren = <div className="modal-body" >
        <h6>Do you want to set a callback or reject this lead.</h6>
        <br />
        <ButtonGroup variant="contained" color="primary" aria-label="contained primary button group">
            <Button onClick={() => CallbackPopupResult(1)}>Yes</Button>
            <Button onClick={() => CallbackPopupResult(0)}>No</Button>
        </ButtonGroup>


    </div>
    let SelectWebphoneChidren = <div className="modal-body">
        <Grid container spacing={2}>
            <Grid item xs={12}>
                <h6 style={{ paddingLeft: 0 }}>Do you want to make this call via ASWAT</h6>
            </Grid>
            <Grid item>
                <Button onClick={() => { handleAswatInput(true) }} color="secondary" variant="contained"> Yes</Button>
            </Grid>
            <Grid item>
                <Button onClick={() => { handleAswatInput(false) }} variant="contained"> No </Button>
            </Grid>
        </Grid>
    </div>
    
    let modalChild = null;
    // if (modalType == "RevisitPaymentCallBack") {
    //     modalChild = revisitChildren;
    // }
    //else 
    if (modalType == "AttemptExceed") {
        modalChild = attemptExceedChildren;
    }
    else if (modalType == "PlanPitchPopupShow") {
        modalChild = PlanPitchPopupBody;
    }
    else if (modalType == Modals.AswatSelection) {
        modalChild = SelectWebphoneChidren
    }
    else {
        modalChild = null;
    }

    const CloseCallDispositionPopup = () => {
        dispatch(updateStateInRedux({ key: 'NonPaymentReasonPopUp', value: false }));
    }
    
    return (
        <div>      
          {modalChild && (
            <ModalPopup
              className="PlanPitchPopupClass"
              disableBackdropClick={true}
              title={modalTitle}
              actions={modalActions}
              open={modalOpen}
              children={modalChild}
              handleClose={() => { setModalOpen(false); clearTimeout(nextAttemptSkipTO); }}
            />
          )}
          {NonPaymentReasonPopUp && <CallDispositionReasonPopup open={NonPaymentReasonPopUp} handleClose={() => { CloseCallDispositionPopup() }} />}
        </div>
      );
      
}

const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {

    return {
        setRefreshAgentStatsToRedux: (value) => dispatch(setRefreshAgentStats({ RefreshAgentStats: value })),
        setNext5LeadsToRedux: (leads) => dispatch(setNext5LeadsData({ next5leads: leads })),
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value })),
        setIsAutoDoneToRedux: (value) => dispatch(setIsAutoDone({ IsAutoDone: value })),
        setIsAnsweredToRedux: (value) => dispatch(setIsAnswered({ IsAnswered: value })),
        setConnectedLeadToRedux: (leads) => dispatch(setConnectedLeadsData({ next5leads: leads })),
        setCallTypeToRedux: (value) => dispatch(setCallType({ CallType: value })),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(ProgressiveComponent);


export const dialerAPI_getLeadIdService = () => {
    const input = {
        url: 'customer/getagenstatus/' + User.UserId, method: 'GET', service: 'CustomerNotificationURL',
        timeout: 'm', acceptOnly200: true
    };

    return CALL_API(input)
        .catch((e) => {
            return {
                status: 0,
                errormsg: e
            }
        });
}


export const connectCallSF = (lead, IsAttempts, useAswatPhone) => {
    let source = '';
    if (isNonProgressiveWfhCalling()) {
        source = 'fosapp'
    }
    return connectCallSFservice(lead, IsAttempts, source, useAswatPhone);
}
export const GetOneLead = (userID, productId) => {
    var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
    if (onCall) {
        // localStorage.setItem("isShowDoneBtn", false);
        return new Promise((resolve, reject) => {
            resolve({});
        });
    }
    return GetOneLeadService(userID, productId);
}
export const SkipMe = (leadid, duration, userid, IsMins) => {

    var url = "onelead/api/LeadPrioritization/SkipLead/" + leadid + "/" + duration + "/" + userid;
    if (IsMins) {
        url = "onelead/api/LeadPrioritization/SkipLead/" + leadid + "/" + duration + "/" + userid + "?IsMins=" + IsMins;
    }
    const input = {
        url: url, method: 'GET', service: 'MatrixCoreAPI'
    };

    return CALL_API(input)
}

export const getAgentIbNumberService = (EmployeeId) => {
    if (!EmployeeId) return;

    var url = `agentstatus/getagentibnumber/${User.EmployeeId}`;
    const input = {
        url: url, method: 'GET', service: 'agentTracker'
    };
    return CALL_API(input)
}