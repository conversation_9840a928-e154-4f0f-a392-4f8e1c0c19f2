import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import { CONFIG } from "../../../appconfig";
import { gaEventTracker } from "../../../helpers";
import User from "../../../services/user.service";
import rootScopeService from "../../../services/rootScopeService";
import { saveIntentFeedbackService, saveSummaryFeedbackService } from "../../../services/Common";
import { Button, Checkbox, FormControlLabel, Tooltip } from "@mui/material";
import { KeyboardBackspaceRounded } from "@mui/icons-material";
import { getSearchObjFromLS } from "../../../helpers/commonHelper";
import dayjs from "dayjs";
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { useSnackbar } from "notistack";
import CustomerInsurerAIPopup from "./Modals/CustomerIntentAIPopup";
dayjs.extend(advancedFormat)

const IntentListItem = ({ intent, notHelpfulForm, leadSummary, callSummary, CustomerIntentsList }) => {
    const dispatch = useDispatch();
    const data = leadSummary || callSummary;

    const handleChange = (event, intent) => {
        Object.keys(data).forEach((title) => {
            Array.isArray(data[title]) && data[title].forEach((item) => {
                if (item.ID === intent.ID) {
                    item.feedback = event.target.checked ? 2 : 1;
                }
            })
        })

        if (leadSummary) {
            CustomerIntentsList.lead_summary = data;
        }
        if (callSummary) {
            CustomerIntentsList.call_summary = data;
        }
        dispatch(updateStateInRedux({ key: 'CustomerIntentsList', value: CustomerIntentsList }));
    }
    if (intent && intent.Response === Feedback.NOT_HELPFUL) {
        return null;
    }
    if (notHelpfulForm) {
        if (intent && intent.Response === Feedback.HELPFUL) return null;

        return <FormControlLabel
            className="ListItem"
            title={intent.DisplayText}
            control={<Checkbox color="secondary" />}
            label={intent.DisplayText.charAt(0).toUpperCase() + intent.DisplayText.slice(1)}
            onChange={(event) => handleChange(event, intent)}
            checked={intent.feedback === 2}
        />
    }
    return <Tooltip title={intent.DisplayText}><li> {intent.DisplayText && intent.DisplayText.charAt(0) && intent.DisplayText.charAt(0).toUpperCase() + intent.DisplayText.slice(1)} </li></Tooltip>;
}

const CustomerPitchDetails = React.memo((props) => {
    const CustomerIntentsList = useSelector(({ salesview }) => salesview.CustomerIntentsList);

    if (!CustomerIntentsList || !CustomerIntentsList.lead_summary) {
        return null
    }
    const leadSummary = CustomerIntentsList.lead_summary;

    return <>
       
        <div className={props.notHelpfulForm ? "feedbackForm scrollbar" : "scrollbar"}>
            {props.notHelpfulForm && <p>Please let us know which Intent is incorrect</p>}

            {props.notHelpfulForm && <p><i>{Array.isArray(leadSummary["Concerns Category"]) && leadSummary["Concerns Category"].filter((intent) => intent.Response === Feedback.NOT_MARKED).length > 0 && "Concerns Category"}</i></p>}
            
            {!props.notHelpfulForm && <p><i>{Array.isArray(leadSummary["Concerns Category"]) && "Concerns Category"}</i></p>}

            {Array.isArray(leadSummary["Concerns Category"]) && leadSummary["Concerns Category"].map((intent) =>
                <IntentListItem
                    key={intent.ID}
                    notHelpfulForm={props.notHelpfulForm}
                    intent={intent}
                    leadSummary={JSON.parse(JSON.stringify(leadSummary))}
                    CustomerIntentsList={JSON.parse(JSON.stringify(CustomerIntentsList))}
                />
            )}

            {Object.keys(leadSummary).map((title) => {
                // helpful intents will remain helpful 

                if(title === "Concerns Category"){
                    return null;
                }

                if(Array.isArray(leadSummary[title]) && leadSummary[title].length > 0 && leadSummary[title][0]["DisplayText"] && leadSummary[title][0]["DisplayText"] == "NA"){
                    return null;
                }

                return <React.Fragment key={title}>
                    {props.notHelpfulForm && <p><i>{Array.isArray(leadSummary[title]) && leadSummary[title].length > 0 && leadSummary[title].filter((intent) => intent.Response === Feedback.NOT_MARKED).length > 0 && title}</i></p>}

                    {!props.notHelpfulForm && <p><i>{Array.isArray(leadSummary[title]) && title}</i></p>}
                    {Array.isArray(leadSummary[title]) && leadSummary[title].map((intent) =>
                        <IntentListItem
                            key={intent.ID}
                            notHelpfulForm={props.notHelpfulForm}
                            intent={intent}
                            leadSummary={JSON.parse(JSON.stringify(leadSummary))}
                            CustomerIntentsList={JSON.parse(JSON.stringify(CustomerIntentsList))}
                        />
                    )}
                </React.Fragment>

            })}
        </div>
    </>
})

const LastContactedSection = () => {

    const ParentLeadId = useSelector(state => state.salesview.parentLeadId);
    const [LastContactedOn, setLastContactedOn] = useState(0);

    useEffect(() => {
        let _LstAgentLeads = getSearchObjFromLS();
        setLastContactedOn(0);
        _LstAgentLeads.forEach(lead => {
            if (lead && lead.LeadID === ParentLeadId && lead.Call && lead.Call.calltime) {
                setLastContactedOn(new Date(lead.Call.calltime));
            }
        });
    }, [ParentLeadId])

    return <>
        <p className="TableHeading">Last Contacted On :</p>
        <li className="hidebullet">{LastContactedOn ? dayjs(LastContactedOn).format("Do MMM YYYY @ hh:mm A") : "Not Available"}</li>

        <hr />
    </>
}

const RequirementCardActions = (props) => {
    const dispatch = useDispatch()
    const CustomerIntentsList = useSelector(({ salesview }) => salesview.CustomerIntentsList);
    const parentLeadId = useSelector(state => state.salesview.parentLeadId);
    const { enqueueSnackbar } = useSnackbar();
    const [showSubmitBtn, setShowSubmitBtn] = useState(true);
    const leadSummary = CustomerIntentsList.lead_summary;

    const handleClose = () => {
        dispatch(updateStateInRedux({ key: "hideCustomerIntentsCard", value: true }));
        gaEventTracker('AIIntentCard_closed', User.EmployeeId, rootScopeService.getLeadId());
    }
    const HandleAction = (action) => {
        let FeedbackGiven = [];
        let CallDataID = getLastCallID(parentLeadId);
        let UserID = User.UserId
        const SummaryFeedBack = leadSummary;

        switch (action) {
            case actions.HELPFUL:
                gaEventTracker('AIIntentCard_Helpful', User.EmployeeId, rootScopeService.getLeadId());

                Object.keys(SummaryFeedBack).forEach((title) => {
                    Array.isArray(SummaryFeedBack[title]) && SummaryFeedBack[title].forEach((intent) => { 
                        intent.Response = intent.feedback || 1;
                        delete intent.feedback;
                    })                
                })
                sendIntentFeedbackV1(parentLeadId, SummaryFeedBack, CustomerIntentsList.call_summary)
                .then((res) => {
                    if (res.status_code === 200) {
                        enqueueSnackbar('Success', { variant: 'success', autoHideDuration: 2000 });
                        props.setCardState(views.SUMMARY);
                    } else {
                        enqueueSnackbar('Feedback not saved, Please try later', { variant: 'error', autoHideDuration: 3000 })
                    }
                }).finally(() => {
                    setShowSubmitBtn(true);
                    dispatch(updateStateInRedux({ key: 'RefreshCustomerPitchedIntent', value: true }));
                });
                break;
            case actions.NOT_HELPFUL:
                gaEventTracker('AIIntentCard_NotHelpful', User.EmployeeId, rootScopeService.getLeadId());
                props.setCardState(views.FEEDBACK);
                break;
            case actions.SUBMIT_FEEDBACK:

                Object.keys(SummaryFeedBack).forEach((title) => {
                    Array.isArray(SummaryFeedBack[title]) && SummaryFeedBack[title].forEach((intent) => {
                        intent.Response = intent.feedback || 1;
                        delete intent.feedback;
                    })
                })

                setShowSubmitBtn(false);
                let notHelpfulCount = 0, helpfulCount = 0;
                try {
                    Object.keys(SummaryFeedBack).forEach((title) => {
                        Array.isArray(SummaryFeedBack[title]) && SummaryFeedBack[title].forEach((intent) => {
                            if (intent.Response === Feedback.NOT_HELPFUL) {
                                notHelpfulCount += 1;
                            } else {
                                helpfulCount += 1;
                            }
                        })
                    })
                } catch { }
                if (notHelpfulCount === 0) {
                    enqueueSnackbar('Please select atleast one option to submit.', { variant: 'error', autoHideDuration: 3000 });
                    setShowSubmitBtn(true);
                    return;
                }
                sendIntentFeedbackV1(parentLeadId, SummaryFeedBack, CustomerIntentsList.call_summary)
                .then((res) => {
                    if (res.status_code === 200) {
                        enqueueSnackbar('Success', { variant: 'success', autoHideDuration: 2000 });
                        props.setCardState(views.SUMMARY);
                    } else {
                        enqueueSnackbar('Feedback not saved, Please try later', { variant: 'error', autoHideDuration: 3000 })
                    }
                }).finally(() => {
                    setShowSubmitBtn(true);
                    dispatch(updateStateInRedux({ key: 'RefreshCustomerPitchedIntent', value: true }));
                });

                break;
            default:
                break;
        }
    }

    let checkForNotMarked = Object.keys(leadSummary).map((title) => {
        return Array.isArray(leadSummary[title]) && leadSummary[title].length > 0 && leadSummary[title].filter((intent) => intent.Response === Feedback.NOT_MARKED).length > 0
    })
    //const showFeedbackActions = isLeadAssignedToUser(parentLeadId) && Array.isArray(leadSummary) && leadSummary.filter((intent) => intent.Response === Feedback.NOT_MARKED).length > 0;
    const showFeedbackActions = (isLeadAssignedToUser(parentLeadId)) && typeof leadSummary === 'object' && !(leadSummary === null) && checkForNotMarked.indexOf(true) > -1;
    return <div className="bottom-section">
        {props.cardState === views.SUMMARY &&
            <>
                {showFeedbackActions &&
                    <>
                        <div onClick={() => { HandleAction(actions.HELPFUL) }}>
                            <img src={CONFIG.PUBLIC_URL + "/images/like.svg"} alt="Helpful" />
                            <span>Helpful</span>
                        </div>
                        <div onClick={() => { HandleAction(actions.NOT_HELPFUL) }}>
                            <img src={CONFIG.PUBLIC_URL + "/images/dislike.svg"} alt="Not Helpful" />
                            <span>Not Helpful</span>
                        </div>
                    </>
                }
                {!showFeedbackActions && 
                    <div onClick={handleClose}>
                        <img src={CONFIG.PUBLIC_URL + "/images/cross.svg"} alt="Close" />
                        <span>Close</span>
                    </div>
                }
            </>
        }
        {props.cardState === views.FEEDBACK && showSubmitBtn &&
            <>
                <Button
                    className="submitBtn"
                    onClick={() => { HandleAction(actions.SUBMIT_FEEDBACK) }}
                >
                    Submit
                </Button>
            </>
        }
    </div>
}

// const SupplierPlansSection = () => {
//     return <>
//         <p className="TableHeading">Customer was checking out:</p>
//         <li>Aditya Birla</li>
//         <li>Care Supreme</li>
//         <hr />
//     </>
// }
const CardLayout = (props) => {
    return <div className={`PitchCard vertical`} >
        {props.showBackButton &&
            <div>
                <Button
                    className="backBtn"
                    startIcon={<KeyboardBackspaceRounded />}
                    onClick={() => { props.setCardState(views.SUMMARY) }}
                >
                    Back
                </Button>



            </div>
        }
        { !props.showBackButton &&
            <img alt="" src={CONFIG.PUBLIC_URL + "/images/expand_open.svg"} className="openNewtabIcon" onClick={() => { 
                props.setCardState(views.SUMMARY) 
                props.SetOpenCallwisePopup()
            }} />
        }
        <h4 className="heading">Customer Summary</h4>
        {props.children}
    </div>
}
const CustomerRequirementsCard = () => {
    const [cardState, setCardState] = useState(views.SUMMARY);
    const [openCallwisePopup, SetOpenCallwisePopup] = useState(false);
    const [popupOpenTime, setPopupOpenTime] = useState(null);

    const handleCallwisePopupOpen = () => {
        setPopupOpenTime(Date.now());
        SetOpenCallwisePopup(true);
        gaEventTracker('AIIntentCard_CallwisePopupOpen', User.EmployeeId, rootScopeService.getLeadId());
    }

    const handleCallwisePopupClose = () => {
        if (popupOpenTime) {
            const duration = Date.now() - popupOpenTime;
            const durationInSeconds = Math.floor(duration / 1000);
            gaEventTracker('AIIntentCard_CallwisePopupDuration', User.EmployeeId, durationInSeconds);
        }
        SetOpenCallwisePopup(false);
        setPopupOpenTime(null);
    }

    return <div>
        <CardLayout showBackButton={cardState !== views.SUMMARY} setCardState={setCardState} SetOpenCallwisePopup={handleCallwisePopupOpen}
        >
            {cardState === views.FEEDBACK && <>
                <CustomerPitchDetails notHelpfulForm />
            </>}    
            {cardState === views.SUMMARY && <>
                <ul>
                    <LastContactedSection />
                    {/* <SupplierPlansSection /> */}
                    <CustomerPitchDetails />
                </ul>
            </>}
            <RequirementCardActions setCardState={setCardState} cardState={cardState} />
        </CardLayout >
        <CustomerInsurerAIPopup open={openCallwisePopup} handleClose={handleCallwisePopupClose}/>
    </div>
}

export default CustomerRequirementsCard;

// SERVICES
const sendIntentFeedback = async (LeadID, Feedback, UserID, CalldataID) => {
    if (!LeadID) {
        return {
            status: -1,
            error: "LeadId is required"
        }
    }
    if (!typeof Feedback === 'object' && !Feedback !== null) {
        return {
            status: -1,
            error: "Feedback should be an object"
        }
    }
    const requestData = {
        LeadID,
        UserID,
        CalldataID,
        Feedback
    }

    return saveIntentFeedbackService(requestData);
}

const sendIntentFeedbackV1 = async (LeadID, Feedback, callSummary) => {
    if (!LeadID) {
        return {
            status: -1,
            error: "LeadId is required"
        }
    }
    if (!typeof Feedback === 'object' && !Feedback !== null) {
        return {
            status: -1,
            error: "Feedback should be an object"
        }
    }
    const requestData = {
        "lead_id": LeadID,
        "call_summary": callSummary,
        "lead_summary": Feedback
    }

    return saveSummaryFeedbackService(requestData);
}
// HELPERS
const actions = {
    HELPFUL: "HELPFUL",
    NOT_HELPFUL: "NOT_HELPFUL",
    CLOSE: "CLOSE",
    SUBMIT_FEEDBACK: "SUBMIT_FEEDBACK"
}

const Feedback = {
    HELPFUL: 1,
    NOT_HELPFUL: 2,
    NOT_MARKED: 0
}
const views = {
    SUMMARY: "SUMMARY", // inital view
    SUBMIT_FEEDBACK: "SUBMIT_FEEDBACK"
}
const getLastCallID = (ParentLeadId) => {
    let _LstAgentLeads = getSearchObjFromLS();
    let callDataId = 1;
    _LstAgentLeads.forEach(lead => {
        if (lead && lead.LeadID === ParentLeadId && lead.Call && lead.Call.uid) {
            callDataId = lead.Call.uid;
        }
    });
    return callDataId;
}

const isLeadAssignedToUser = (ParentLeadId) => {
    let _LstAgentLeads = getSearchObjFromLS();
    let isAssigned = false;
    _LstAgentLeads.forEach(lead => {
        if (lead && lead.LeadID === ParentLeadId) {
            isAssigned = true;
        }
    });
    return isAssigned;
}