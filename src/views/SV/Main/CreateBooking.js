import React, { Fragment, useCallback, useEffect, useState, useRef } from "react";
import { Button, FormControlLabel, Grid, TextField, Radio, Tooltip } from "@mui/material";
import ExpandMore from '@mui/icons-material/ExpandMore';
import { SelectDropdown } from "../../../components";
import masterService from "../../../services/masterService";
import rootScopeService from "../../../services/rootScopeService";
import { default as Data } from "../../../assets/json/StaticData";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker as DatePickerMUI } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { connect, useSelector } from "react-redux";
import { CALL_API } from "../../../services";
import { default as ProductVisibility } from "../../../assets/json/productWiseColumns";
import { Autocomplete } from "@mui/material";
import { useSnackbar } from "notistack";
import { IsValidItem, isValidApplicationNumber, IsValidBrokerage, IsFosAgentForBrokerage, IsSmeHouseHold, IsSmePropertyProduct } from "./helpers/CreateBooking";
import User from "../../../services/user.service";
import TextInput from "../../../components/TextInput";
import { setIsCallCreateBooking, setRefreshLead, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import Checkbox from '@mui/material/Checkbox';
import { SV_CONFIG } from "../../../appconfig";
import { AddCircleOutline } from "@mui/icons-material";
import DeleteIcon from '@mui/icons-material/Delete';
import { debounce } from "lodash";
import { FamilyTypes, SumInsuredTypeOptions, LDTypes, YesNo } from "./helpers/Constants";
import { SetCustomerComment, GetRejectLeadsInput, GetRejectLeads, API_STATUS, SetLeadAudit } from "../../../services/Common";
import dayjs from "dayjs";
import { DocsUpload } from "./Modals/DocsUpload";
import { useDispatch } from "react-redux";
import { ToWords } from 'to-words';
import MultipleSelectDropdown from "../../../components/MultipleSelectDropdown";
import { AnualOpenLeadComments } from "./Modals/AnualOpenLeadComments";
import ErrorBoundary from "../../../hoc/ErrorBoundary/ErrorBoundary";
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import { GetCustomerOpenLeads, GetSubStatusV2 } from '../../../services/Common';
import LeadRejectionPrompt from './Modals/LeadRejectionPrompt';
import { currency } from "../../../utils/utility";

const icon = <CheckBoxOutlineBlankIcon fontSize="small" />;
const checkedIcon = <CheckBoxIcon fontSize="small" />;

const DatePicker = (props) => {
    let { show = true, name, label, value, handleChange } = props;
    let { format = "dd/MM/yyyy", disableToolbar = false, disabled = false } = props;
    let { sm = 6, md = 4, xs = 12 } = props;

    if (!show) return null;
    return (
        <Grid item sm={sm} md={md} xs={xs}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePickerMUI
                    disableToolbar={disableToolbar}
                    inputFormat={format}
                    id={name}
                    inputVariant="outlined"
                    label={label}
                    value={value ? new Date(value) : value}
                    fullWidth
                    disabled={disabled}
                    onChange={(newValue) => handleChange({ target: { name, value: newValue } })}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            variant="outlined"
                            name={name}
                            fullWidth={true}
                            InputProps={{
                                ...params.InputProps,
                                readOnly: true,
                            }}
                        />
                    )}
                    autoOk={true}
                    leftArrowButtonProps={{ color: "inherit" }}
                    rightArrowButtonProps={{ color: "inherit" }}
                />
            </LocalizationProvider>
        </Grid>
    )
}
let newOrderStructure = {
    leadId: '',
    LeadSource: '',
    ProductId: '',
    SubProduct: {
        ID: 0,
    },
    ActiveLead: {},
    CityState: {
        CityStateName: ''
    },
    PolicyType: {
        PolicyTypeId: ''
    },
    BookingType: '',
    Supplier: {
        OldSupplierId: '',
        SupplierId: ''
    },
    Plan: {
        OldSupplierId: '',
    },
    ReferenceNo: '',
    DateOfInspection: '',
    Medical_or_InspectionRequired: '',
    InspectionStatus: '',
    ODPremium: '',
    PaidPremium: 0,
    CompanyName: '',
    LoadingAmount: '',
    NoOfLives: '',
    NoOfEmployees: '',
    DocumentsRequired: 0,
    IsDocReceived: false,
    payment: {
        PaymentStatus: ''
    },
    Occupation: '',
    TransitType: '',
    TermTenure: '',
    TermSI: '',
    SumInsured: '',
    TotalPremium: '',
    PrevPolicyNo: '',
    InsuredName: '',
    PreviousBookingNo: '',
    InstallmentPaid: 0,
    ApplicationNo: '',
    PolicyNo: '',
    ShowPaymentDetails: '',
    ContentSI: 0,
    BuildingSI: 0,
    ExpiringInsurer: 0,
    PolicyDocName: '',
    DocumentId: '',
    SalesPartners: [],
    SalesSpecialists: [],
    SumInsuredType: {
        Id: 0
    },
    FamilyType: {
        Id: 0
    },
    Grades: [],
    PrimaryAgentSharePercentage: '',
    ProposalNo: '',
    ShipmentType: '',
    InlandSI: '',
    ImportSI: '',
    ExportSI: '',
    MerchantTradingSI: '',
    PAN: '',
    GST: '',
    CIN: '',
    Name: '',
    IsTP: '',
    IsSTP: null,
    MarineCoverType: 0,
    InstallmentsData: [],
    CoInsurance: 0,
    LeadersPercentage: '',
    OccupationType: '',
    ManufacturerTraderName: '',
    ManufacturerTraderContactNo: '',
    ConstitutionOfBusiness: '',
    LD: {
        Id: 0
    },
    LDAmount: null,
    Association: {
        Id: 0,
    },
    BookingFrom: {
        Id: '',
    },
    PolicyCategory: {
        Id: '',
    },
    QuoteId: '',
    ChildOccupation: '',
    TerrorismPremium: 0,
    TerrorismPremiumVal: 0,
    BurglaryPremium: 0,
    BurglaryPremiumVal: 0,
    FirePremium: 0,
    BookingCategory:{
        Id:''
    },
    ProjectDuration: null,
    PlanType:{
        Id: ''
    }
}

const UploadDocuments = ({ leadId, subProductId, isSmePos, transitType, IsSmeFosAgent, IsSmeFosAgentGroup }) => {
    let DocsUploadPanel = useSelector(state => state.salesview.DocsUploadPanel);
    const reduxDispatch = useDispatch();
    return (
        <DocsUpload
            open={DocsUploadPanel}
            handleClose={() => { reduxDispatch(updateStateInRedux({ key: "DocsUploadPanel", value: false })) }}
            LeadId={leadId}
            SubProductId={subProductId}
            IsSmePOS={isSmePos}
            TransitType={transitType}
            IsSmeFosAgent={IsSmeFosAgent}
            IsSmeFosAgentGroup={IsSmeFosAgentGroup}
        />
    );
}

function CreateBooking(props) {
    let [IsCallCreateBooking] = useSelector(({ salesview }) => [salesview.IsCallCreateBooking]);
    let createBookingData = useSelector(({ salesview }) => salesview.CreateBookingData);
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);



    const [ShowCreateBookingSection, setShowCreateBookingSection] = useState(false);
    const { enqueueSnackbar } = useSnackbar();
    const [CallGetBookingDetails, setCallGetBookingDetails] = useState(false);
    const [callGetCustomerOrder, setcallGetCustomerOrder] = useState(false);
    const [isDisabled, setIsDisabled] = useState(true);
    const [IsdisabledPED, setIsdisabledPED] = useState(false);
    const [IsSIdisabled, setIsSIdisabled] = useState(false);
    const [Visible, setVisible] = useState({});
    const [activeLeadsWithoutOrder, setActiveLeadsWithoutOrder] = useState([]);
    const [OrderList, setOrderList] = useState([]);
    const [SIPerPerson, setSIPerPerson] = useState(false);
    const [IsNotPaySubMode, setIsNotPaySubMode] = useState(true);
    const [AllPaymentModes, setAllPaymentModes] = useState([]);
    // const [OTP, setOTP] = useState('');
    const [disableOTP, setdisableOTP] = useState(false);

    const [newOrder, setNewOrder] = useState(newOrderStructure);
    const [OrderListOriginal, setOrderListOriginal] = useState([]);
    const [NewOrderOriginal, setNewOrderOriginal] = useState(null);

    const [ValidationData, setValidationData] = useState([]);
    const [InstallmentsData, setInstallmentsData] = useState({});
    const [SMEJson, setSMEJson] = useState({});
    const [FieldsData, setFieldsData] = useState([]);
    const [PaymentPeriodicity, setPaymentPeriodicity] = useState([]);
    const [payment, setPayment] = useState(User.Payment);
    const [isGetCustomerOrderCalled, setIsGetCustomerOrderCalled] = useState(0);
    const [IsdisabledBuildingSI, setIsdisabledBuildingSI] = useState(true);
    const [IsdisabledContentSI, setIsdisabledContentSI] = useState(true);
    const [IsDisabledExpiringPolicyNo, setIsDisabledExpiringPolicyNo] = useState(false);
    const [IsDisabledExpiringBookingId, setIsDisabledExpiringBookingId] = useState(false);
    const [IsDisabledForSmeElements, setIsDisabledForSmeElements] = useState(false);
    const [File, setFile] = useState(null);
    const [IsSaveUpdateEnabled, setIsSaveUpdateEnabled] = useState(true);
    const [SalesPartners, setSalesPartners] = useState([]);
    const [InputSalesPartner, setInputSalesPartner] = useState('');
    const [SalesSpecialists, setSalesSpecialists] = useState([]);
    const [InputSalesSpecialist, setInputSalesSpecialist] = useState('');
    const [IsLoading, setIsLoading] = useState(false);
    const [IsShowBookingDetailsSection, setIsShowBookingDetailsSection] = useState(-1);
    const [OptimzedPlansList, setOptimzedPlansList] = useState([]);
    const [OptimzedPlansListforBookedLeads, setOptimzedPlansListforBookedLeads] = useState([]);
    const [UpdateOrderPlan, setUpdateOrderPlan] = useState(false);
    const [PrimaryAgent, setPrimaryAgent] = useState('');
    const [SupplierBySubProduct, setSupplierBySubProduct] = useState([]);
    const [IsShowSupplierBySubProduct, setIsShowSupplierBySubProduct] = useState(false);
    const [IsFosSmeAgentForBrokerage, setIsFosSmeAgentForBrokerage] = useState(true);
    const [IsTPChecked, setIsTPChecked] = useState(false);
    const [InvoiceFile, setInvoiceFile] = useState(null);
    const [ShopTypes, setShopTypes] = useState([]);
    const [LeadAssignedAgentId, setLeadAssignedAgentId] = useState(0);
    const [IsNotHealthRenewal, setIsNotHealthRenewal] = useState(false);
    const [IsSmeFosAgent, setIsSmeFosAgent] = useState(false);
    const [IsSmeFosAgentGroup, setIsSmeFosAgentGroup] = useState(false);
    const refUploadFiles = useRef({})
    const reduxDispatch = useDispatch();
    const toWords = new ToWords();
    const [AnualOpenPopupLeadId, setAnualOpenPopupLeadId] = useState(null);
    const dataForBooking = useRef(null);
    const [confirmBooking, setConfirmBooking] = useState(false);
    const [showLeadRejectionPrompt, setShowLeadRejectionPrompt] = useState(false);
    const [customerOpenLeads, setCustomerOpenLeads] = useState([]);
    const [loadingOpenLeads, setLoadingOpenLeads] = useState(false);
    const [currentBookingRequestData, setCurrentBookingRequestData] = useState(null);
    const [ChildOccupancyList, setChildOccupancyList] = useState([]);
    const [Association, setAssociation] = useState([]);
    // Dynamic terrorism rates master data from API (array format)
    const [TerrorismRatesMaster, setTerrorismRatesMaster] = useState([]);

    let DocsUploadPanel = useSelector(state => state.salesview.DocsUploadPanel);
    let isSmeDocsUploadValid = useSelector(state => state.salesview.IsSmeDocsUploadValid);
    let customerId = rootScopeService.getCustomerId();
    let productId = rootScopeService.getProductId();
    let leadId = rootScopeService.getLeadId();
    let docTypeId = 56;
    let MaxSalesPartner = 1;
    let MaxSalesSpecialist = 1;
    let MaxGrades = 10;
    let invoiceDocTypeId = 67;
    let MarineAnualOpenLeadData = useSelector(state => state.salesview.MarineAnualOpenLeadData);

    let [allLeads, leadIds, IsRenewal, isCustomerVerified, parentLeadId, primaryLeadId] = useSelector(state => {
        let { allLeads, leadIds, IsRenewal, isCustomerVerified, parentLeadId, primaryLeadId } = state.salesview;
        return [allLeads, leadIds, IsRenewal, isCustomerVerified, parentLeadId, primaryLeadId]
    });
    let invalids = [undefined, null, 0, "", "0"];
    let intloanPrds = [158, 159, 160, 164, 171];
    let inspectionRequired = Data.InspectionRequired;
    let InspectionType = Data.InspectionType;
    let TransitTypes = Data.TransitTypes;
    let ShipmentTypes = Data.ShipmentTypes;
    let MarineCoverTypes = Data.MarineCoverTypes;
    let CoInsuranceTypes = Data.CoInsuranceTypes;
    let SmeGhiSubProducts = [1, 103];
    let OccupationTypes = Data.OccupationTypes;
    let ConstitutionOfBusinesses = Data.ConstitutionOfBusinesses;
    let Inclusions = Data.Inclusion;
    let isRSA = Data.IsRSA;
    let EMI = Data.EMI;
    let BookingTypes = Data.BookingType;
    let isTP = Data.IsTP;
    let CreateBookingSetFields = SV_CONFIG["CreateBookingSetFields"] ? SV_CONFIG["CreateBookingSetFields"] : '';
    let MedicalExtensions = Data.MedicalExtensions;
    let WorkerTypes = Data.WorkerTypes;

    const [options, setOptions] = useState({ //options for dropdown
        activeLeadCollection: [],
        SubProduct: [],
        supplier: [],
        plan: [],
        policyType: [],
        cities: [],
        Occupations: [],
        BookingTypes: Object.entries(Data.BookingType).map((e) => ({ label: e[0], value: e[1] })),
        paymentMode: Object.entries(productId === 117 ? Data.PaymentModeMotor : Data.PaymentMode).map((e) => ({ PaymentModeName: e[0], PaymentModeValue: e[1] })),
        // SubPaymentModes: [],
        paymentFrequency: [],
        paymentSource: Data.PaymentSource.map(ps => ({ label: ps, value: ps })),
        SMEPlanDeal: [],
        CoverTypes: [],
        PropertyPurpose: [],
        PropertyTypes: [],
        SubStatusList: [
            { 'SubStatusId': 0, 'Name': 'Select' },
            { 'SubStatusId': 1872, 'Name': 'Bureau Approved' },
            { 'SubStatusId': 1873, 'Name': 'Lead Approved' },
            { 'SubStatusId': 1874, 'Name': 'Card Issued' }
        ],
        IsSTPOptions: Object.entries(Data.IsSTP).map((e) => ({ label: e[0], value: e[1] })),
        SmePropertyTypes: [],
    })
    const [SubPaymentModesOptions, setSubPaymentModesOptions] = useState([]);
    useEffect(() => {
        setOrderList([]);
        setActiveLeadsWithoutOrder([]);
        if (leadIds) {
            getActiveLeads()
            // if (options.supplier.length > 0 && options.plan.length > 0){
            //     // GetCustomerOrder();
            // }
        }
    }, [leadIds])
    useEffect(() => {
        if (RefreshLead) {
            setOrderList([]);
            setActiveLeadsWithoutOrder([]);
            setShowCreateBookingSection(false);
            if (leadIds) {
                getActiveLeads()
                if (Array.isArray(options.supplier) && options.supplier.length > 0
                    //&& Array.isArray(options.plan) && options.plan.length > 0
                ) {
                    setcallGetCustomerOrder(true);
                }
            }
        }
    }, [RefreshLead])

    useEffect(() => {
        setNewOrderInitially();
    }, [activeLeadsWithoutOrder])
    useEffect(() => {
        if (ShowCreateBookingSection) {
            onOccupationChange(newOrder, -1);
        }
    }, [ShowCreateBookingSection])
    useEffect(() => {
        //todo add isarray
        if (leadIds
            && Array.isArray(options.supplier) && options.supplier.length > 0
            //&& Array.isArray(options.plan) && options.plan.length > 0
            && ((Array.isArray(options.SubProduct) && options.SubProduct.length > 0) || [101, 2, 139, 117, 130, 118, 106, 3, 190, 114, 186, 187, 188, 194, 189, 138].includes(productId))
            && options.paymentFrequency.length > 0 && options.paymentMode.length > 0
            && !isGetCustomerOrderCalled) {
            // if (leadIds && options.supplier.length > 0 && options.plan.length > 0 && Array.isArray(options.SubProduct) && options.SubProduct.length > 0 &&
            //     options.paymentFrequency.length > 0 && options.paymentMode.length > 0 && !isGetCustomerOrderCalled) {
            GetCustomerOrder();
            setIsGetCustomerOrderCalled(1);
        }
    }, [JSON.stringify(options), leadIds])
    useEffect(() => {
        if (callGetCustomerOrder) {
            GetCustomerOrder();
            setcallGetCustomerOrder(false);
        }
    }, [callGetCustomerOrder])

    useEffect(() => {
        updateVisibleAndPolicyTypes();
    }, [productId])

    // useEffect(() => {
    //     if (OrderList.length > 0 && CallGetBookingDetails) {
    //         //GetBookingDetails(0, OrderList[0].leadId,"");
    //         setCallGetBookingDetails(false);
    //     }
    // }, [OrderList, CallGetBookingDetails])

    useEffect(() => {
        getSuppliersAndPlan();
        getSubProductByProductID();
        getPaymentModes(rootScopeService.getProductId());
        getPaymentPeriodicity();
        if (rootScopeService.getProductId() === 131) {
            GetDoctorAssociations();
            LoadTerrorismRates();
        }
    }, [])

    useEffect(() => {
        if (OrderList.length > 0 && OptimzedPlansListforBookedLeads.length > 0) {
            let _OrderListCopy = JSON.parse(JSON.stringify(OrderList));
            let currentOrderIndex = IsShowBookingDetailsSection;
            // _OrderListCopy.forEach((item, index) => {
            //     if(item.ShowBookingDetails){
            //         currentOrderIndex=index;
            //     }
            // })

            let plan = getOptimizedPlanList(_OrderListCopy[currentOrderIndex]).filter((p) => p.OldPlanId == _OrderListCopy[currentOrderIndex].Plan.OldPlanId)[0];
            //console.log("plan123 293",plan);
            if (plan) {
                setOrderList(prevState => prevState.map((o, i) => {
                    const newObj = { ...o };
                    if (i == currentOrderIndex) newObj.Plan = plan;
                    //console.log("plan123 299 Order",newObj);
                    return newObj;
                }));
            }


        }
    }, [OptimzedPlansListforBookedLeads, IsShowBookingDetailsSection])

    useEffect(() => {

        if (UpdateOrderPlan && OrderList.length > 0 && OptimzedPlansListforBookedLeads.length > 0) {
            let _OrderListCopy = JSON.parse(JSON.stringify(OrderList));
            let currentOrderIndex = IsShowBookingDetailsSection;

            let plan = getOptimizedPlanList(_OrderListCopy[currentOrderIndex]).filter((p) => p.OldPlanId === _OrderListCopy[currentOrderIndex].Plan.OldPlanId)[0];
            if (plan) {
                setOrderList(prevState => prevState.map((o, i) => {
                    const newObj = { ...o };
                    if (i == currentOrderIndex) newObj.Plan = plan;
                    //console.log("plan123 318 Order",newObj);
                    return newObj;
                }));
            }
            setUpdateOrderPlan(false);
        }
    }, [UpdateOrderPlan])
    const ShowOrderSection = (args) => {
        if (invalids.indexOf(args.OrderLeadId) == -1 || args.OrderLeadId == 0) {
            setIsDisabled(true);
            setNewOrder({
                ...newOrderStructure,
                leadId: args.OrderLeadId,
                ActiveLead: 0,
                ShowPaymentDetails: false
            });
            if (args.OrderLeadId == 0) {
                setShowCreateBookingSection(false);
            }
            else {
                GetCustomerBookingDetailsService();
            }
        }
        else {
            let activeLeadCollection = getActiveLeads();
            setShowCreateBookingSection(true);
            let ProductId = rootScopeService.getProductId();
            const input = {
                url: "api/Master/GetProductPlans?ProductId=" + ProductId + "&supplierId=" + args.SupplierId + "&Source=Booking",
                method: 'GET',
                service: 'MatrixCoreAPI',
            }
            CALL_API(input).then((response) => {
                setIsDisabled(false);
                setOptimzedPlansList(response);
                let SubProductId = 1;
                let Optimizedplans = response;
                var plans = Optimizedplans.filter((item) => {
                    return (
                        item.ProductId === rootScopeService.getProductId() &&
                        item.OldSupplierId === args.SupplierId &&
                        item.OldPlanId === args.PlanId
                    )
                });
                if (plans != null && plans.length > 0) {
                    SubProductId = plans[0].SubProductTypeID;
                }
                setNewOrder({
                    ...newOrderStructure,
                    leadId: args.leadId,
                    ProductId: rootScopeService.getProductId(),
                    SubProduct: {
                        ID: SubProductId
                    },
                    ActiveLead: activeLeadCollection.filter((o) => o.leadId == args.leadId)[0],
                    PolicyType:
                    {
                        PolicyTypeId: GetPolicyType()
                    },
                    BookingType: 1,
                    TotalPremium: args.Premium,
                    SumInsured: args.SumInsured,
                    Supplier: options.supplier.filter((item) =>
                        item.ProductId === rootScopeService.getProductId() &&
                        item.OldSupplierId === args.SupplierId &&
                        item.SubCategoryId === SubProductId
                    )[0],
                    //Plan: getOptimizedPlanList(_OrderList[index]).filter((p) => p.OldPlanId == _OrderList[index].Plan.OldPlanId)[0];
                    Plan: Optimizedplans.filter((item) =>
                        item.ProductId === rootScopeService.getProductId() &&
                        item.OldPlanId === args.PlanId &&
                        item.OldSupplierId === args.SupplierId &&
                        item.SubProductTypeID === SubProductId
                    )[0],
                    //Plan : [],
                    ReferenceNo: '',
                    DateOfInspection: '',
                    Medical_or_InspectionRequired: 2,
                    InspectionStatus: {
                        ID: 0,
                        Name: ''
                    },
                    ODPremium: '',
                    PaymentDate: '',
                    ProposalNo: ''
                });
            });
        }

    };

    useEffect(() => {
        if (IsCallCreateBooking && createBookingData.hasOwnProperty('leadId')) {
            ShowOrderSection(createBookingData);
            props.setIsCallCreateBookingToRedux(false);
        }
    }, [IsCallCreateBooking, createBookingData]);

    const GetSalesPartnersDebounce = useCallback(debounce(() => GetSalesPartnerAndSalesSpecialist('SalesPartners'), 200), [InputSalesPartner]);

    useEffect(() => {
        if (InputSalesPartner == '' || InputSalesPartner.length < 3) {
            return undefined;
        }
        GetSalesPartnersDebounce(InputSalesPartner);
    }, [InputSalesPartner]);

    const GetSalesSpecialistsDebounce = useCallback(debounce(() => GetSalesPartnerAndSalesSpecialist('SalesSpecialists'), 200), [InputSalesSpecialist]);

    useEffect(() => {
        if (InputSalesSpecialist == '' || InputSalesSpecialist.length < 3) {
            return undefined;
        }
        GetSalesSpecialistsDebounce(InputSalesSpecialist);
    }, [InputSalesSpecialist]);

    useEffect(() => {
        if (productId == 131 && newOrder.ShowPaymentDetails) {
            GetPrimaryAgent(newOrder.leadId);
        }
    }, [newOrder.ShowPaymentDetails]);

    useEffect(() => {
        if (rootScopeService.getProductId() === 131) {
            setIsFosSmeAgentForBrokerage(IsFosAgentForBrokerage());
        }
        else {
            setIsFosSmeAgentForBrokerage(true);
        }
    }, [])

    useEffect(() => {
        if (productId == 131) {
            GetLeadAssignedAgentDetails().then((response) => {
                if (response && response.UserId) {
                    setLeadAssignedAgentId(response.UserId);
                    if (response.ManagerId) {
                        reduxDispatch(updateStateInRedux({ key: "LeadAssignedManagerId", value: response.ManagerId }))
                        reduxDispatch(updateStateInRedux({ key: "LeadAssignedAgentId", value: response.UserId }))
                    }
                }
            });
        }
    }, [])

    useEffect(() => {
        if (confirmBooking && dataForBooking && dataForBooking.current) {
            let { reqData, result, item } = dataForBooking.current;
            try {
                ConfirmPaymentForLead(reqData, result, item);
            }
            catch (e) {
                console.log(e);
            }
            finally {
                setConfirmBooking(false);
            }
        }
    }, [confirmBooking])

    const getSuppliersAndPlan = () => {
        if (productId === 131) {
            masterService.getSupplierByProduct(131).then(suppliers => {
                setOptions(prevState => ({ ...prevState, supplier: suppliers }));
                setSupplierBySubProduct(suppliers);
            })
        }
        else {
            masterService.getSuppliers().then(res => {
                setOptions(prevState => ({ ...prevState, supplier: res }));
            })
        }
    }

    const handleToggle = (e) => {
        GetOrderDetail();
        AutoSelectLeadAndSubProduct(true);
    };

    const AutoSelectLeadAndSubProduct = (isToggleOpen, leadId = 0) => {
        try {
            let isSingleLead = true;
            if (isToggleOpen && options.activeLeadCollection && options.activeLeadCollection.length === 1) {
                ActiveLeadChanged({ ...newOrder, "ActiveLead": options.activeLeadCollection[0] }, -1);
                setNewOrder(prevState => ({ ...prevState, "ActiveLead": options.activeLeadCollection[0], leadId: options.activeLeadCollection[0].leadId }));
            }
            else {
                isSingleLead = false;
            }

            if (isToggleOpen && isSingleLead) {
                if (productId === 131 && newOrder && newOrder.ActiveLead && newOrder.ActiveLead.leadId && newOrder.ActiveLead.leadId > 0) {
                    let subProductId = allLeads.find((lead) => (lead.LeadID == newOrder.ActiveLead.leadId)).SubProductTypeId;
                    let subProduct = options.SubProduct.find((sub) => (sub.ID == subProductId));
                    setNewOrder(prevState => ({ ...prevState, "SubProduct": subProduct }));
                    OnSubProductChange({ ...newOrder, "SubProduct": subProduct }, -1, true);
                    FetchSmeRenewal(newOrder.ActiveLead.leadId);
                }
            }
            else if (!isToggleOpen) {
                if (productId === 131 && newOrder && newOrder.ActiveLead && newOrder.ActiveLead.leadId && newOrder.ActiveLead.leadId > 0) {
                    let subProductId = allLeads.find((lead) => (lead.LeadID == leadId)).SubProductTypeId;
                    let subProduct = options.SubProduct.find((sub) => (sub.ID == subProductId));
                    setNewOrder(prevState => ({ ...prevState, "SubProduct": subProduct }));
                    OnSubProductChange({ ...newOrder, "SubProduct": subProduct }, -1, true);
                    FetchSmeRenewal(leadId);
                }
            }
        }
        catch (e) {
            console.log(e);
        }
    }

    const handleChange = (e, key1, key2) => {
        // handle input changes for Create Booking

        let val = e.target.value;
        let e_name = e.target.name;

        if (key1 && key2) {
            setNewOrder(prevState => ({ ...prevState, [key1]: { ...newOrder[key1], [key2]: val } }));
        }
        else if (!CreateBookingSetFields.includes(e_name)) {
            setNewOrder(prevState => ({ ...prevState, [e_name]: val }));
        }

        // Auto-calculate terrorism premium for sub-product IDs 5, 7, 8 only when terrorism premium is included
        if (e_name === 'TerrorismPremiumVal' && val && val.Id === 1 && productId == 131 &&
            newOrder.SubProduct && [5, 7, 8].indexOf(newOrder.SubProduct.ID) !== -1) {
            HandleTerrorismVal(val, -1, e_name, { ...newOrder });
        }

        // Auto-calculate when Sum Insured changes and terrorism premium is already included
        if (e_name === 'SumInsured' && productId == 131 &&
            newOrder.SubProduct && [5, 7, 8].indexOf(newOrder.SubProduct.ID) !== -1 &&
            newOrder.TerrorismPremiumVal && newOrder.TerrorismPremiumVal.Id === 1) {
            HandleSITerrorism(val, -1, e_name, { ...newOrder });
        }

        // Auto-calculate when Occupation changes and terrorism premium is already included
        if (e_name === 'Occupation' && productId == 131 &&
            newOrder.SubProduct && [5, 7, 8].indexOf(newOrder.SubProduct.ID) !== -1 &&
            newOrder.TerrorismPremiumVal && newOrder.TerrorismPremiumVal.Id === 1) {
            HandleOccTerrorism(val, -1, e_name, { ...newOrder });
        }

        switch (e_name) {
            case 'PolicyType':
                PolicyTypeChangedForCreate({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'ActiveLead':
                ActiveLeadChanged({ ...newOrder, [e_name]: val }, -1);
                setNewOrder(prevState => ({ ...prevState, [e_name]: val, leadId: val.leadId }));
                AutoSelectLeadAndSubProduct(false, val.leadId);
                break;
            case 'Supplier':
                OnSupplierChange({ ...newOrder, [e_name]: val }, -1, "CreateLead");
                break;
            case 'Plan':
                setInstallment({ ...newOrder, [e_name]: val }, -1);
                OnPlanChange({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'SubProduct':
                OnSubProductChange({ ...newOrder, [e_name]: val }, -1, true, true);
                break;
            case 'paymentMode':
                setSubPaymentModes({ ...newOrder, [key1]: { ...newOrder[key1], [key2]: val } })
                break;
            case 'paymentFrequency':
                setInstallment({ ...newOrder, [key1]: { ...newOrder[key1], [key2]: val } }, -1)
                break;
            case 'paymentSource':
                setInstallment({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'PolicyStartDate':
                OnPolicyTermChange({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'SIPerPerson':
                SetSIByNoOfLives({ ...newOrder, [e_name]: val }, SIPerPerson, -1);
                break;
            case 'NoOfEmployees':
                SetSIByNoOfLives({ ...newOrder, [e_name]: val }, SIPerPerson, -1);
                break;
            case 'Occupation':
                onOccupationChange({ ...newOrder, [e_name]: val }, -1, true);
                break;
            case 'Portability':
                setNewOrder(prevState => ({ ...prevState, [e_name]: !newOrder.Portability }));
                break;
            case 'CoverType':
            case 'BuildingSI':
            case 'ContentSI':
                OnCoverTypeChange({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'PrevPolicyNo':
                OnPrevPolicyNo({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'PreviousBookingNo':
                OnPreviousBookingNo({ ...newOrder, [e_name]: val }, -1);
                break;
            case 'TransitType':
                // Reset ShipmentType when TransitType changes to avoid type mismatch
                if (productId === 131 && newOrder.SubProduct?.ID === 13) {
                    setNewOrder(prevState => ({ ...prevState, ShipmentType: val === "Annual Open" ? [] : "" }));
                }
                break;
            case 'ShipmentType':
                // Helper function to check shipment type selection
                const isShipmentSelected = (shipmentType, type) => {
                    if (!shipmentType) return false;
                    if (Array.isArray(shipmentType)) {
                        return shipmentType.some(st => st.Id === type);
                    }
                    if (typeof shipmentType === 'string') {
                        return shipmentType === type;
                    }
                    if (shipmentType.Id) {
                        return shipmentType.Id === type;
                    }
                    return false;
                };

                // Clear InlandSI if Inland is not selected
                if (!isShipmentSelected(val, 'Inland')) {
                    setNewOrder(prevState => ({ ...prevState, InlandSI: '' }));
                }
                // Clear ImportSI if Import is not selected
                if (!isShipmentSelected(val, 'Import')) {
                    setNewOrder(prevState => ({ ...prevState, ImportSI: '' }));
                }
                // Clear ExportSI if Export is not selected
                if (!isShipmentSelected(val, 'Export')) {
                    setNewOrder(prevState => ({ ...prevState, ExportSI: '' }));
                }
                // Clear MerchantTradingSI if Merchant Trading is not selected
                if (!isShipmentSelected(val, 'Merchant Trading')) {
                    setNewOrder(prevState => ({ ...prevState, MerchantTradingSI: '' }));
                }
            break;
            case 'HandleAddNewSalesPartner':
                HandleAddNewSalesPartner({ ...newOrder }, -1);
                break;
            case 'SalesPartners':
                OnSalesPartnerChanged(e_name, val, e.target.index, { ...newOrder }, -1);
                break;
            case 'OnSalesPartnerSharePercentageChanged':
                OnSalesPartnerSharePercentageChanged('SalesPartners', val, e.target.index, { ...newOrder }, -1);
                break;
            case 'HandleSalesPartnerDelete':
                HandleSalesPartnerDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'HandleAddNewSalesSpecialist':
                HandleAddNewSalesSpecialist({ ...newOrder }, -1);
                break;
            case 'SalesSpecialists':
                OnSalesSpecialistChanged(e_name, val, e.target.index, { ...newOrder }, -1);
                break;
            case 'HandleSalesSpecialistDelete':
                HandleSalesSpecialistDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'HandleAddNewGrade':
                HandleAddNewGrade({ ...newOrder }, -1);
                break;
            case 'HandleGradeDelete':
                HandleGradeDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'GradeSumInsured':
                GradeSumInsured('Grades', val, e.target.index, { ...newOrder }, -1);
                break;
            case 'Brokerage':
                BrokerageChanged(val, { ...newOrder }, -1);
                break;
            case 'PolicyTerm':
                OnPolicyTermChange({ ...newOrder, [e_name]: val }, -1, true);
                break;
            case 'IsTP':
                OnIsTPChange(val, { ...newOrder }, -1);
                break;
            case 'emi':
                OnEMIChanged(val, { ...newOrder }, -1);
                break;
            case 'HandleAddNewInstallment':
                HandleAddNewInstallment({ ...newOrder }, -1);
                break;
            case 'InstallmentDate':
                AddInstallmentData(val, e.target.index, { ...newOrder }, -1, true);
                break;
            case 'InstallmentAmount':
                AddInstallmentData(val, e.target.index, { ...newOrder }, -1, false);
                break;
            case 'HandleInstallmentDelete':
                HandleInstallmentDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'AddFollowerSupplier':
                AddFollowerSupplier({ ...newOrder }, -1);
                break;
            case 'HandleFollowerSupplierDelete':
                HandleFollowerSupplierDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'FollowerPercentage':
                OnFollowerPercentageChanged('FollowerSuppliers', val, e.target.index, { ...newOrder }, -1);
                break;
            case 'FollowerSuppliers':
                OnFollowerSupplierChanged(e_name, val, e.target.index, { ...newOrder }, -1);
                break;
            case 'LeadersPercentage':
                OnLeadersPercentageChanged(e_name, val, { ...newOrder }, -1);
                break;
            case 'OccupationType':
                setNewOrder(prevState => ({ ...prevState, [e_name]: val, ManufacturerTraderName: '', ManufacturerTraderContactNo: '' }));
                break;
            case 'HandleAddRiskLocation':
                HandleAddRiskLocation({ ...newOrder }, -1);
                break;
            case 'HandleRiskLocationDelete':
                HandleRiskLocationDelete(e.target.index, { ...newOrder }, -1);
                break;
            case 'RiskAddress':
            case 'RiskLocationCity':
            case 'RiskCityPincode':
                AddRiskLocation(val, e.target.index, { ...newOrder }, -1, e_name);
                break;
            case 'ChildOccupancies':
                HandleChildOccupancies(val, e.target.index, { ...newOrder }, -1, e_name);
                break;
            case 'BurglaryPremium':
                HandleFirePremium(val, -1, e_name, { ...newOrder });
                break;
            case 'TerrorismPremium':
                HandleFirePremium(val, -1, e_name, { ...newOrder });
                break;
            case 'TotalPremium':
                HandleTotalPremium(val, -1, e_name, { ...newOrder });
                break;
            case 'BurglaryPremiumVal':
                HandlePremiumYesNo(val, -1, e_name, { ...newOrder });
                break;
            case 'TerrorismPremiumVal':
                HandlePremiumYesNo(val, -1, e_name, { ...newOrder });
                break;
            default:
                break;
        }
    };


    const HandleChildOccupancies = (val, index, item, indx, e_name) => {
        try {
            const unselected = ChildOccupancyList.filter(e => !val.map(m => m.ID).includes(e.ID));
            const sortedList = [...val, ...unselected]
            setChildOccupancyList(sortedList);
            if (val && val.length > 5) {
                enqueueSnackbar("There are already 5 Child Occupancy added. Please delete one or more to proceed.", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }
            if (indx == -1)
                setNewOrder(prevState => ({ ...prevState, "ChildOccupancies": val }));
            else
                item.ChildOccupancies = val;
        }
        catch {

        }

    }

    const GetNumLabel = (num) => {
        let newLabel = '';
        try {
            if (num && num > 0) {
                newLabel = toWords.convert(num, { currency: true, ignoreDecimal: true, doNotAddOnly: true });
            }
        }
        catch {
        }
        return newLabel;
    };

    const IsSmeMarineAnualOpen = (item) => {
        let isSmeMarineAnualOpen = false;
        try {
            isSmeMarineAnualOpen = item.TransitType && item.SubProduct && item.SubProduct.ID === 13;
        }
        catch {
            // Do nothing
        }
        return !!isSmeMarineAnualOpen;
    };

    const OnEMIChanged = function (val, item, index) {
        if (index === -1) {
            setNewOrder
                (
                    prevState =>
                    (
                        {
                            ...prevState,
                            payment: { ...prevState.payment, IsEMI: (val == "1" ? 1 : 0) }
                        }
                    )
                );
        }
        else {
            item.payment.IsEMI = (val == "1" ? 1 : 0);
        }
    };

    const OnIsTPChange = function (val, item, index) {
        setIsTPChecked(false);
        if (productId === 117 && item.LeadSource && item.LeadSource === "Renewal") {
            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, IsTP: val }));
                if (val == "1") {
                    setNewOrder(prevState => ({ ...prevState, SumInsured: 0 }));
                    setNewOrder(prevState => ({ ...prevState, ODPremium: 0 }));
                    setIsTPChecked(true);
                }
            }
            else {
                item.IsTP = val;
                if (val == "1") {
                    item.SumInsured = 0;
                    item.ODPremium = 0;
                    setIsTPChecked(true);
                }
            }
        }
    };

    const AddInstallmentData = function (val, index, item, indx, isDateUpdated) {
        let installments = JSON.parse(JSON.stringify(item.InstallmentsData));
        let installment = JSON.parse(JSON.stringify(installments[index]));
        let installmentValue = {};
        if (isDateUpdated) {
            installmentValue = { Date: dayjs(val).format('YYYY-MM-DD'), Amount: installment.Amount };
        }
        else {
            installmentValue = { Date: installment.Date, Amount: val };
        }
        installments[index] = installmentValue;

        if (indx == -1)
            setNewOrder(prevState => ({ ...prevState, InstallmentsData: installments }));
        else
            item.InstallmentsData = installments;
    };

    const AddRiskLocation = function (val, index, item, indx, e_name) {
        try {
            if (item.RiskLocations) {
                let locations = JSON.parse(JSON.stringify(item.RiskLocations));
                let location = locations[index];
                let value = null;

                if (e_name === 'RiskAddress') {
                    value = { RiskAddress: val, City: location.City, PinCode: location.PinCode };
                }
                else if (e_name === 'RiskLocationCity') {
                    value = { RiskAddress: location.RiskAddress, City: val, PinCode: location.PinCode };
                }
                else if (e_name === 'RiskCityPincode') {
                    value = { RiskAddress: location.RiskAddress, City: location.City, PinCode: val };
                }

                locations[index] = value;

                if (indx == -1)
                    setNewOrder(prevState => ({ ...prevState, "RiskLocations": locations }));
                else
                    item.RiskLocations = locations;
            }
        }
        catch (e) {
            console.log('AddRiskLocation: ' + e);
        }
    };

    const GradeSumInsured = function (e_name, val, index, item, indx) {
        if (IsValidGradeSumInsured(val)) {
            let grades = JSON.parse(JSON.stringify(item.Grades));
            let grade = JSON.parse(JSON.stringify(grades[index]));
            let gradeValue = { Id: grade.Id, Name: grade.Name, SumInsured: val };
            grades[index] = gradeValue;

            if (indx == -1)
                setNewOrder(prevState => ({ ...prevState, [e_name]: grades }));
            else
                item.Grades = grades;
        }
    };

    const IsSmePos = function (item) {
        try {
            return productId === 131 && item.payment &&
                item.payment.PaymentStatus && item.payment.PaymentStatus === 4001 &&
                item.payment.PaymentSubStatus !== 0 && item.payment.PaymentSubStatus
                && item.payment.PaymentSubStatus === 50;
        }
        catch {
            return false;
        }
    }

    const IsValidGradeSumInsured = function (val) {
        try {
            if (!val) {
                return true;
            }

            if (val.length !== val.trim().length) {
                enqueueSnackbar("Invalid Sum Insured number.", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }

            if (!isNaN(Number(val))) {
                if (!val.includes('.') || (val.split('.')[1]).length <= 2) {
                    if (val && val < 0) {
                        enqueueSnackbar("Invalid Sum Insured number.", { variant: 'error', autoHideDuration: 3000, });
                        return false;
                    }
                    else {
                        if ((val.includes(".") && val.length > 15) || (!val.includes(".") && val.length > 12)) {
                            enqueueSnackbar("Max 12 digits are allowed.", { variant: 'error', autoHideDuration: 3000, });
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                }
                else {
                    enqueueSnackbar("Sum Insured cannot exceed more than two decimal.", { variant: 'error', autoHideDuration: 3000, });
                    return false;
                }
            }
            else {
                enqueueSnackbar("Invalid Sum Insured number.", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }
        }
        catch {
            return false;
        }
    };

    const HandleInstallmentDelete = (index, item, indexOfIns) => {
        let installments = JSON.parse(JSON.stringify(item.InstallmentsData));
        installments.splice(index, 1);

        if (indexOfIns == -1)
            setNewOrder(prevState => ({ ...prevState, InstallmentsData: installments }));
        else
            item.InstallmentsData = installments;
    };

    const HandleRiskLocationDelete = (index, item, indexOfIns) => {
        if (item.RiskLocations) {
            let locations = JSON.parse(JSON.stringify(item.RiskLocations));
            locations.splice(index, 1);

            if (indexOfIns == -1)
                setNewOrder(prevState => ({ ...prevState, RiskLocations: locations }));
            else
                item.RiskLocations = locations;
        }
    };

    const HandleGradeDelete = (index, item, indexOfGrade) => {
        let count = 1;
        let gradesCopy = [];
        let grades = JSON.parse(JSON.stringify(item.Grades));
        grades.splice(index, 1);

        grades.forEach(grade => {
            let gradeName = "Grade " + count;
            gradesCopy.push({ Id: count, Name: gradeName, SumInsured: grade.SumInsured });
            ++count;
        })

        if (indexOfGrade == -1)
            setNewOrder(prevState => ({ ...prevState, "Grades": gradesCopy }));
        else
            item.Grades = gradesCopy;
    };

    const HandleAddNewInstallment = (item, index) => {
        let installments = [];

        if (item.InstallmentsData && item.InstallmentsData.length >= item.PayTerm) {
            enqueueSnackbar("There are already " + item.PayTerm + " installments added.", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (item.InstallmentsData) {
            let installment = JSON.parse(JSON.stringify(item.InstallmentsData));
            installments = [...installment, { Date: null, Amount: null }];
        }
        else {
            installments = [{ Date: null, Amount: null }];
        }

        if (index == -1)
            setNewOrder(prevState => ({ ...prevState, "InstallmentsData": installments }));
        else
            item.InstallmentsData = installments
    };

    const HandleAddRiskLocation = (item, index) => {
        let riskLocation = [];
        let riskLocations = [];
        if (item.RiskLocations && item.RiskLocations.length >= 5) {
            enqueueSnackbar("There are already 5 Risk Locations available. Please delete one or more to proceed.", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }
        if (item.RiskLocations) {
            riskLocation = JSON.parse(JSON.stringify(item.RiskLocations));
            riskLocations = [...riskLocation, { RiskAddress: "", City: null, PinCode: "" }];
        }
        else {
            riskLocations = [{ RiskAddress: "", City: null, PinCode: "" }];
        }

        if (index == -1)
            setNewOrder(prevState => ({ ...prevState, "RiskLocations": riskLocations }));
        else
            item.RiskLocations = riskLocations
    };

    const HandleAddNewGrade = (item, index) => {
        let grade = [];
        let grades = [];

        if (item.Grades && item.Grades.length >= MaxGrades) {
            enqueueSnackbar("There are already " + MaxGrades + " Grades available. Please delete one or more to proceed.", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (item.Grades) {
            let gradeId = item.Grades.length + 1;
            let gradeName = "Grade " + gradeId;
            grade = JSON.parse(JSON.stringify(item.Grades));
            grades = [...grade, { Id: gradeId, Name: gradeName, SumInsured: "" }];
        }
        else {
            grades = [{ Id: 1, Name: "Grade 1", SumInsured: "" }];
        }

        if (index == -1)
            setNewOrder(prevState => ({ ...prevState, "Grades": grades }));
        else
            item.Grades = grades
    };

    const AddFollowerSupplier = (item, index) => {
        if (item && item.Supplier && item.Supplier.OldSupplierId) {
            if (item.FollowerSuppliers && item.FollowerSuppliers.length >= 7) {
                enqueueSnackbar("Cannot add more than 7 followers", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }
            let followerSupplier = [];
            let followerSuppliers = [];

            if (item.FollowerSuppliers) {
                followerSupplier = JSON.parse(JSON.stringify(item.FollowerSuppliers));
                followerSuppliers = [...followerSupplier, { FollowerSupplier: {}, FollowerPercentage: "" }];
            }
            else {
                followerSuppliers = [{ FollowerSupplier: {}, FollowerPercentage: "" }];
            }

            if (index == -1)
                setNewOrder(prevState => ({ ...prevState, "FollowerSuppliers": followerSuppliers }));
            else
                item.FollowerSuppliers = followerSuppliers
        }
        else {
            enqueueSnackbar("Leader Supplier should be selected", { variant: 'error', autoHideDuration: 3000, });
        }
    };

    const HandleAddNewSalesPartner = (item, index) => {
        if (item.SalesPartners && item.SalesPartners.length >= MaxSalesPartner) {
            enqueueSnackbar("Only one Secondary agent can be added", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }
        let salesPartner = [];
        let salesPartners = [];

        if (item.SalesPartners) {
            salesPartner = JSON.parse(JSON.stringify(item.SalesPartners));
            salesPartners = [...salesPartner, { SharePercentage: "", UserId: "0", UserName: "" }];
        }
        else {
            salesPartners = [{ SharePercentage: "", UserId: "0", UserName: "" }];
        }

        if (index == -1)
            setNewOrder(prevState => ({ ...prevState, "SalesPartners": salesPartners }));
        else
            item.SalesPartners = salesPartners
    };

    const HandleAddNewSalesSpecialist = (item, index) => {
        if (item.SalesSpecialists && item.SalesSpecialists.length >= MaxSalesSpecialist) {
            enqueueSnackbar("Only one Sales specialist can be added", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        let salesSpecialist = [];
        let salesSpecialists = [];

        if (item.SalesSpecialists) {
            salesSpecialist = JSON.parse(JSON.stringify(item.SalesSpecialists));
            salesSpecialists = [...salesSpecialist, { SharePercentage: "", UserId: "0", UserName: "" }];
        }
        else {
            salesSpecialists = [{ SharePercentage: "", UserId: "0", UserName: "" }];
        }

        if (index == -1)
            setNewOrder(prevState => ({ ...prevState, "SalesSpecialists": salesSpecialists }));
        else
            item.SalesSpecialists = salesSpecialists;
    };

    const GetSalesPartnerAndSalesSpecialist = (inputPartner) => {

        let partner = InputSalesPartner;

        if (inputPartner == 'SalesSpecialists')
            partner = InputSalesSpecialist;

        const input = {
            url: `api/UserDetails/GetSalesPartnerAndSalesSpecialist/${partner}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            if (response && response.SalesPartners) {
                if (inputPartner == 'SalesSpecialists')
                    setSalesSpecialists(response.SalesPartners);
                else if (inputPartner == 'SalesPartners')
                    setSalesPartners(response.SalesPartners);
                else
                    enqueueSnackbar("Secondary Sales Agent or Sales Specialist could not be loaded. " + response.Message, { variant: 'error', autoHideDuration: 3000, });
            }
            else {
                enqueueSnackbar("Secondary Sales Agent or Sales Specialist could not be loaded. " + response.Message, { variant: 'error', autoHideDuration: 3000, });
            }
            setIsLoading(false);
        });
    };

    const HandleFollowerSupplierDelete = (index, item, indexOfBookingData) => {
        let followerSupplier = JSON.parse(JSON.stringify(item.FollowerSuppliers));
        followerSupplier.splice(index, 1);

        if (indexOfBookingData == -1) {
            setNewOrder(prevState => ({ ...prevState, "FollowerSuppliers": followerSupplier }));
        }
        else {
            item.FollowerSuppliers = followerSupplier;
        }
    };

    const HandleSalesPartnerDelete = (index, item, indexOfBookingData) => {
        let salesPartner = JSON.parse(JSON.stringify(item.SalesPartners));
        salesPartner.splice(index, 1);

        if (indexOfBookingData == -1) {
            setNewOrder(prevState => ({ ...prevState, "SalesPartners": salesPartner, PrimaryAgentSharePercentage: 100 }));
        }
        else {
            item.SalesPartners = salesPartner;
            item.PrimaryAgentSharePercentage = 100;
        }
    };
    const HandleSalesSpecialistDelete = (index, item, indexOfBookingData) => {
        let salesSpecialist = JSON.parse(JSON.stringify(item.SalesSpecialists));
        salesSpecialist.splice(index, 1);

        if (indexOfBookingData == -1)
            setNewOrder(prevState => ({ ...prevState, "SalesSpecialists": salesSpecialist }));
        else
            item.SalesSpecialists = salesSpecialist;
    };

    const OnSalesPartnerChanged = function (e_name, val, index, item, indx) {
        let salesPartner = JSON.parse(JSON.stringify(item.SalesPartners));

        let sharePercentage = salesPartner[index].SharePercentage ? salesPartner[index].SharePercentage :
            (val.SharePercentage ? val.SharePercentage : "");

        salesPartner[index] = {
            SharePercentage: sharePercentage,
            UserId: val.UserId ? val.UserId : "0",
            UserName: val.UserName ? val.UserName : ""
        };
        if (indx == -1)
            setNewOrder(prevState => ({ ...prevState, [e_name]: salesPartner }));
        else
            item.SalesPartners = salesPartner;
    };

    const OnFollowerSupplierChanged = function (e_name, val, index, item, indx) {
        let followerSupplier = JSON.parse(JSON.stringify(item.FollowerSuppliers));
        let followerPercentage = followerSupplier[index].FollowerPercentage ?
            followerSupplier[index].FollowerPercentage :
            (val.FollowerPercentage ? val.FollowerPercentage : "");
        followerSupplier[index] = {
            FollowerSupplier: val,
            FollowerPercentage: followerPercentage
        };
        if (indx == -1)
            setNewOrder(prevState => ({ ...prevState, [e_name]: followerSupplier }));
        else
            item.FollowerSuppliers = followerSupplier;
    };

    const OnSalesSpecialistChanged = function (e_name, val, index, item, indx) {
        let salesSpecialist = JSON.parse(JSON.stringify(item.SalesSpecialists));

        let sharePercentage = salesSpecialist[index].SharePercentage ? salesSpecialist[index].SharePercentage :
            (val.SharePercentage ? val.SharePercentage : "");

        salesSpecialist[index] = {
            SharePercentage: sharePercentage,
            UserId: val.UserId ? val.UserId : "0",
            UserName: val.UserName ? val.UserName : ""
        };
        if (indx == -1)
            setNewOrder(prevState => ({ ...prevState, [e_name]: salesSpecialist }));
        else
            item.SalesSpecialists = salesSpecialist;
    };

    const OnLeadersPercentageChanged = function (e_name, val, item, indx) {
        if (IsValidSharePercentage(val, "Leaders percentage")) {
            if (indx == -1) {
                setNewOrder(prevState => ({ ...prevState, [e_name]: val }));
            }
            else {
                item.LeadersPercentage = val;
            }
        }
    };

    const OnFollowerPercentageChanged = function (e_name, val, index, item, indx) {
        if (IsValidSharePercentage(val, "Followers percentage")) {
            if (item && item.LeadersPercentage && (val > item.LeadersPercentage)) {
                enqueueSnackbar("Follower's Percentage cannot be more than Leader Supplier's Percentage.", { variant: 'error', autoHideDuration: 3000, });
            }
            else {
                let followerSuppliers = JSON.parse(JSON.stringify(item.FollowerSuppliers));
                let followerSupplier = followerSuppliers[index];
                let followerSupplierWithPercentage = { FollowerSupplier: followerSupplier.FollowerSupplier, FollowerPercentage: val }
                followerSuppliers[index] = followerSupplierWithPercentage;
                if (indx == -1) {
                    setNewOrder(prevState => ({ ...prevState, [e_name]: followerSuppliers }));
                }
                else {
                    item.FollowerSuppliers = followerSuppliers;
                }
            }
        }
    };

    const OnSalesPartnerSharePercentageChanged = function (e_name, val, index, item, indx) {
        if (IsValidSharePercentage(val)) {
            let salesPartners = JSON.parse(JSON.stringify(item.SalesPartners));
            let salesPartner = JSON.parse(JSON.stringify(salesPartners[index]));
            let salesPartnerWithSharePercentage = { SharePercentage: val, UserId: salesPartner.UserId, UserName: salesPartner.UserName };
            salesPartners[index] = salesPartnerWithSharePercentage;

            let primaryAgentSharePercentage = 100 - val;
            primaryAgentSharePercentage = parseFloat(primaryAgentSharePercentage).toFixed(2);

            if (indx == -1) {
                setNewOrder(prevState => ({ ...prevState, [e_name]: salesPartners, PrimaryAgentSharePercentage: primaryAgentSharePercentage }));
            }
            else {
                item.SalesPartners = salesPartners;
                item.PrimaryAgentSharePercentage = primaryAgentSharePercentage;
            }
        }
    };

    const IsValidSharePercentage = function (val, mes = "Share percentage") {
        try {
            if (!isNaN(Number(val))) {
                if (!val || parseFloat(val) < 100) {
                    if (!val.includes('.') || (val.split('.')[1]).length <= 2) {
                        if (val && val < 0) {
                            enqueueSnackbar("Invalid " + mes + " number.", { variant: 'error', autoHideDuration: 3000, });
                            return false;
                        }
                        else {
                            return true;
                        }
                    }
                    else {
                        enqueueSnackbar(mes + " cannot exceed more than two decimal.", { variant: 'error', autoHideDuration: 3000, });
                        return false;
                    }
                }
                else {
                    enqueueSnackbar(mes + " should be less than 100 percent.", { variant: 'error', autoHideDuration: 3000, });
                    return false;
                }
            }
            else {
                enqueueSnackbar("Invalid " + mes + " number.", { variant: 'error', autoHideDuration: 3000, });
                return false;
            }
        }
        catch {
            return false;
        }
    };

    const DisableFollowerSupplier = function (option, item) {
        let isSeleceted = false;
        try {
            if (item && item.Supplier && item.Supplier.OldSupplierId == option.OldSupplierId) {
                isSeleceted = true;
            }

            if (item && Array.isArray(item.FollowerSuppliers) && item.FollowerSuppliers.length > 0) {
                let followerSupplier = item.FollowerSuppliers.find((i) => (i.FollowerSupplier.OldSupplierId == option.OldSupplierId));
                if (followerSupplier) {
                    isSeleceted = true;
                }
            }
        }
        catch {
            isSeleceted = false;
        }
        finally {
            return isSeleceted;
        }
    };

    const DisableSelectedSalesPartnerOrSalesSpecialist = function (option, item) {
        let isSeleceted = false;
        try {
            if (item && PrimaryAgent && item.PrimaryAgentSharePercentage > 0 &&
                option.UserName.includes(PrimaryAgent)) {
                isSeleceted = true;
            }
            if (item && item.SalesAgentDetails && item.SalesAgentDetails.EmployeeId &&
                option.UserName.includes(item.SalesAgentDetails.EmployeeId)) {
                isSeleceted = true;
            }
            if (!isSeleceted && OrderList && Array.isArray(OrderList) && OrderList.length > 0) {
                let userDetails = OrderList.find((i) => (i.leadId == item.leadId));
                if (userDetails && userDetails.SalesAgentDetails && userDetails.SalesAgentDetails.EmployeeId &&
                    option.UserName.includes(userDetails.SalesAgentDetails.EmployeeId)) {
                    isSeleceted = true;
                }
            }
            if (!isSeleceted && item && item.SalesPartners && Array.isArray(item.SalesPartners) && item.SalesPartners.length > 0) {
                if (!!item.SalesPartners.find((selectedPartner) => (option.UserId == selectedPartner.UserId)))
                    isSeleceted = true;
            }
            if (!isSeleceted && item && item.SalesSpecialists && Array.isArray(item.SalesSpecialists) && item.SalesSpecialists.length > 0) {
                if (!!item.SalesSpecialists.find((salesSpecialist) => (option.UserId == salesSpecialist.UserId)))
                    isSeleceted = true;
            }
        }
        catch {
            isSeleceted = false;
        }
        finally {
            return isSeleceted;
        }
    };

    const BrokerageChanged = function (val, item, index) {
        let errorMessage = IsValidBrokerage(val, item.Plan.OldPlanId, options.SMEPlanDeal);
        if (errorMessage) {
            enqueueSnackbar(errorMessage, { variant: 'error', autoHideDuration: 3000, });
        }
        else {
            if (index == -1) {
                setNewOrder(prevState => ({ ...prevState, Brokerage: val }))
            }
            else {
                item.Brokerage = val;
            }
        }
    };

    const IsCoInsurer = function (item) {
        let isCoInsurer = false;
        try {
            isCoInsurer = productId === 131 && item.CoInsurance && item.CoInsurance == 1;
        }
        catch {
            isCoInsurer = false;
        }
        return !!isCoInsurer;
    };

    const IsSmeWC = function (item) {
        let isSmeWc = false;
        try {
            isSmeWc = productId === 131 && item.SubProduct && item.SubProduct.ID === 19
        }
        catch {
            isSmeWc = false;
        }
        return !!isSmeWc;
    };
    
    const IsSmeEngineeringSubproduct = function(item) {
        let valid=false;
        try{
            valid = productId==131 && item.SubProduct && item.SubProduct.ID && [16,17,18].indexOf(item.SubProduct.ID) !=-1
        }
        catch{
            valid=false;
        }
        return !!valid;
    }

    const IsShipmentTypeSelected = function(item, type) {
        try {
            if (!item.ShipmentType) return false;
            
            // Handle multi-select case (Annual Open)
            if (Array.isArray(item.ShipmentType)) {
                return item.ShipmentType.some(shipment => shipment.Id === type);
            }
            
            // Handle single-select case (non-Annual Open)
            if (typeof item.ShipmentType === 'string') {
                return item.ShipmentType === type;
            }
            
            // Handle object case (single select with object)
            if (item.ShipmentType.Id) {
                return item.ShipmentType.Id === type;
            }
            
            return false;
        }
        catch {
            return false;
        }
    }

    const GetPrimaryAgent = function (leadId) {
        const input = {
            url: `api/UserDetails/GetPrimaryAgent/${leadId}`,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        CALL_API(input).then((response) => {
            if (response) {
                setPrimaryAgent(response);
                setNewOrder(prevState => ({ ...prevState, PrimaryAgentSharePercentage: 100 }));
            }
            else {
                enqueueSnackbar("Primary Agent name could not be loaded. " + response.Message, { variant: 'error', autoHideDuration: 3000, });
            }
        });
    };

    const GetNumOfWorker = (item, name, isNum) => {
        if (isNum) {
            if (name === "Skilled")
                return item.NoOfWorkerSkilled
            else if (name === "Semi-Skilled")
                return item.NoOfWorkerSemiSkilled
            else if (name === "Un-Skilled")
                return item.NoOfWorkerUnSkilled
            else if (name === "Other")
                return item.NoOfWorkerOther
            else
                return ''
        }
        else {
            if (name === "Skilled")
                return item.SalaryOfWorkerSkilled
            else if (name === "Semi-Skilled")
                return item.SalaryOfWorkerSemiSkilled
            else if (name === "Un-Skilled")
                return item.SalaryOfWorkerUnSkilled
            else if (name === "Other")
                return item.SalaryOfWorkerOther
            else
                return ''
        }
    }

    const GetNameOfWorker = (ename, isName) => {
        let name = ''
        if (isName) {
            if (ename === "Skilled")
                name = "NoOfWorkerSkilled"
            else if (ename === "Semi-Skilled")
                name = "NoOfWorkerSemiSkilled"
            else if (ename === "Un-Skilled")
                name = "NoOfWorkerUnSkilled"
            else if (ename === "Other")
                name = "NoOfWorkerOther"
        }
        else {
            if (ename === "Skilled")
                name = "SalaryOfWorkerSkilled"
            else if (ename === "Semi-Skilled")
                name = "SalaryOfWorkerSemiSkilled"
            else if (ename === "Un-Skilled")
                name = "SalaryOfWorkerUnSkilled"
            else if (ename === "Other")
                name = "SalaryOfWorkerOther"
        }
        return name
    }

    const GetInsuredScopeValue = (item, name) => {
        if (name === "Building")
            return item.BuildingValue
        else if (name === "Content")
            return item.ContentValue
        else if (name === "Stock")
            return item.StockValue
        else
            return ''
    }

    const GetInsuredScopeName = (name) => {
        if (name === "Building")
            return "BuildingValue"
        else if (name === "Content")
            return "ContentValue"
        else if (name === "Stock")
            return "StockValue"
        else
            return ''
    }

    const handleChangeForBooking = (e, item, index, key1, key2) => {

        //console.log(e, key1, key2);
        // handle input changes for Booking Details
        let val = e.target.value;
        let e_name = e.target.name;
        let updated_item = { ...item };
        if (key1 && key2) {
            updated_item[key1] = { ...updated_item[key1], [key2]: val };
            // setNewOrder(prevState => ({ ...prevState, [key1]: { ...newOrder[key1], [key2]: val } }));
        }
        else if (!CreateBookingSetFields.includes(e_name)) {
            updated_item[e_name] = val;
        }

        // Auto-calculate terrorism premium for sub-product IDs 5, 7, 8 only when terrorism premium is included
        if (e_name === 'TerrorismPremiumVal' && val && val.Id === 1 && productId == 131 &&
            updated_item.SubProduct && [5, 7, 8].indexOf(updated_item.SubProduct.ID) !== -1) {
            HandleTerrorismVal(val, e.target.index, e_name, updated_item);
        }

        // Auto-calculate when Sum Insured changes and terrorism premium is already included in booking items
        if (e_name === 'SumInsured' && productId == 131 &&
            updated_item.SubProduct && [5, 7, 8].indexOf(updated_item.SubProduct.ID) !== -1 &&
            updated_item.TerrorismPremiumVal && updated_item.TerrorismPremiumVal.Id === 1) {
            HandleSITerrorism(val, e.target.index, e_name, updated_item);
        }

        // Auto-calculate when Occupation changes and terrorism premium is already included in booking items
        if (e_name === 'Occupation' && productId == 131 &&
            updated_item.SubProduct && [5, 7, 8].indexOf(updated_item.SubProduct.ID) !== -1 &&
            updated_item.TerrorismPremiumVal && updated_item.TerrorismPremiumVal.Id === 1) {
            HandleOccTerrorism(val, e.target.index, e_name, updated_item)
        }

        switch (e_name) {
            case 'PolicyType':
                PolicyTypeChangedForEdit(updated_item, index)
                break;
            case 'Supplier':
                OnSupplierChange(updated_item, index, "BookedLead");
                break;
            case 'Plan':
                setInstallment(updated_item, index);
                OnPlanChange(updated_item, index);
                break;
            case 'SubProduct':
                OnSubProductChange(updated_item, index, true, true);
                // updateSIPerPerson(updated_item, index);
                break;
            case 'paymentMode':
                setSubPaymentModes(updated_item)
                break;
            case 'paymentFrequency':
                setInstallment(updated_item, index)
                break;
            case 'paymentSource':
                setInstallment(updated_item, index);
                break;
            case 'PolicyStartDate':
                OnPolicyTermChange(updated_item, index);
                break;
            case 'SIPerPerson':
                SetSIByNoOfLives(updated_item, SIPerPerson, index);
                break;
            case 'NoOfEmployees':
                SetSIByNoOfLives(updated_item, SIPerPerson, index);
                break;
            case 'Occupation':
                onOccupationChange(updated_item, index, true);
                break;
            case 'Portability':
                updated_item[e_name] = !updated_item[e_name];
                break;
            case 'CoverType':
            case 'BuildingSI':
            case 'ContentSI':
                OnCoverTypeChange(updated_item, index);
                break;
            case 'PrevPolicyNo':
                OnPrevPolicyNo(updated_item, index);
                break;
            case 'PreviousBookingNo':
                OnPreviousBookingNo(updated_item, index);
                break;
            case 'HandleAddNewSalesPartner':
                HandleAddNewSalesPartner(updated_item, e.target.index);
                break;
            case 'SalesPartners':
                OnSalesPartnerChanged(e_name, val, e.target.index, updated_item, e.target.index);
                break;
            case 'OnSalesPartnerSharePercentageChanged':
                OnSalesPartnerSharePercentageChanged('SalesPartners', val, e.target.index, updated_item, e.target.index);
                break;
            case 'HandleSalesPartnerDelete':
                HandleSalesPartnerDelete(e.target.index, updated_item, index);
                break;
            case 'TransitType':
                // Reset ShipmentType when TransitType changes to avoid type mismatch
                if (productId === 131 && updated_item.SubProduct?.ID === 13) {
                    updated_item.ShipmentType = val === "Annual Open" ? [] : "";
                }
                break;
            case 'ShipmentType':
                // Helper function to check shipment type selection
                const isShipmentSelectedForBooking = (shipmentType, type) => {
                    if (!shipmentType) return false;
                    if (Array.isArray(shipmentType)) {
                        return shipmentType.some(st => st.Id === type);
                    }
                    if (typeof shipmentType === 'string') {
                        return shipmentType === type;
                    }
                    if (shipmentType.Id) {
                        return shipmentType.Id === type;
                    }
                    return false;
                };

                // Clear InlandSI if Inland is not selected
                if (!isShipmentSelectedForBooking(updated_item.ShipmentType, 'Inland')) {
                    updated_item.InlandSI = '';
                }
                // Clear ImportSI if Import is not selected
                if (!isShipmentSelectedForBooking(updated_item.ShipmentType, 'Import')) {
                    updated_item.ImportSI = '';
                }
                // Clear ExportSI if Export is not selected
                if (!isShipmentSelectedForBooking(updated_item.ShipmentType, 'Export')) {
                    updated_item.ExportSI = '';
                }
                // Clear MerchantTradingSI if Merchant Trading is not selected
                if (!isShipmentSelectedForBooking(updated_item.ShipmentType, 'Merchant Trading')) {
                    updated_item.MerchantTradingSI = '';
                }
            break;
            case 'HandleAddNewSalesSpecialist':
                HandleAddNewSalesSpecialist(updated_item, e.target.index);
                break;
            case 'SalesSpecialists':
                OnSalesSpecialistChanged(e_name, val, e.target.index, updated_item);
                break;
            case 'HandleSalesSpecialistDelete':
                HandleSalesSpecialistDelete(e.target.index, updated_item, index);
                break;
            case 'HandleAddNewGrade':
                HandleAddNewGrade(updated_item, e.target.index);
                break;
            case 'HandleGradeDelete':
                HandleGradeDelete(e.target.index, updated_item, index);
                break;
            case 'GradeSumInsured':
                GradeSumInsured('Grades', val, e.target.index, updated_item, e.target.index);
                break;
            case 'Brokerage':
                BrokerageChanged(val, updated_item, index);
                break;
            case 'PolicyTerm':
                OnPolicyTermChange(updated_item, index, true);
                break;
            case 'IsTP':
                OnIsTPChange(val, updated_item, e.target.index);
                break;
            case 'emi':
                OnEMIChanged(val, updated_item, e.target.index);
                break;
            case 'HandleAddNewInstallment':
                HandleAddNewInstallment(updated_item, e.target.index);
                break;
            case 'HandleInstallmentDelete':
                HandleInstallmentDelete(e.target.index, updated_item, index);
                break;
            case 'InstallmentDate':
                AddInstallmentData(val, e.target.index, updated_item, e.target.index, true);
                break;
            case 'InstallmentAmount':
                AddInstallmentData(val, e.target.index, updated_item, e.target.index, false);
                break;
            case 'AddFollowerSupplier':
                AddFollowerSupplier(updated_item, e.target.index);
                break;
            case 'HandleFollowerSupplierDelete':
                HandleFollowerSupplierDelete(e.target.index, updated_item, e.target.index);
                break;
            case 'FollowerPercentage':
                OnFollowerPercentageChanged('FollowerSuppliers', val, e.target.index, updated_item, e.target.index);
                break;
            case 'FollowerSuppliers':
                OnFollowerSupplierChanged(e_name, val, e.target.index, updated_item, e.target.index);
                break;
            case 'LeadersPercentage':
                OnLeadersPercentageChanged(e_name, val, updated_item, e.target.index);
                break;
            case 'OccupationType':
                updated_item[e_name] = val;
                updated_item['ManufacturerTraderName'] = '';
                updated_item['ManufacturerTraderContactNo'] = '';
                break;
            case 'HandleAddRiskLocation':
                HandleAddRiskLocation(updated_item, e.target.index);
                break;
            case 'HandleRiskLocationDelete':
                HandleRiskLocationDelete(e.target.index, updated_item, index);
                break;
            case 'RiskAddress':
            case 'RiskLocationCity':
            case 'RiskCityPincode':
                AddRiskLocation(val, e.target.index, updated_item, e.target.index, e_name);
                break;
            case 'ChildOccupancies':
                HandleChildOccupancies(val, e.target.index, updated_item, e.target.index, e_name);
                break;
            case 'BurglaryPremium':
                HandleFirePremium(val, e.target.index, e_name, updated_item);
                break;
            case 'TerrorismPremium':
                HandleFirePremium(val, e.target.index, e_name, updated_item);
                break;
            case 'TotalPremium':
                HandleTotalPremium(val, e.target.index, e_name, updated_item);
                break;
            case 'BurglaryPremiumVal':
                HandlePremiumYesNo(val, e.target.index, e_name, updated_item);
                break;
            case 'TerrorismPremiumVal':
                HandlePremiumYesNo(val, e.target.index, e_name, updated_item);
                break;
            default:
                break;
        }
        setOrderList(prevState => prevState.map((order, idx) => {
            if (idx === index) {
                return updated_item;
            }
            return order;
        }))
    };

    // Function to load terrorism rates from GetMappingValues API
    const LoadTerrorismRates = () => {
        try {
            if (productId === 131) {
                masterService.GetMappingValues()
                    .then((mappingValues) => {
                        if (mappingValues && Array.isArray(mappingValues)) {
                            // Keep as array format
                            setTerrorismRatesMaster(mappingValues);
                        } else {
                            setTerrorismRatesMaster([]);
                        }
                    })
                    .catch((error) => {
                        console.error("Error loading terrorism rates:", error);
                        // Set empty array on error to fall back to manual input
                        setTerrorismRatesMaster([]);
                    });
            }
        } catch (error) {
            console.error("Error in LoadTerrorismRates:", error);
            setTerrorismRatesMaster([]);
        }
    };

    // Function to check if terrorism premium field should be disabled
    const IsTerrorismPremiumDisabled = (item) => {
        try {
            // Check if it's sub-product 5, 7, or 8
            if (!item.SubProduct || [5, 7, 8].indexOf(item.SubProduct.ID) === -1) {
                return false; // Not applicable sub-products, keep enabled
            }

            // Check if occupancy is selected
            if (!item.Occupation || !item.Occupation.ID) {
                return false; // No occupancy selected, keep enabled for manual input
            }

            // Find terrorism rate for this occupancy
            const rateItem = TerrorismRatesMaster.find(rateItem => rateItem.Id == parseInt(item.Occupation.ID));

            // If rate exists and is not null/undefined, disable the field
            if (rateItem && rateItem.Value !== null && rateItem.Value !== undefined) {
                return true; // Rate found, disable field for auto-calculation
            }

            return false; // No rate found, keep enabled for manual input
        } catch (error) {
            console.error("Error checking terrorism premium disabled state:", error);
            return false; // On error, keep enabled
        }
    };

    // Function to calculate terrorism premium automatically
    const CalculateTerrorismPremium = (sumInsured, occupancyId) => {
        try {
            // Find terrorism rate from array by occupancy ID
            const rateItem = TerrorismRatesMaster.find(item => item.Id == parseInt(occupancyId));
            if (!rateItem || rateItem.Value === undefined || rateItem.Value === null) {
                return null; // No rate found, return null to indicate manual input should be used
            }
            const terrorismRate = parseFloat(rateItem.Value);
            const calculatedPremium = (parseFloat(sumInsured || 0) * terrorismRate) / 1000;
            return Math.round(calculatedPremium * 100) / 100; // Round to 2 decimal places
        } catch (error) {
            console.error("Error calculating terrorism premium:", error);
            return null; // Return null on error to fall back to manual input
        }
    };

    const HandleFirePremium = (val, indx, e_name, item) => {
        try {
            const totalPremium = parseFloat(item.TotalPremium || 0);
            const terrorismPremium = parseFloat(item.TerrorismPremium || 0);
            const burglaryPremium = parseFloat(item.BurglaryPremium || 0);
            const inputPremium = parseFloat(val) || 0;
            var Premium = 0;
            var sumOfAllThreePrem = 0;
            if (indx === -1) {
                if (e_name == "TerrorismPremium") {
                    Premium = totalPremium - (inputPremium + burglaryPremium)
                    sumOfAllThreePrem = (Premium + inputPremium + burglaryPremium);
                    if ((totalPremium !== sumOfAllThreePrem) || Premium <= 0) {
                        throw new Error("Error");
                    }
                    setNewOrder(prevState => ({ ...prevState, "TerrorismPremium": val, "FirePremium": Premium }));
                }
                else if (e_name == "BurglaryPremium") {
                    Premium = totalPremium - (inputPremium + terrorismPremium)
                    sumOfAllThreePrem = (Premium + inputPremium + terrorismPremium);
                    if ((totalPremium !== sumOfAllThreePrem) || Premium <= 0) {
                        throw new Error("Error");
                    }
                    setNewOrder(prevState => ({ ...prevState, "BurglaryPremium": val, "FirePremium": Premium }));
                }
            }
            else {
                if (e_name == "TerrorismPremium") {
                    Premium = totalPremium - (inputPremium + burglaryPremium)
                    sumOfAllThreePrem = (Premium + inputPremium + burglaryPremium);
                    if ((totalPremium !== sumOfAllThreePrem) || Premium <= 0) {
                        throw new Error("Error");
                    }
                    item.TerrorismPremium = val;
                    item.FirePremium = Premium;
                }
                else if (e_name == "BurglaryPremium") {
                    Premium = totalPremium - (inputPremium + terrorismPremium)
                    sumOfAllThreePrem = (Premium + inputPremium + terrorismPremium);
                    if ((totalPremium !== sumOfAllThreePrem) || Premium <= 0) {
                        throw new Error("Error");
                    }
                    item.BurglaryPremium = val;
                    item.FirePremium = Premium;
                }
            }
        }
        catch (err) {
            enqueueSnackbar("Premium should be equal to the sum of other valid numeric premium's", { variant: 'error', autoHideDuration: 5000, });
        }
    }

    const HandleTotalPremium = (val, indx, e_name, item) => {
        try {
            if (indx === -1) {
                setNewOrder(prevState => ({
                    ...prevState, "FirePremium": 0, "TotalPremium": val,
                    "TerrorismPremiumVal": 0, "BurglaryPremiumVal": 0, "TerrorismPremium": 0, "BurglaryPremium": 0
                }));
            } else {
                item.TotalPremium = val;
                item.FirePremium = 0;
                item.TerrorismPremiumVal = 0;
                item.BurglaryPremiumVal = 0;
                item.TerrorismPremium = 0;
                item.BurglaryPremiumVal = 0;
            }
        } catch (error) {
            console.error("Error in HandleTotalPremium:", error);
        }
    };
    const HandleTerrorismVal = (val, indx, e_name, item) => {
        try {
            if (item.SumInsured && item.Occupation && item.Occupation.ID) {
                const calculatedTerrorismPremium = CalculateTerrorismPremium(item.SumInsured, item.Occupation.ID);
                AutoCalculateTerrorismPremium(calculatedTerrorismPremium, indx, e_name, item, val)
            }
        }
        catch {

        }
    }

    const HandleOccTerrorism = (val, indx, e_name, item) => {
        try {
            if (val && val.ID && item.SumInsured) {
                const calculatedTerrorismPremium = CalculateTerrorismPremium(item.SumInsured, val.ID);
                AutoCalculateTerrorismPremium(calculatedTerrorismPremium, indx, e_name, item, val);
            }
        }
        catch {

        }
    }

    const HandleSITerrorism = (val, indx, e_name, item) => {
        try {
            if (val && item.Occupation && item.Occupation.ID) {
                const calculatedTerrorismPremium = CalculateTerrorismPremium(val, item.Occupation.ID);
                AutoCalculateTerrorismPremium(calculatedTerrorismPremium, indx, e_name, item, val);

            }
        }
        catch {

        }
    }
    const AutoCalculateTerrorismPremium = (calculatedTerrorismPremium, indx, e_name, item, val) => {
        try {
            // Only auto-calculate if a valid rate was found
            if (calculatedTerrorismPremium !== null) {
                // Calculate fire premium: Total Premium - Terrorism Premium - Burglary Premium
                const totalPremium = parseFloat(item.TotalPremium || 0);
                const burglaryPremium = parseFloat(item.BurglaryPremium || 0);
                const calculatedFirePremium = totalPremium - calculatedTerrorismPremium - burglaryPremium;

                if (indx == -1) {
                    setNewOrder(prevState => ({
                        ...prevState,
                        [e_name]: val,
                        "TerrorismPremium": calculatedTerrorismPremium,
                        "FirePremium": calculatedFirePremium > 0 ? calculatedFirePremium : prevState.FirePremium
                    }));
                    return;
                }
                else {
                    item.TerrorismPremium = calculatedTerrorismPremium;
                    item.FirePremium = calculatedFirePremium > 0 ? calculatedFirePremium : item.FirePremium;
                }
            }
        }
        catch {

        }
    }
    const HandlePremiumYesNo = (val, indx, e_name, item) => {
        try {
            if (val && val.Id && val.Id === 2) {
                if (indx === -1) {
                    if (e_name == "TerrorismPremiumVal") {
                        setNewOrder(prevState => ({ ...prevState, "TerrorismPremium": 0, "FirePremium": (parseFloat(item.FirePremium) + parseFloat(item.TerrorismPremium)) || 0 }));
                    }
                    else if (e_name == "BurglaryPremiumVal") {
                        setNewOrder(prevState => ({ ...prevState, "BurglaryPremium": 0, "FirePremium": (parseFloat(item.FirePremium) + parseFloat(item.BurglaryPremium)) || 0 }));
                    }
                } else {
                    if (e_name == "TerrorismPremiumVal") {
                        item.FirePremium = (parseFloat(item.FirePremium) + parseFloat(item.TerrorismPremium)) || 0;
                        item.TerrorismPremium = 0;
                    }
                    else if (e_name == "BurglaryPremiumVal") {
                        item.FirePremium = (parseFloat(item.FirePremium) + parseFloat(item.BurglaryPremium)) || 0;
                        item.BurglaryPremium = 0;
                    }
                }
            }
        } catch (error) {
            console.error("Error in HandleTotalPremium:", error);
        }
    };

    const GetLeadAssignedAgentId = () => {
        const input = {
            url: "api/SalesView/GetLeadAssignedAgent?LeadId=" + leadId,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        return CALL_API(input);
    };

    const GetLeadAssignedAgentDetails = () => {
        const input = {
            url: "api/SalesView/GetLeadAssignedAgentDetails?LeadId=" + leadId,
            method: 'GET',
            service: 'MatrixCoreAPI'
        }
        return CALL_API(input);
    };

    const HandleFileUpload = async (item) => {
        if (productId == 131 && File) {
            const data = {
                UploadedFile: File,
                DocTypeId: docTypeId,
                LeadId: item.leadId,
                CustomerId: customerId,
                ProductId: item.ProductId
            };

            const input = {
                url: "api/UploadFile/UploadPolicy",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data
            };

            return CALL_API(input);
        }
    };

    const HandleMiscFileUpload = async () => {
        if (productId == 131) {
            const data = {
                UploadedFile: InvoiceFile,
                LeadId: leadId,
                CustomerId: customerId,
                ProductId: productId,
                EnquiryId: 0,
                Type: "Invoice"
            }
            const input = {
                url: "api/UploadFile/UploadMiscDocument",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data,
                timeout: 12000
            };

            return CALL_API(input);
        }
    };

    const DownloadDocument = (documentId, documentTypeId = 56) => {
        var request = {
            custId: customerId.toString(),
            docId: documentId,
            docTypeId: documentTypeId
        }
        GetDocumentPath(request).then
            ((response) => {
                if (response && response.ttlDocUrl) {
                    window.open(response.ttlDocUrl);
                }
                else {
                    window.alert("Error: " + response.statusMsg);
                }
            });
    };

    const GetDocumentPath = (request) => {
        const _input = {
            url: `api/SalesView/GetDocumentUrl`,
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: request
        }

        return CALL_API(_input);
    };

    const UpdateBasicPolicyDetails = (leadId, docId) => {
        if (productId == 131 && docId) {
            const data = {
                LeadId: leadId,
                PolicyDocId: docId
            };

            const input = {
                url: "api/UploadFile/UpdateBasicPolicyDetails",
                method: 'POST',
                service: 'commonUploadFileToUrl',
                requestData: data
            };

            return CALL_API(input);
        }
    };

    const ActiveLeadChanged = function (item, index) {
        setFile(null);
        setInvoiceFile(null);
        setNewOrder(prevState => ({ ...prevState, PolicyType: options }));
        setNewOrder(prevState => ({ ...prevState, PrevPolicyNo: "" }));
        setNewOrder(prevState => ({ ...prevState, PreviousBookingNo: "" }));
        setNewOrder(prevState => ({ ...prevState, LeadSource: "" }));
        setIsDisabledForSmeElements(false);
        if (productId != 131 && productId != 101) {
            setIsdisabledPED(false);
        }
        for (let i = 0; i < allLeads.length; i++) {
            if (allLeads[i].LeadID == item.ActiveLead.leadId) {
                let isNotHealthRenewal = productId == 2 && allLeads[i].LeadSource !== "Renewal"
                setIsNotHealthRenewal(isNotHealthRenewal);
            }

            if (productId == 131
                && allLeads[i].LeadID == item.ActiveLead.leadId
                && allLeads[i].LeadSource == "Renewal"
                && allLeads[i].ReferralID > 0
                // && allLeads[i].PrevPolicyNo !== ""
                // && allLeads[i].PreviousBookingNo !== ""
            ) {
                setNewOrder(prevState => ({ ...prevState, PolicyType: options.policyType[1] }));
                setNewOrder(prevState => ({ ...prevState, PreviousBookingNo: allLeads[i].PreviousBookingNo }));
                setNewOrder(prevState => ({ ...prevState, PrevPolicyNo: allLeads[i].PrevPolicyNo }));
                setIsDisabledForSmeElements(true);
                break;
            }
            if (productId == 117 && allLeads[i].LeadID == item.ActiveLead.leadId) {
                setNewOrder(prevState => ({ ...prevState, LeadSource: allLeads[i].LeadSource }));
                if (allLeads[i].LeadSource.toLowerCase() === "renewal") {
                    let RenewalPolicyType = [{ "PolicyTypeId": 1, "PolicyTypeName": "Renewal" }];
                    //options.PolicyType = RenewalPolicyType;
                    setOptions(prevState => ({ ...prevState, policyType: RenewalPolicyType }));

                    let DirectPayment = [{
                        "ParentModeValue": 0, "PaymentModeName": "Direct Payment", "PaymentModeValue": 4001
                    }];
                    //options.paymentMode = DirectPayment;
                    setOptions(prevState => ({ ...prevState, paymentMode: DirectPayment }));
                    //setAllPaymentModes(DirectPayment);
                    setIsdisabledPED(true);

                    let OfflineBookingType = [{
                        "label": "Offline",
                        "value": 2
                    }];
                    setNewOrder(prevState => ({ ...prevState, BookingType: BookingTypes['Offline'] }));
                    setOptions(prevState => ({ ...prevState, BookingTypes: OfflineBookingType }));

                    setNewOrder(prevState => ({ ...prevState, PrevPolicyNo: allLeads[i].PrevPolicyNo }));
                    setNewOrder(prevState => ({ ...prevState, RegistrationNo: allLeads[i].RegistrationNumber }));
                    setNewOrder(prevState => ({ ...prevState, InsuredName: allLeads[i].InsuredName }));

                }
                else {
                    setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Motor }))
                }
            }
        }
    }

    const PolicyTypeChangedForCreate = function (item, index) {
        if (productId != 131 && productId != 101) {
            setIsdisabledPED(false);
        }
        let _allPaymentModes = AllPaymentModes;
        let _PaymentModes = _allPaymentModes.filter((payMode) => (payMode.ParentModeValue == 0));
        setOptions(prevState => ({ ...prevState, paymentMode: _PaymentModes }));
        setAllPaymentModes(_allPaymentModes);

        if (productId == 131) {
            setIsDisabledForSmeElements(false);
            setIsDisabledExpiringPolicyNo(false);
            setIsDisabledExpiringBookingId(false);
            setNewOrder(prevState => ({ ...prevState, PrevPolicyNo: "" }));
            setNewOrder(prevState => ({ ...prevState, PreviousBookingNo: "" }));
        }
        if (productId == 117 && item.PolicyType.PolicyTypeId == 1) {
            let OfflineBookingType = [{
                "label": "Offline",
                "value": 2
            }];
            setNewOrder(prevState => ({ ...prevState, BookingType: BookingTypes['Offline'] }));
            setOptions(prevState => ({ ...prevState, BookingTypes: OfflineBookingType }));

            let DirectPayment = [{
                "ParentModeValue": 0, "PaymentModeName": "Direct Payment", "PaymentModeValue": 4001
            }];
            setOptions(prevState => ({ ...prevState, paymentMode: DirectPayment }));
            //setAllPaymentModes(DirectPayment);

            setIsdisabledPED(true);
        }
        if (item.ProductId == 2 && item.PolicyType.PolicyTypeId == 1) {
            setNewOrder(prevState => ({ ...prevState, InsuredName: "" }))
        }
    }

    const PolicyTypeChangedForEdit = function (item, index) {
        if (productId != 131 && productId != 101) {
            setIsdisabledPED(false);
        }
        let _allPaymentModes = AllPaymentModes;
        let _PaymentModes = _allPaymentModes.filter((payMode) => (payMode.ParentModeValue == 0));
        setOptions(prevState => ({ ...prevState, paymentMode: _PaymentModes }));
        setAllPaymentModes(_allPaymentModes);

        if (productId == 131) {
            setIsDisabledForSmeElements(false);
            setIsDisabledExpiringPolicyNo(false);
            setIsDisabledExpiringBookingId(false);
            item.PrevPolicyNo = "";
            item.PreviousBookingNo = "";
        }
        if (productId == 117 && item.PolicyType.PolicyTypeId == 1) {
            let OfflineBookingType = [{
                "label": "Offline",
                "value": 2
            }];
            item.BookingType = BookingTypes['Offline'];
            setOptions(prevState => ({ ...prevState, BookingTypes: OfflineBookingType }));

            let DirectPayment = [{
                "ParentModeValue": 0, "PaymentModeName": "Direct Payment", "PaymentModeValue": 4001
            }];
            item.payment.PaymentStatus = 4001;
            setOptions(prevState => ({ ...prevState, paymentMode: DirectPayment }));
            //setAllPaymentModes(DirectPayment);



            setIsdisabledPED(true);
        }
        if (item.ProductId == 2 && item.PolicyType.PolicyTypeId == 1) {
            item.InsuredName = ""
        }
    }
    const OnPrevPolicyNo = function (item, index) {
        if (productId == 131 && item.PrevPolicyNo != undefined && item.PrevPolicyNo.length > 0 && item.PolicyType.PolicyTypeId == 1)
            setIsDisabledExpiringBookingId(true);
        else
            setIsDisabledExpiringBookingId(false);
    }
    const OnPreviousBookingNo = function (item, index) {
        if (productId == 131 && item.PreviousBookingNo != undefined && item.PreviousBookingNo.length > 0 && item.PolicyType.PolicyTypeId == 1)
            setIsDisabledExpiringPolicyNo(true);
        else
            setIsDisabledExpiringPolicyNo(false);
    }
    const OnCoverTypeChange = function (item, index) {

        let _IsdisabledBuildingSI = true;
        let _IsdisabledContentSI = true;
        if (item.CoverType.ID === 1) {
            _IsdisabledBuildingSI = false;

            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, ContentSI: 0 }))
                setNewOrder(prevState => ({ ...prevState, SumInsured: parseFloat(item.BuildingSI || 0) }))
            }
            else {
                item.ContentSI = 0;
                item.SumInsured = parseFloat(item.BuildingSI || 0);
            }
        }
        else if (item.CoverType.ID === 2) {
            _IsdisabledContentSI = false;
            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, BuildingSI: 0 }))
                setNewOrder(prevState => ({ ...prevState, SumInsured: parseFloat(item.ContentSI || 0) }))
            }
            else {
                item.BuildingSI = 0;
                item.SumInsured = parseFloat(item.ContentSI || 0);
            }
        }
        else if (item.CoverType.ID === 3) {
            _IsdisabledBuildingSI = false;
            _IsdisabledContentSI = false;

            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, SumInsured: parseFloat(item.ContentSI || 0) + parseFloat(item.BuildingSI || 0) }))
            }
            else {
                item.SumInsured = parseFloat(item.ContentSI || 0) + parseFloat(item.BuildingSI || 0);
            }
        }
        setIsdisabledBuildingSI(_IsdisabledBuildingSI);
        setIsdisabledContentSI(_IsdisabledContentSI);
    };

    const OnPolicyTermChange = (item, index, isPolicyTermChanged = false) => {
        try {
            if ([131, 101].indexOf(productId) > -1 || (productId === 117 && (item.PolicyType && item.PolicyType.PolicyTypeId === 1))) {
                if (index === -1 && isPolicyTermChanged && [131, 101].indexOf(productId) > -1 && !item.PolicyEndDate && !item.PolicyStartDate) {
                    item.PolicyEndDate = new Date();
                    item.PolicyStartDate = new Date();
                }
                let PolicyEndDate = '';
                let PolicyStartDate = item.PolicyStartDate;
                let date = item.PolicyStartDate;
                if (index !== -1 && isPolicyTermChanged && [131, 101].indexOf(productId) > -1) {
                    date = new Date(date);
                }
                date = new Date(date.getTime());
                date = new Date(date.toDateString());
                PolicyStartDate = date;
                date = new Date(date.getTime());

                if (invalids.indexOf(item.PolicyStartDate) === -1 && item.ProductId === 117 && (item.PolicyType && item.PolicyType.PolicyTypeId == 1)) {
                    PolicyEndDate = new Date(date.setMonth(date.getMonth() + 12));
                    PolicyEndDate = PolicyEndDate.getTime() - 1;
                    setIsdisabledPED(true);
                }
                else {
                    if (!item.PolicyTerm) {
                        PolicyEndDate = "";
                        if (!!Visible.PolicyTerm || !!Visible.Tenure) {
                            if (index === -1) {
                                setNewOrder(prevState => ({ ...prevState, PolicyStartDate }))
                            }
                            else {
                                if (PolicyStartDate)
                                    item.PolicyStartDate = PolicyStartDate;
                                if (PolicyEndDate)
                                    item.PolicyEndDate = PolicyEndDate;

                                if (!PolicyStartDate) {
                                    item.PolicyEndDate = new Date();
                                    item.PolicyStartDate = new Date();
                                }
                            }
                            if (!isPolicyTermChanged) {
                                alert('Enter Policy Term/Tenure, then select Policy StartDate Again');
                            }
                            return;
                        }
                    }
                    else if (invalids.indexOf(item.PolicyStartDate) === -1 && item.ProductId === 131 && item.PolicyTerm) {
                        PolicyEndDate = new Date(date.setMonth(date.getMonth() + parseInt(item.PolicyTerm)));
                        PolicyEndDate = PolicyEndDate.getTime() - 1;
                    }
                    else if (invalids.indexOf(item.PolicyStartDate) === -1 && item.ProductId === 101 && item.PolicyTerm) {
                        PolicyEndDate = new Date(date.setFullYear(date.getFullYear() + parseInt(item.PolicyTerm)));
                        PolicyEndDate = PolicyEndDate.getTime() - 1;
                    }
                }

                if (index === -1) {
                    if (PolicyStartDate && PolicyEndDate) {
                        setNewOrder(prevState => ({ ...prevState, PolicyEndDate, PolicyStartDate }));
                    }
                }
                else {
                    if (PolicyEndDate)
                        item.PolicyEndDate = PolicyEndDate;
                    if (PolicyStartDate)
                        item.PolicyStartDate = PolicyStartDate;
                }
            }
        }
        catch (e) {
            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, PolicyEndDate: null, PolicyStartDate: null }));
            }
            else {
                item.PolicyEndDate = null;
                item.PolicyStartDate = null;
            }
            console.log(e.message);
        }
    }

    const bindChildOccupancy = (item, index, isOnChange) => {
        setChildOccupancyList(null);

        if (index === -1) {
            setNewOrder(prevState => ({ ...prevState, IsValidChildOccupancy: false }));
            if (isOnChange) {
                setNewOrder(prevState => ({ ...prevState, ChildOccupancies: null }));
                setNewOrder(prevState => ({ ...prevState, ChildOccupation: null }));
            }

        }
        else {
            item.IsValidChildOccupancy = false;
            if (isOnChange) {
                item.ChildOccupancies = null;
                item.ChildOccupation = null;
            }

        }

        if (item && item.Occupation) {
            try {
                let childOcc = options.Occupations.filter((o) => o.ParentCategoryId == item.Occupation.ID && o.SubProductTypeId == item.SubProduct.ID);
                if (childOcc && childOcc.length > 0) {
                    setChildOccupancyList(childOcc);
                    if (index === -1) {
                        setNewOrder(prevState => ({ ...prevState, IsValidChildOccupancy: true }));
                    }
                    else {
                        item.IsValidChildOccupancy = true;
                    }
                }
            }
            catch { return []; }
        }
    }

    const onOccupationChange = (item, index, isOnChange = false) => {
        let OtherOccupany = false;
        if (item && item.Occupation && item.Occupation.Name == "Others") {
            OtherOccupany = true;
        }
        setVisible(prevState => ({ ...prevState, OtherOccupany }));
        // setVisible({ ...Visible, OtherOccupany });

        bindChildOccupancy(item, index, isOnChange);
    }

    const OnSubProductChange = (item, index, isBlankSupPlan = true, resetOccupation = false) => {
        let OtherOccupanyMandatory = false;
        let _visible = {};
        if (productId === 131) {
            OtherOccupanyMandatory = true;
            let filteredArray = options.Occupations.filter((o) => o.SubProductTypeId == item.SubProduct.ID && o.ParentCategoryId == 0);
            if (filteredArray.length === 0) {
                OtherOccupanyMandatory = false;
            }
            if ([1, 2, 3, 4, 11, 19, 103, 104].indexOf(item.SubProduct.ID) !== -1) {
                _visible.NoOfLives = true;
                _visible.NoOfEmployee = true;
            }
            else {
                _visible.NoOfLives = false;
                _visible.NoOfEmployee = false;
            }
            SetSupplierBySubProductId(item, index, isBlankSupPlan);

            if ([8].indexOf(item.SubProduct.ID) !== -1) {
                GetShopTypes();
            }

            // Reset terrorism premium fields when sub-product changes to 5, 7, 8
            if ([5, 7, 8].indexOf(item.SubProduct.ID) !== -1) {
                if (index === -1) {
                    setNewOrder(prevState => ({
                        ...prevState,
                        "TerrorismPremium": null,
                        "TerrorismPremiumVal": null // Reset to blank, user needs to select
                    }));
                } else {
                    item.TerrorismPremium = null;
                    item.TerrorismPremiumVal = null;
                }
            }
        }

        if (index === -1) {
            setNewOrder(prevState => ({ ...prevState, OtherOccupanyMandatory }));
            if (resetOccupation) {
                setNewOrder(prevState => ({ ...prevState, Occupation: null }));
            }
        }
        else {
            item.OtherOccupanyMandatory = OtherOccupanyMandatory;
            if (resetOccupation) {
                item.Occupation = null;
            }
        }
        setVisible({ ...Visible, ..._visible });

        if (productId === 131 && [1, 2, 3, 103, 104].indexOf(item.SubProduct.ID) !== -1) {
            setSIPerPerson(true);
            SetSIByNoOfLives(item, true, index);
            updateSIPerPerson(item, true, index);
        }
        else {
            setSIPerPerson(false);
        }
    };

    const SetSupplierBySubProductId = (item, index, isBlankSupPlan) => {
        try {
            setIsShowSupplierBySubProduct(true);

            if (options.supplier) {
                let supplierBySubProduct = options.supplier.filter((sup) => sup.SubProductId == item.SubProduct.ID);
                setSupplierBySubProduct(supplierBySubProduct);
            }
            else {
                masterService.getSupplierByProduct(item.ProductId).then(suppliers => {
                    if (Array.isArray(suppliers)) {
                        // let result = [];
                        let supplierBySubProduct = suppliers.filter((sup) => sup.SubProductId == item.SubProduct.ID);
                        setSupplierBySubProduct(supplierBySubProduct);

                        // options.supplier.forEach(supplier => {
                        //     supplierBySubProduct.forEach(sup => {
                        //         if(supplier.ProductId == item.ProductId && supplier.OldSupplierId == sup.OldSupplierId)
                        //         {
                        //             result.push(supplier);
                        //         }
                        //     })
                        // });

                        // setSupplierBySubProduct(result);


                    }
                    else {
                        setSupplierBySubProduct([]);
                    }
                })
            }

            if (isBlankSupPlan) {
                if (index === -1) {
                    setNewOrder(prevState => ({ ...prevState, Supplier: [], Plan: [] }));
                }
                else {
                    item.Supplier = [];
                    item.Plan = [];
                }

                setOptimzedPlansList([]);
                setOptimzedPlansListforBookedLeads([]);
            }
        }
        catch (e) {
            setSupplierBySubProduct([]);
            console.log(e.message);
        }
    };

    const OnSupplierChange = (item, index, Source, blankData = true) => {

        let SupplierId = item.Supplier.OldSupplierId;
        //SupplierId = ([131, 3, 154].indexOf(productId) === -1) ? item.Supplier.OldSupplierId : item.Supplier.SupplierId;
        if (SupplierId) {
            if (blankData) {
                item.Plan = [];
                setNewOrder(prevState => ({ ...prevState, Plan: [] }));
            }
            masterService.GetProductPlansFromCore(rootScopeService.getProductId(), SupplierId, "Booking").then(res => {
                //setOptions(prevState => ({ ...prevState, plan: res }));
                if (Array.isArray(res)) {
                    if (productId == 131) {
                        let result = [];
                        let subProductId = item.SubProduct.ID;
                        res.forEach((plan) => {
                            if (plan.SubProductId == subProductId) {
                                result.push(plan);
                            }
                        });
                        res = result;
                    }

                    Source == "CreateLead" ? setOptimzedPlansList(res) : setOptimzedPlansListforBookedLeads(res);

                    if (res && res.length == 1 && (Source == 'CreateLead' || Source == 'BookedLead')) {
                        BindIfSinglePlan(res[0], item, index);
                    }
                }
            })
        }
    };

    const OnPlanChange = (item, index) => {
        if (productId === 131) {
            let oldPlanId = item.Plan.OldPlanId;
            let Brokerage = 0;
            for (var i = 0, len = options.SMEPlanDeal.length; i < len; i++) {
                if (oldPlanId == options.SMEPlanDeal[i].OldPlanId) {
                    Brokerage = options.SMEPlanDeal[i].Brokerage;
                    break;
                }
            }
            if (index === -1) {
                setNewOrder(prevState => ({ ...prevState, Brokerage }))
            }
            else {
                item.Brokerage = Brokerage;
            }
        }
    }

    const SetSIByNoOfLives = function (item, isSIPerPerson, index) {
        if (isSIPerPerson) {
            if (!isNaN(Number(item.NoOfEmployees)) && Number(item.NoOfEmployees) > 0) {
                if (!isNaN(Number(item.SIPerPerson)) && Number(item.SIPerPerson) > 0) {
                    if (index === -1) {
                        setNewOrder(prevState => ({
                            ...prevState,
                            SumInsured: (prevState.SIPerPerson * prevState.NoOfEmployees)
                        }))
                    }
                    else item.SumInsured = (item.SIPerPerson * item.NoOfEmployees);
                }
            }
        }
    }
    const setInstallment = (item, index) => {
        let Installments;
        try {
            let PayPeriodicity = "";
            switch (item.payment.PaymentPeriodicity) {
                case 1:
                    PayPeriodicity = "Monthly";
                    break;
                case 2:
                    PayPeriodicity = "Quarterly";
                    break;
                case 3:
                    PayPeriodicity = "Half Yearly";
                    break;
                case 4:
                    PayPeriodicity = "Yearly";
                    break;
                case 5:
                    PayPeriodicity = "Single Premium";
                    break;
                default:
                    break
            }

            if (productId === 115 && [6, 3, 7].indexOf(item.Supplier.OldSupplierId) !== -1 && ["Credit Card", "Debit Card", "Net-Banking"].indexOf(item.PaymentSource) !== -1 && PayPeriodicity === "Monthly") {
                if (item.Supplier.OldSupplierId == "6" && [203, 227, 238].indexOf(item.Plan.OldPlanId) === -1) {
                    Installments = 3; //HDFC
                }
                else if (item.Supplier.OldSupplierId == "3") {
                    if ([206, 239, 241, 255].indexOf(item.Plan.OldPlanId) != -1) {
                        Installments = 1;  //Bajaj
                    }
                    else {
                        Installments = 2;  //Bajaj
                    }
                }
                else if (item.Supplier.OldSupplierId == "7") {  //ICICI
                    if (item.PaymentSource == "Credit Card") {
                        Installments = 1;
                    }
                    else {
                        Installments = 2;
                    }
                }
            }
            else if (rootScopeService.getProductId() === 7 && [5, 16].indexOf(item.Supplier.OldSupplierId) !== -1 && ["Credit Card", "Debit Card", "Net-Banking"].indexOf(item.PaymentSource) !== -1 && PayPeriodicity === "Monthly") {
                if (item.Supplier.OldSupplierId == "5") {
                    Installments = 2; //ICICI and Max
                }
                else if (item.Supplier.OldSupplierId == "16") {
                    Installments = 3;  //HDFC
                }
            }
            else if (rootScopeService.getProductId() === 7 && [10, 20].indexOf(item.Supplier.OldSupplierId) !== -1 && PayPeriodicity === "Monthly") {
                Installments = 2; // Max, Baxa
            }
            else {
                Installments = InstallmentsData.Product[rootScopeService.getProductId()].Plans[item.Plan.OldPlanId].PaymentMode[PayPeriodicity].Installments;
            }
        } catch (e) {
            Installments = 1;
        }
        if (index === -1) {
            setNewOrder(prevState => ({ ...prevState, InstallmentPaid: Installments }))
        }
        else {
            item.InstallmentPaid = Installments;
        }
        //console.log('InstallmentPaid' + Installments);
    };
    const updateSIPerPerson = (item, isSIPerPerson, index) => {
        if (isSIPerPerson) {
            if (!isNaN(Number(item.NoOfEmployees)) && Number(item.NoOfEmployees) > 0) {
                if (!isNaN(Number(item.SumInsured)) && Number(item.SumInsured) > 0) {
                    item.SIPerPerson = item.SumInsured / item.NoOfEmployees;
                }
            }
            if (item.SMERenewal) {
                setNewOrder(prevState => ({ ...prevState, "SIPerPerson": item.SIPerPerson }))
            }
        }
    }
    const setNewOrderInitially = () => {
        if (activeLeadsWithoutOrder.length > 0) {
            // let reqCity = null;
            // if (options.cities && Array.isArray(options.cities) && activeLeadsWithoutOrder[0].CityState) {
            //     reqCity = options.cities.find(c => c.CityID === activeLeadsWithoutOrder[0].CityState.CityID);
            // }
            let _newOrder = {
                leadId: activeLeadsWithoutOrder[0].leadId,
                ProductId: productId,
                SubProduct: {
                    ID: 0,
                },
                ActiveLead: {
                    leadId: activeLeadsWithoutOrder[0].leadId,
                },
                CityState: activeLeadsWithoutOrder[0].CityState,
                PolicyType: GetPolicyType(),
                BookingType: 1,
                Supplier: options.supplier.filter((supplier) => (supplier.ProductId == productId && supplier.OldSupplierId == 0 && supplier.SubCategoryId == activeLeadsWithoutOrder[0].ProductTypeId))[0],
                //Plan: options.plan.filter((plan) => (plan.ProductId == productId && plan.OldSupplierId == 0 && plan.OldPlanId == 0 && plan.SubProductTypeID == activeLeadsWithoutOrder[0].ProductTypeId))[0],
                Plan: [],
                ReferenceNo: '',
                DateOfInspection: '',
                Medical_or_InspectionRequired: 2,
                InspectionStatus: {
                    ID: 0,
                    Name: ''
                },
                ODPremium: '',
                PaidPremium: 0,
                CompanyName: '',
                LoadingAmount: '',
                NoOfLives: '',
                NoOfEmployees: '',
                DocumentsRequired: 0,
                IsDocReceived: false,
                payment: {
                    PaymentStatus: ''
                },
                Occupation:
                {
                    ID: 0,
                    Name: ""
                },

                TransitType: '',
                TermTenure: 0,
                TermSI: 0,
                SumInsured: '',
                TotalPremium: '',
                PrevPolicyNo: '',
                InsuredName: '',
                PreviousBookingNo: '',
                InstallmentPaid: '',
                ApplicationNo: '',
                PolicyNo: '',
                ShowPaymentDetails: '',
                ExpiringInsurer: 0,
                PolicyDocName: '',
                DocumentId: '',
                SalesPartners: [],
                SalesSpecialists: [],
                ProposalNo: '',
                ShipmentType: [],
                InlandSI: '',
                ImportSI: '',
                ExportSI: '',
                MerchantTradingSI: '',
                MarineCoverType: 0,
                CoInsurance: 0,
                LeadersPercentage: '',
                OccupationType: '',
                ManufacturerTraderName: '',
                ManufacturerTraderContactNo: '',
                ConstitutionOfBusiness: '',
                TerrorismPremium: 0,
                BurglaryPremium: 0,
                FirePremium: 0
            };

            if (productId === 131) {
                _newOrder.payment = {
                    PaymentPeriodicity: 4,
                },
                    _newOrder.LD = {
                        Id: 0
                    },
                    _newOrder.LDAmount = null;
                _newOrder.Association = {
                    Id: 0
                },
                    _newOrder.BookingFrom = {
                        Id: ''
                    }
                _newOrder.PolicyCategory = {
                    Id: ''
                }
                _newOrder.QuoteId = ''
                _newOrder.BookingCategory={
                    Id:''
                }
                _newOrder.ProjectDuration=null
                _newOrder.PlanType={
                    Id:''
                }

            }
            // if (rootScopeService.getProductName().indexOf("International") != -1) {
            //     //     $scope.newOrder.payment = {
            //     //         PaymentStatus: 4001,
            //     //         PaymentPeriodicity: 4
            //     //     }
            //     // }
            setNewOrder(_newOrder);
            OnSubProductChange(_newOrder, -1, true);
            onOccupationChange(_newOrder, -1);
        };
    };
    const GetPolicyType = function () {
        let PolicyTypeId = 0;
        if ([2, 106, 118, 130].indexOf(rootScopeService.getProductId()) != -1 && IsRenewal == 1) {
            PolicyTypeId = 1;
        }
        return PolicyTypeId;
    }
    const getPaymentModes = () => {
        if (intloanPrds.indexOf(productId) != -1) {
            if (AllPaymentModes.length > 0) return;

            let _allPaymentModes = [{
                "ParentModeValue": 0, "PaymentModeName": "Direct Payment", "PaymentModeValue": 4001
            }];
            setOptions(prevState => ({ ...prevState, paymentMode: _allPaymentModes }));
            setAllPaymentModes(_allPaymentModes);
        }
        else {
            masterService.getPaymentModes(productId).then(function (modes) {
                let _allPaymentModes = modes;
                let _PaymentModes = _allPaymentModes.filter((payMode) => (payMode.ParentModeValue == 0));
                setOptions(prevState => ({ ...prevState, paymentMode: _PaymentModes }));
                setAllPaymentModes(_allPaymentModes);
            })
        }
    };
    const getSubProductByProductID = () => {
        masterService.getSubProductByProductID(productId).then(res => {
            res = res || [];
            setOptions(prevState => ({ ...prevState, SubProduct: res }));
            //console.log('subProducts', res)
        });
    };
    const getPaymentPeriodicity = function () {
        let paymentFrequencyObj = {}, paymentFrequency = [];

        if (productId === 131) {
            paymentFrequencyObj = {
                "Yearly": 12
            }
        }
        else {
            paymentFrequencyObj = {
                "Monthly": 1,
                "Quarterly": 3,
                "Half Yearly": 6,
                "Yearly": 12,
                "Single Premium": 13
            }
        }

        setPaymentPeriodicity(paymentFrequencyObj);
        for (const key in paymentFrequencyObj) {
            paymentFrequency.push({ label: key, value: paymentFrequencyObj[key] })
        }
        setOptions(prevState => ({ ...prevState, paymentFrequency }));
    };

    const GetOrderDetail = function () {
        //=====================  On popup call function =============================
        let _ValidationData = require("../../../assets/json/applicationNoValidation").default;
        let _InstallmentsData = require("../../../assets/json/PlansData").default;
        let _SMEJson = require("../../../assets/json/SME").default;
        let _FieldsData = require("../../../assets/json/FieldsData").default;
        setValidationData(_ValidationData);
        setInstallmentsData(_InstallmentsData);
        setSMEJson(_SMEJson);
        setFieldsData(_FieldsData);

        //=====================  End On popup call function =============================
        setOrderList(prevState => prevState.map(order => ({ ...order, ShowBookingDetails: false })))

        if (ShowCreateBookingSection) {
            setShowCreateBookingSection(false);
        }
        else {
            setShowCreateBookingSection(true);
            setIsDisabled(false);
        }

        if (productId == 131 && LeadAssignedAgentId <= 0) {
            setShowCreateBookingSection(false);
            enqueueSnackbar("Cannot book unassigned lead, please get this lead assigned for Booking", { variant: 'error', autoHideDuration: 3000, });
        }
    }

    const GetCustomerBookingDetailsService = (LeadId) => {
        const input = {
            url: `coremrs/api/MRSCore/GetCustomerBookingDetails/${LeadId}/${productId}`,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        return CALL_API(input).then(response => {
            return response;
        });
    }

    const GetBookingDetails = function (index, leadId, Source) {

        //=====================  Start call on demand===========================
        IsFosSmeAgent();
        let _ValidationData = require("../../../assets/json/applicationNoValidation").default;
        let _InstallmentsData = require("../../../assets/json/PlansData").default;
        let _SMEJson = require("../../../assets/json/SME").default;
        let _FieldsData = require("../../../assets/json/FieldsData").default;
        setValidationData(_ValidationData);
        setInstallmentsData(_InstallmentsData);
        setSMEJson(_SMEJson);
        setFieldsData(_FieldsData);
        setFile(null);
        setInvoiceFile(null);
        if (Source == "Toggle") {
            setIsShowBookingDetailsSection(index);
        }
        //=====================  Start call on demand===========================

        if (OrderList[index].ShowBookingDetails) {
            setOrderList(prevState => prevState.map((o, i) => {
                if (i == index) o.ShowBookingDetails = false;
                return o;
            }));
            setIsShowBookingDetailsSection(-1);
        }
        else {
            setShowCreateBookingSection(false);
            getPlanListSupplierWise(OrderList[index]).then((planres) => {
                GetCustomerBookingDetailsService(leadId).then((response) => {
                    if (invalids.indexOf(response) === -1) {
                        let _OrderList = [...OrderList];
                        _OrderList.forEach((item) => {
                            item.ShowBookingDetails = false;
                        })
                        response.ShowBookingDetails = true;
                        _OrderList[index] = { ...response, Plan: _OrderList[index].Plan };
                        
                        // Handle ShipmentType conversion based on Transit Type
                        if (_OrderList[index].ShipmentType) {
                            if (!Array.isArray(_OrderList[index].ShipmentType)) {
                                // Check if Transit Type is Annual Open to determine format
                                if (_OrderList[index].TransitType === "Annual Open") {
                                    // Convert to array format for multi-select
                                    let shipmentTypeArray = [];
                                    const shipmentTypeValues = _OrderList[index].ShipmentType.split(',').map(val => val.trim());
                                    shipmentTypeValues.forEach(value => {
                                        const shipmentType = ShipmentTypes.find(st => st.Id === value || st.Name === value);
                                        if (shipmentType) {
                                            shipmentTypeArray.push(shipmentType);
                                        }
                                    });
                                    _OrderList[index].ShipmentType = shipmentTypeArray;
                                } else {
                                    // Keep as ID string for single-select dropdown
                                    const shipmentTypeValue = _OrderList[index].ShipmentType;
                                    const shipmentType = ShipmentTypes.find(st => st.Id === shipmentTypeValue || st.Name === shipmentTypeValue);
                                    _OrderList[index].ShipmentType = shipmentType ? shipmentType.Id : '';
                                }
                            }
                        } else {
                            // Set default based on Transit Type
                            _OrderList[index].ShipmentType = _OrderList[index].TransitType === "Annual Open" ? [] : '';
                        }
                        
                        // Ensure all SI fields are initialized
                        if (!_OrderList[index].InlandSI) _OrderList[index].InlandSI = '';
                        if (!_OrderList[index].ImportSI) _OrderList[index].ImportSI = '';
                        if (!_OrderList[index].ExportSI) _OrderList[index].ExportSI = '';
                        if (!_OrderList[index].MerchantTradingSI) _OrderList[index].MerchantTradingSI = '';
                        
                        if (productId === 131) {
                            response.Supplier.SupplierId = response.Supplier.OldSupplierId;
                            response.Plan.PlanId = response.Plan.OldPlanId;
                        }

                        let SubProduct = options.SubProduct.filter((sp) => sp.ID == _OrderList[index].SubProduct.ID)[0];
                        let supplier = getSupplierList(_OrderList[index]).filter((s) => s.OldSupplierId == _OrderList[index].Supplier.OldSupplierId)[0];
                        let plan = getOptimizedPlanList(_OrderList[index]).filter((p) => p.OldPlanId == _OrderList[index].Plan.OldPlanId)[0];
                        if (plan) { _OrderList[index].Plan = plan; }
                        if (supplier) { _OrderList[index].Supplier = supplier; }
                        if (SubProduct) { _OrderList[index].SubProduct = SubProduct; }

                        _OrderList[index].SIPerPerson = "";
                        _OrderList[index].PolicyType = options.policyType.filter((pt => pt.PolicyTypeId == response.PolicyTypeId))[0];
                        _OrderList[index].Occupation = options.Occupations.find(o => o.ID == response.OccupancyId && o.SubProductTypeId == response.SubProduct.ID);
                        // _OrderList[index].Occupation = {
                        //     ID: response.OccupancyId,
                        //     Name: ''
                        // }

                        if (invalids.indexOf(response.PaymentDate) === -1) {
                            _OrderList[index].PaymentDate = response.PaymentDate;
                        }
                        else {
                            _OrderList[index].PaymentDate = "";
                        }
                        _OrderList[index].DocumentsRequired = response.DocumentsRequired;
                        _OrderList[index].IsDocReceived = response.IsDocReceived;

                        _OrderList[index].Supplier.ProductId = rootScopeService.getProductId();
                        _OrderList[index].BookingType = BookingTypes[response.BookingType];
                        _OrderList[index].ProductId = rootScopeService.getProductId();
                        _OrderList[index].Medical_or_InspectionRequired = response.Medical_or_InspectionRequired;
                        _OrderList[index].PrevPolicyNo = response.PrevPolicyNo;
                        _OrderList[index].PaidPremium = response.PaidPremium;
                        _OrderList[index].OtherOccupany = response.OtherOccupany;
                        _OrderList[index].SubStatus = options.SubStatusList[0];
                        _OrderList[index].BankNameBranch = response.BankNameBranch;
                        _OrderList[index].TPPremium = response.TPPremium;
                        _OrderList[index].ServiceTax = response.ServiceTax;
                        _OrderList[index].AddonPremium = response.AddonPremium;
                        _OrderList[index].ProposalNo = response.ProposalNo;

                        if (response.IsTP && response.IsTP == "1") {
                            _OrderList[index].ODPremium = response.ODPremium;
                        }
                        else if (_OrderList[index].ODPremium == 0) {
                            _OrderList[index].ODPremium = "";
                        }

                        if (_OrderList[index].DateOfInspection == 0) {
                            _OrderList[index].DateOfInspection = "";
                        }
                        else if (invalids.indexOf(response.DateOfInspection) == -1) {
                            _OrderList[index].DateOfInspection = response.DateOfInspection;
                            _OrderList[index].InspectionStatus = InspectionType.filter((type) => type.ID == response.InspectionStatus)[0];
                        }
                        if (invalids.indexOf(response.PolicyStartDate) == -1) {
                            _OrderList[index].PolicyStartDate = response.PolicyStartDate;
                        }

                        if (invalids.indexOf(response.PolicyEndDate) == -1) {
                            _OrderList[index].PolicyEndDate = response.PolicyEndDate;
                        }

                        if (response.ProductId === 117 && (response.LeadSource.toLowerCase() === "renewal")) {
                            let RenewalPolicyType = [{ "PolicyTypeId": 1, "PolicyTypeName": "Renewal" }];
                            _OrderList[index].PolicyType = RenewalPolicyType[0];
                            //options.PolicyType = RenewalPolicyType;
                            setOptions(prevState => ({ ...prevState, policyType: RenewalPolicyType }));
                            _OrderList[index].BookingType = 2;

                            if ((response.LeadSource.toLowerCase() === "renewal") || (response.PolicyTypeId == 1)) {
                                response.PaymentStatus = 4001;
                                let DirectPayment = [{
                                    "ParentModeValue": 0, "PaymentModeName": "Direct Payment", "PaymentModeValue": 4001
                                }];
                                //options.paymentMode = DirectPayment;
                                setOptions(prevState => ({ ...prevState, paymentMode: DirectPayment }));
                                //setAllPaymentModes(DirectPayment);
                                setIsdisabledPED(true);
                            }

                        }

                        if (invalids.indexOf(response.PaymentStatus) == -1) {
                            _OrderList[index].payment = {
                                PaymentStatus: response.PaymentStatus == 1 ? 1001 : response.PaymentStatus,
                                PaymentPeriodicity: PaymentPeriodicity[response.PaymentPeriodicity],
                                ChequeNo: response.ChequeNo,
                                BankNameBranch: response.BankNameBranch,
                                TransRefNo: response.TransRefNo,
                                IsEMI: response.IsEMI ? 1 : 0,
                                PaymentSubStatus: response.PaymentSubStatus
                            }

                            if (invalids.indexOf(response.IssuanceDate) == -1) {

                                _OrderList[index].IssuanceDate = response.IssuanceDate;
                            }
                        }
                        else {
                            if (rootScopeService.getProductId() === 131) {
                                _OrderList[index].payment = {
                                    PaymentPeriodicity: 4,
                                    IsEMI: 0,
                                }
                            }
                            else {
                                _OrderList[index].payment = {
                                    IsEMI: 0
                                }
                            }

                            // if (rootScopeService.getProductName().indexOf("International") != -1) {
                            //     $scope.OrderList[index].payment = {
                            //         PaymentStatus: 4001,
                            //         PaymentPeriodicity: 4
                            //     }
                            // }
                        }
                        if (_OrderList[index].ProductId == 2 && response.IsSTP != null) {
                            _OrderList[index].IsSTP = response.IsSTP ? 1 : 0;
                        }
                        if (_OrderList[index].ProductId == 101) {
                            //todo might need to update for product 101
                            setIsSIdisabled(true);

                            _OrderList[index].CoverType = options.CoverTypes.find((type) => type.ID === response.CoverageTypeId);
                            _OrderList[index].PropertyType = options.PropertyTypes.find((type) => type.ID === response.PropertyTypeId)
                            _OrderList[index].PropertyPurpose = options.PropertyPurpose.find((type) => type.ID === response.PurposeId)
                            OnCoverTypeChange(_OrderList[index], index);
                        }

                        if (_OrderList[index].ProductId == 131 && (_OrderList[index].SubProduct.ID == 1 || _OrderList[index].SubProduct.ID == 103)) {
                            _OrderList[index].SumInsuredType = SumInsuredTypeOptions.find((i) => i.Name == _OrderList[index].SumInsuredType.Name);
                            _OrderList[index].FamilyType = FamilyTypes.find((i) => i.Name == _OrderList[index].FamilyType.Name);
                        }

                        if (_OrderList[index].ProductId == 131) {
                            OnSubProductChange(_OrderList[index], index, false);
                            OnSupplierChange(_OrderList[index], index, 'Booking', false);

                            if (response.Loading && response.Loading > 0) {
                                _OrderList[index].LD = LDTypes.find((i) => i.Id == 1);
                                _OrderList[index].LDAmount = response.Loading;
                            }
                            else if (response.Discounting && response.Discounting > 0) {
                                _OrderList[index].LD = LDTypes.find((i) => i.Id == 2);
                                _OrderList[index].LDAmount = response.Discounting;
                            }
                            if (response.BurglaryPremium && response.BurglaryPremium > 0) {
                                _OrderList[index].BurglaryPremiumVal = YesNo.find((i) => i.Id == 1);
                                _OrderList[index].BurglaryPremium = response.BurglaryPremium;
                            }
                            else {
                                _OrderList[index].BurglaryPremiumVal = YesNo.find((i) => i.Id == 2);
                            }
                            if (response.TerrorismPremium && response.TerrorismPremium > 0) {
                                _OrderList[index].TerrorismPremiumVal = YesNo.find((i) => i.Id == 1);
                                _OrderList[index].TerrorismPremium = response.TerrorismPremium;
                            }
                            else {
                                _OrderList[index].TerrorismPremiumVal = YesNo.find((i) => i.Id == 2);
                            }

                            if (response.BookingFrom && response.BookingFrom != '') {
                                var BookingFromOptions = Data.BookingFromTypesSME
                                if (response.SubProduct && [1, 2, 3, 4].indexOf(response.SubProduct.ID) != -1) {
                                    BookingFromOptions = Data.BookingFromTypes
                                }
                                _OrderList[index].BookingFrom = BookingFromOptions.find((i) => i.Id == response.BookingFrom);
                            }
                            if (response.QuoteId && response.QuoteId != '') {
                                _OrderList[index].QuoteId = response.QuoteId;
                            }
                            if (response.PolicyCategory && response.PolicyCategory != '') {
                                _OrderList[index].PolicyCategory = Data.PolicyCategory.find((i) => i.Id == response.PolicyCategory);
                            }
                            if (response.BookingCategory && response.BookingCategory != '') {
                                _OrderList[index].BookingCategory = Data.BookingCategory.find((i) => i.Id == response.BookingCategory);
                            }
                            if (response.PlanType && response.PlanType != '') {
                                _OrderList[index].PlanType = Data.PlanType.find((i) => i.Id == response.PlanType);
                            }
                            if (response.ProjectDuration !== null && response.ProjectDuration !== undefined) {
                                _OrderList[index].ProjectDuration = response.ProjectDuration;
                            }

                            if (_OrderList[index].PolicyStartDate === 0) {
                                _OrderList[index].PolicyStartDate = new Date();
                                _OrderList[index].PolicyEndDate = new Date();
                            }

                            _OrderList[index].CoInsurance = _OrderList[index].CoInsurance ? 1 : 0;
                            _OrderList[index].LeadersPercentage = (_OrderList[index].LeadersPercentage && _OrderList[index].LeadersPercentage > 0) ? _OrderList[index].LeadersPercentage : '';

                            if (_OrderList[index].FollowerSuppliers && Array.isArray(_OrderList[index].FollowerSuppliers)) {
                                let allSuppliers = (IsShowSupplierBySubProduct && Array.isArray(SupplierBySubProduct) && SupplierBySubProduct.length > 0) ? SupplierBySubProduct : supplierList;
                                let folSuppliers = _OrderList[index].FollowerSuppliers;
                                let followerSuppliers = [];
                                allSuppliers.forEach(sup => {
                                    for (var folSupIndex = 0; folSupIndex < folSuppliers.length; folSupIndex++) {
                                        if (folSuppliers[folSupIndex].SupplierId == sup.OldSupplierId) {
                                            followerSuppliers.push({ FollowerSupplier: sup, FollowerPercentage: folSuppliers[folSupIndex].FollowerPercentage });
                                            folSuppliers.splice(folSupIndex, 1);
                                            break;
                                        }
                                    }
                                });
                                _OrderList[index].FollowerSuppliers = followerSuppliers;
                            }

                            if (!_OrderList[index].Brokerage || _OrderList[index].Brokerage <= 0) {
                                _OrderList[index].Brokerage = response.Brokerage ? response.Brokerage : 0;
                            }

                            if (_OrderList[index].TransitFromCityId && _OrderList[index].TransitFromCityId > 0) {
                                _OrderList[index].TransitFrom = options.cities.filter((sp) => sp.CityID == _OrderList[index].TransitFromCityId)[0];
                            }
                            if (_OrderList[index].TransitToCityId && _OrderList[index].TransitToCityId > 0) {
                                _OrderList[index].TransitTo = options.cities.filter((sp) => sp.CityID == _OrderList[index].TransitToCityId)[0];
                            }
                            if (_OrderList[index].Inclusion && _OrderList[index].Inclusion.length > 0) {
                                let inclusions = [];
                                _OrderList[index].Inclusion.forEach(element => {
                                    inclusions.push(Inclusions.filter((sp) => sp.Id == element)[0]);
                                });
                                _OrderList[index].Inclusion = inclusions;
                            }
                            if (_OrderList[index].RiskLocations && _OrderList[index].RiskLocations.length > 0) {
                                let riskLocation = [];
                                _OrderList[index].RiskLocations.forEach(e => {
                                    let city = null;
                                    if (e.CityId) {
                                        city = options.cities.filter((sp) => sp.CityID == e.CityId)[0];
                                    }
                                    riskLocation.push({ RiskAddress: e.RiskAddress, City: city, PinCode: e.PinCode });
                                });
                                _OrderList[index].RiskLocations = riskLocation;
                            }
                            if (_OrderList[index].WorkerTypes && _OrderList[index].WorkerTypes.length > 0) {
                                let workerTypes = [];
                                _OrderList[index].WorkerTypes.forEach(e => {
                                    workerTypes.push(WorkerTypes.filter((sp) => sp.Name == e.WorkerType)[0]);
                                    if (e.WorkerType === "Skilled") {
                                        _OrderList[index].NoOfWorkerSkilled = e.NoOfWorker
                                        _OrderList[index].SalaryOfWorkerSkilled = e.Salary
                                    }
                                    else if (e.WorkerType === "Semi-Skilled") {
                                        _OrderList[index].NoOfWorkerSemiSkilled = e.NoOfWorker
                                        _OrderList[index].SalaryOfWorkerSemiSkilled = e.Salary
                                    }
                                    else if (e.WorkerType === "Un-Skilled") {
                                        _OrderList[index].NoOfWorkerUnSkilled = e.NoOfWorker
                                        _OrderList[index].SalaryOfWorkerUnSkilled = e.Salary
                                    }
                                    else if (e.WorkerType === "Other") {
                                        _OrderList[index].NoOfWorkerOther = e.NoOfWorker
                                        _OrderList[index].SalaryOfWorkerOther = e.Salary
                                    }
                                });

                                _OrderList[index].WorkerType = workerTypes;
                            }

                            if (_OrderList[index].MedicalExtension) {
                                if (MedicalExtensions.findIndex((e) => e.Id == _OrderList[index].MedicalExtension) === -1) {
                                    _OrderList[index].OtherMedicalExtension = _OrderList[index].MedicalExtension;
                                    _OrderList[index].MedicalExtension = "Other";
                                }
                            }

                            if (_OrderList[index].PropertyTypeId)
                                _OrderList[index].PropertyType = options.SmePropertyTypes.find((type) => type.ID === _OrderList[index].PropertyTypeId)

                            if (_OrderList[index].BuildingSI || _OrderList[index].ContentSI || _OrderList[index].StockSI) {
                                _OrderList[index].InsuredScope = [];
                                if (_OrderList[index].BuildingSI && _OrderList[index].BuildingSI > 0) {
                                    _OrderList[index].BuildingValue = _OrderList[index].BuildingSI;
                                    _OrderList[index].InsuredScope.push(Data.InsuredScopes[0]);
                                }
                                if (_OrderList[index].ContentSI && _OrderList[index].ContentSI > 0) {
                                    _OrderList[index].ContentValue = _OrderList[index].ContentSI;
                                    _OrderList[index].InsuredScope.push(Data.InsuredScopes[1]);
                                }
                                if (_OrderList[index].StockSI && _OrderList[index].StockSI > 0) {
                                    _OrderList[index].StockValue = _OrderList[index].StockSI;
                                    _OrderList[index].InsuredScope.push(Data.InsuredScopes[2]);
                                }
                            }
                            if (_OrderList[index].ChildOccupancies && _OrderList[index].SubProduct && _OrderList[index].SubProduct.ID && _OrderList[index].SubProduct.ID == 19) {
                                var ids = _OrderList[index].ChildOccupancies.split(',');
                                const childOccArray = options.Occupations.filter(item =>
                                    ids.some(id => item.ID == id) &&
                                    parseInt(item.SubProductTypeId) === _OrderList[index].SubProduct.ID
                                );
                                _OrderList[index].ChildOccupancies = childOccArray;
                            }
                            else {
                                if (_OrderList[index].ChildOccupancyId && _OrderList[index].ChildOccupancyId > 0) {
                                    _OrderList[index].ChildOccupation = options.Occupations.find(o => o.ID == _OrderList[index].ChildOccupancyId && o.SubProductTypeId == _OrderList[index].SubProduct.ID);
                                }
                            }
                            if (_OrderList[index].SubProduct && _OrderList[index].SubProduct.ID === 14 && _OrderList[index].AssociationId && Association) {
                                _OrderList[index].Association = Association.find((a) => a.Id === _OrderList[index].AssociationId)
                            }
                        }
                        else {
                            OnSubProductChange(_OrderList[index], index, true);
                        }

                        if (_OrderList[index].ProductId == 2) {
                            setIsNotHealthRenewal(_OrderList[index].LeadSource !== "Renewal");
                        }
                        if (_OrderList[index].ProductId == 3) {
                            _OrderList[index].DestinationCountry = response.DestinationCountry || '';
                        }

                        // updateSIPerPerson(_OrderList[index], index);
                        setOrderListOriginal(_OrderList);
                        onOccupationChange(_OrderList[index], index);
                        setOrderList(_OrderList);
                        setUpdateOrderPlan(true);
                    }
                }, function (error) {
                    console.log(error);
                });
            });
        }
    };

    const GetCustomerOrderService = (leadId) => {
        const input = {
            url: `coremrs/api/MRSCore/GetCustomerOrder/${leadId}`,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        return CALL_API(input).then(response => {
            return response;
        });
    }

    const GetCustomerOrder = function () {
        if (!leadId)
            return;
        GetCustomerOrderService(leadId).then(function (response) {
            let _orderList = [];
            let _activeLeadsWithoutOrder = activeLeadsWithoutOrder;
            if (invalids.indexOf(response) === -1) {
                response.forEach((item) => {
                    if (invalids.indexOf(item.OfferNumber) === -1) {
                        if (item.Supplier && item.Supplier.hasOwnProperty('OldSupplierId')) {
                            if (productId === 131) {
                                item.Supplier.SupplierId = item.Supplier.OldSupplierId;
                                item.Plan.PlanId = item.Plan.OldPlanId;
                                item.Supplier = { ...item.Supplier, ...(getSupplierList(item).filter((s) => s.OldSupplierId === item.Supplier.OldSupplierId)[0]) };
                                item.Plan = item.Plan;
                            }
                            else {
                                //set supplier and plans
                                item.Supplier = getSupplierList(item).filter((s) => s.OldSupplierId === item.Supplier.OldSupplierId)[0];
                                //item.Plan = getOptimizedPlanList(item).filter((p) => p.OldPlanId === item.Plan.OldPlanId)[0];
                                item.Plan = item.Plan;
                            }

                        }
                        _orderList.push(item);
                    }
                    else {
                        _activeLeadsWithoutOrder.push(item);
                    }
                });

                setOrderList(_orderList);
                setActiveLeadsWithoutOrder(_activeLeadsWithoutOrder);
                if (_orderList.length > 0) {
                    setCallGetBookingDetails(true);
                }
            }
        }, function (error) {
            console.log(error);
        });

    };
    const getActiveLeads = function () {

        let activeStatus = [1, 2, 3, 4, 11];
        let _ActiveLeadCollection = [];
        let data = Array.isArray(allLeads) ? allLeads.filter((lead) => lead.StatusMode == 'P') : [];

        data.forEach((item) => {
            if (activeStatus.indexOf(item.StatusId) !== -1) {
                if ((item.OfferNumber == undefined || item.OfferNumber == null || item.OfferNumber == "") && item.SupplierId <= 0) {
                    _ActiveLeadCollection.push({ leadId: item.LeadID });
                }
            }
        });

        setOptions(prevState => ({ ...prevState, activeLeadCollection: _ActiveLeadCollection }));
        if (_ActiveLeadCollection.length > 0) {
            let _newOrder = {
                ...newOrderStructure,
                leadId: _ActiveLeadCollection[0].leadId,
                ActiveLead: {
                    leadId: _ActiveLeadCollection[0].leadId
                },
                ProductId: rootScopeService.getProductId()
            }
            setNewOrder(_newOrder);
        }
        return _ActiveLeadCollection;
    };
    const updateVisibleAndPolicyTypes = () => {
        if (productId === 2) {
            setVisible(ProductVisibility.Health)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 115) {
            setVisible(ProductVisibility.Investment)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Investment }))
        }
        else if (productId === 7) {
            setVisible(ProductVisibility.Term)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Term }))
        }
        else if (productId === 117) {
            setVisible(ProductVisibility.Motor)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Motor }))
        }
        else if (productId === 131) {
            setVisible(ProductVisibility.SME)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.SME, SmePropertyTypes: Data.SmePropertyType }))
            setIsdisabledPED(true);
            masterService.getCities().then(function (cities) {
                setOptions(prevState => ({ ...prevState, cities }))
            });

            masterService.getSMEOccupationList().then(function (Occupations) {
                setOptions(prevState => ({ ...prevState, Occupations }))
            });

            masterService.getSMEPlanDeal().then(function (SMEPlanDeal) {
                setOptions(prevState => ({ ...prevState, SMEPlanDeal }))
            });
        }
        else if (productId === 165) {
            setVisible(ProductVisibility.Partnerships)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.SME }))
        }
        else if ([106, 118, 130].indexOf(productId) != -1) {
            setVisible(ProductVisibility.CIPA_STU)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 3) {
            setVisible(ProductVisibility.Travel)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 138) {
            setVisible({ ...ProductVisibility.Default, PaymentSource: false })
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 114) {
            setVisible(ProductVisibility.TwoWheeler)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 148) {
            setVisible(ProductVisibility.IntMotor)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.IntMotor }))
        }
        else if (productId === 150) {
            setVisible(ProductVisibility.IntTravel)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.IntTravel }))
        }
        else if (productId === 154) {
            setVisible(ProductVisibility.IntTCyberSaferavel)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 139) {
            setVisible(ProductVisibility.Commercial)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (intloanPrds.indexOf(productId) != -1) {
            setVisible(ProductVisibility.INTLoan)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }
        else if (productId === 101) {
            setVisible({ ...ProductVisibility.HOME, PaymentSource: false });
            setIsdisabledPED(true);
            setOptions(prevState => ({
                ...prevState,
                policyType: Data.PolicyType.Health,
                CoverTypes: Data.PropertyCoverType,
                PropertyPurpose: Data.PropertyPurpose,
                PropertyTypes: Data.PropertyType
            }));
            masterService.getCities().then(function (cities) {
                setOptions(prevState => ({ ...prevState, cities }))
            });
        }
        else {
            setVisible(ProductVisibility.Default)
            setOptions(prevState => ({ ...prevState, policyType: Data.PolicyType.Health }))
        }

        if (Visible !== undefined && [151, 148, 155, 150, 163, 153, 176].indexOf(productId) != -1) {
            Visible.PBDiscount = true;
            Visible.InsurerFee = true;
            Visible.PaidPremium = true;

        }

        if (Visible != undefined && [148, 151, 155].indexOf(productId) != -1) {
            Visible.IsRevenueAddition = true;
            Visible.IsPremiumExclusion = true;

        }
    }
    const setSubPaymentModes = (item) => {
        let _SubPaymentModes = [];
        let _IsNotPaySubMode = true;
        if (item.payment != undefined) {
            let val = -1;
            AllPaymentModes.forEach((mode) => {
                val = item.payment.PaymentStatus;

                if (val == mode.ParentModeValue) {
                    _IsNotPaySubMode = false;
                    _SubPaymentModes.push(mode);
                }
            })
        }
        setSubPaymentModesOptions(_SubPaymentModes);
        // setOptions(prevState => ({ ...prevState, SubPaymentModes: _SubPaymentModes }));
        setIsNotPaySubMode(_IsNotPaySubMode);
    };
    const SaveComment = (comment) => {
        let reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            ProductId: rootScopeService.getProductId(),
            ParentLeadId: parentLeadId,
            PrimaryLeadId: primaryLeadId,
            UserId: User.UserId,
            Comment: comment,
            EventType: 10
        };
        SetCustomerComment(reqData);
    }
    const RejectOtherChildLead = function (LeadID) {
        let Rleads = [], leadx = "";
        let index = -1;
        allLeads.forEach((lead, i) => { if (lead.LeadID === LeadID) { index = i } });

        if (rootScopeService.getProductId() === 7) {
            allLeads.forEach((vdata, key) => {
                if (vdata.Age == allLeads[index].Age) {
                    if (vdata.StatusId == 1 || vdata.StatusId == 2 || vdata.StatusId == 3 || vdata.StatusId == 4 || vdata.StatusId == 11) {
                        Rleads.push(vdata.LeadID);
                        leadx = vdata.LeadID + "," + leadx;
                    }
                }
            });
        }
        else if (rootScopeService.getProductId() === 115) {
            allLeads.forEach((vdata, key) => {
                if ((vdata.Age + '-' + vdata.SubProductTypeId) == (allLeads[index].Age + '-' + allLeads[index].SubProductTypeId)) {
                    if (vdata.StatusId == 1 || vdata.StatusId == 2 || vdata.StatusId == 3 || vdata.StatusId == 4 || vdata.StatusId == 11) {
                        Rleads.push(vdata.LeadID);
                        leadx = vdata.LeadID + "," + leadx;
                    }
                }
            });
        }
        // else if (rootScopeService.getProductId() === 117) {
        //     allLeads.forEach(allLeads, function (vdata, key) {
        //         if ((vdata.Make + '-' + vdata.Model) == (allLeads[index].Make + '-' + allLeads[index].Model)) {
        //             if (vdata.StatusId == 1 || vdata.StatusId == 2 || vdata.StatusId == 3 || vdata.StatusId == 4 || vdata.StatusId == 11) {
        //                 Rleads.push(vdata.LeadID);
        //                 leadx = vdata.LeadID + "," + leadx;
        //             }
        //         }
        //     });
        // };

        if (Rleads.length > 1) {
            let productId = rootScopeService.getProductId();
            let response = GetRejectLeadsInput(leadx, -1, productId, User.UserId, 0, 0, null);
            GetRejectLeads(response.input, response.isCoreApi).then((resultData) => {
                if (resultData && resultData.Data) {
                    props.setRefreshLeadToRedux(true);
                    enqueueSnackbar('Lead Rejected Successfully!', { variant: 'success', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar('Something went wrong, Please Connect to Support team.', { variant: 'error', autoHideDuration: 3000, });
                }
            });
        };
    };

    const CancelNewOrder = () => {
        setNewOrder(newOrderStructure);
        setShowCreateBookingSection(false);
        setIsDisabled(false);
    };
    const CancelEditOrder = (index) => {
        setIsDisabled(true);
        // if (OrderListOriginal.length > 0 && OptimzedPlansListforBookedLeads.length > 0) {
        //     let _OrderListOriginalCopy = JSON.parse(JSON.stringify(OrderListOriginal));
        //     let currentOrderIndex = -1;
        //     _OrderListOriginalCopy.forEach((item, index) => {
        //         if(item.ShowBookingDetails){
        //             currentOrderIndex=index;
        //         }
        //     })
        //     let plan = getOptimizedPlanList(_OrderListOriginalCopy[currentOrderIndex]).filter((p) => p.OldPlanId == _OrderListOriginalCopy[currentOrderIndex].Plan.OldPlanId)[0];
        //     if (plan) {
        //             setOrderList(OrderListOriginal.map((o, i) => {
        //                 const newObj = {...o};
        //                 if (i == currentOrderIndex)
        //                 {
        //                     newObj.Plan = plan;
        //                     newObj.ShowBookingDetails = true;
        //                 }
        //                 return newObj;
        //             }));
        //          }
        // }
        setOrderList(OrderListOriginal.map((o, i) => {
            if (i === index) { o.ShowBookingDetails = false; }
            return o;
        }));
    }

    const EditClicked = (item, index) => {
        setIsTPChecked(false);
        setIsDisabled(false);
        setIsDisabledForSmeElements(false);

        if (item.ProductId == 131
            && item.PolicyType.PolicyTypeId == 1
            && item.PrevPolicyNo !== ""
            && item.PreviousBookingNo !== "") {
            setIsDisabledForSmeElements(true);
        }

        if (item.IsTP == "1") {
            setIsTPChecked(true);
        }
    }

    const ValidateOrder = async (item, itemType = 0) => {
        setIsSaveUpdateEnabled(false);
        try {
            if (item && item.ProductId == 131 && item.SalesPartners && Array.isArray(item.SalesPartners) && item.SalesPartners.length > 0) {
                if (window.confirm('Have you cross checked the Revenue Percentages? If yes then click OK to save')) {
                    await _ValidateOrder(item, itemType);
                }
            }
            else {
                await _ValidateOrder(item, itemType);
            }
        }
        catch (e) {
            console.error(e);
        }
        setIsSaveUpdateEnabled(true);
    };

    const _ValidateOrder = async (item, itemType = 0) => {

        //ShowPaymentDetails == payment details enable or disable
        if (item.ShowPaymentDetails == 1) {
            var message = "Please select payment mode";
            var payment = AllPaymentModes.forEach((mode) => {
                if (mode.PaymentModeValue == item.payment.PaymentStatus && mode.ParentModeValue == 0) {
                    message = "";
                }
            });
            if (message != "") {
                enqueueSnackbar(message, { variant: 'error', autoHideDuration: 3000 });
                return;
            }
        }
        // Determine which original values to pass based on whether this is renewal data
        let originalValuesToPass = null;
        if (OrderListOriginal && OrderListOriginal[IsShowBookingDetailsSection]) {
            originalValuesToPass = OrderListOriginal[IsShowBookingDetailsSection];
        } else if (NewOrderOriginal && item === newOrder) {
            // For renewal scenarios, pass renewal original values with a flag
            originalValuesToPass = { ...NewOrderOriginal, isRenewal: true };
        }

        let result = IsValidItem(item,
            item.ShowPaymentDetails ? 1 : 0,
            itemType,
            File ? File.name : "",
            item.ProductId == 131 ? SupplierBySubProduct : [],
            item.ProductId == 131 ? OptimzedPlansListforBookedLeads : [],
            InvoiceFile ? InvoiceFile.name : "",
            isSmeDocsUploadValid,
            IsSmeFosAgent,
            originalValuesToPass);

        if (result.IsValid == false) {
            enqueueSnackbar(result.Message, { variant: 'error', autoHideDuration: 6000, style: { whiteSpace: 'pre-line' } });
        }
        else {
            let data;
            if (productId === 7) {
                data = ValidationData.Product[0].TermLife;
            } else if (productId === 115) {
                data = ValidationData.Product[0].Investment;
            } else if (productId === 138) {
                data = ValidationData.Product[0].Cancer;
            } else if (productId === 143) {
                data = ValidationData.Product[0].Heart;
            } else if (productId === 144) {
                data = ValidationData.Product[0].ComprehensiveCI;
            }

            let errorMessage = '';
            let ValidAppNo = true;
            if ([7, 115, 138, 143, 144].indexOf(rootScopeService.getProductId()) !== -1) {
                ValidAppNo = false;
                if ((item.ShowPaymentDetails == 1 || rootScopeService.getProductId() === 7)) {
                    data.forEach((element, index) => {
                        let SupplierId = item.Supplier.OldSupplierId;
                        let PlanId = item.Plan.OldPlanId;
                        let ApplicationNo = item.ApplicationNo;

                        let PolicyNo = item.PolicyNo;
                        if (parseInt(element.supplier) == SupplierId && (parseInt(element.Plan) == PlanId || element.Plan == "") && (!ValidAppNo) && result.IsValid) {
                            if (!isValidApplicationNumber(ApplicationNo, element.rule)) {

                                if (item.ApplicationNo.indexOf('123') === 0 && productId === 7 && item.Supplier.OldSupplierId == 10) {
                                    result.IsValid = true;
                                    ValidAppNo = true;
                                }
                                else {

                                    errorMessage = element.msg;
                                    result.IsValid = false;
                                }
                            }
                            else {
                                result.IsValid = true;
                                ValidAppNo = true;
                            }
                        }
                    });
                }
            }
            if (ValidAppNo == false && errorMessage != '')
                alert(errorMessage);
        }

        if (result.IsValid) {
            var reqDataAudit = [];
            OrderListOriginal.forEach((vdata, key) => {
                if (vdata.leadId == result.Item.MatrixLeadId) {
                    for (const [key1, vdata1] of Object.entries(result.Item)) {

                        var fieldVal = FieldsData.Field[key1];
                        if ((typeof vdata1 != "object") && (key1 != "MatrixLeadId") && (vdata[key1] != vdata1) && (vdata[key1] != undefined) && fieldVal > '') {
                            reqDataAudit.push({
                                LeadId: vdata.leadId,
                                AgentId: User.UserId,
                                SectionName: "Orders",
                                Field: fieldVal,
                                OldValue: vdata[key1],
                                NewValue: vdata1,
                                ProductId: rootScopeService.getProductId()
                            });
                        }

                    }
                    if (vdata.Plan.PlanName != result.Item.Plan.PlanName && result.Item.Plan.PlanName != undefined) {
                        reqDataAudit.push({
                            LeadId: vdata.leadId,
                            AgentId: User.UserId,
                            SectionName: "Orders",
                            Field: FieldsData.Field["Plan"],
                            OldValue: vdata.Plan.PlanName,
                            NewValue: result.Item.Plan.PlanName,
                            ProductId: rootScopeService.getProductId()
                        });
                    }

                    if (vdata.CityState != undefined && result.Item.CityState != undefined) {
                        if (vdata.CityState.CityStateName != result.Item.CityState.CityStateName && result.Item.CityState.CityStateName != undefined) {
                            reqDataAudit.push({
                                LeadId: vdata.leadId,
                                AgentId: User.UserId,
                                SectionName: "Orders",
                                Field: FieldsData.Field["City"],
                                OldValue: vdata.CityState.CityStateName,
                                NewValue: result.Item.CityState.CityStateName,
                                ProductId: rootScopeService.getProductId()
                            });
                        }
                    }

                    if (vdata.Supplier.SupplierName != result.Item.Plan.SupplierDisplayName && result.Item.Plan.SupplierDisplayName != undefined) {
                        reqDataAudit.push({
                            LeadId: vdata.leadId,
                            AgentId: User.UserId,
                            SectionName: "Orders",
                            Field: FieldsData.Field["Supplier"],
                            OldValue: vdata.Supplier.SupplierName,
                            NewValue: result.Item.Plan.SupplierDisplayName,
                            ProductId: rootScopeService.getProductId()
                        });
                    }
                }
            })
            if (reqDataAudit.length > 0) {
                //  callAPI
                SetLeadAudit(reqDataAudit).then((resultData) => {
                    enqueueSnackbar("Lead Audit Details Saved", { variant: 'success', autoHideDuration: 3000, });
                }, function () {
                    console.log("Lead Audit not saved");
                })
            }

            var reqData = {
                Data: {
                    User: {
                        UserId: User.UserId
                    },
                    InputData: result.Item
                }
            };

            if (productId == 131) {
                let fileUploadResponse = null;
                try {
                    if (File) {
                        fileUploadResponse = await HandleFileUpload(item);
                        if (!fileUploadResponse.IsSaved) {
                            enqueueSnackbar(fileUploadResponse.Message, { variant: 'error', autoHideDuration: 3000, });
                            setFile(null);
                            return;
                        }
                    }
                    if (InvoiceFile) {
                        fileUploadResponse = await HandleMiscFileUpload();
                        if (fileUploadResponse && fileUploadResponse.ok) {
                            reqData.Data.InputData.LeadDocument = { DocId: fileUploadResponse.docId, DocTypeId: invoiceDocTypeId, FileName: InvoiceFile.name };
                        }
                        else {
                            enqueueSnackbar(fileUploadResponse.msg, { variant: 'error', autoHideDuration: 3000, });
                            setInvoiceFile(null);
                            return;
                        }
                    }
                }
                catch (error) {
                    enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
                    setFile(null);
                    setInvoiceFile(null);
                    return;
                }
            }

            AddOrder(reqData).then((response) => {
                if (response && response.IsSaved) {
                    enqueueSnackbar("Successfully Saved.", { variant: 'success', autoHideDuration: 3000, });
                    setIsDisabled(true);
                    props.setRefreshLeadToRedux(true);
                    setCallGetBookingDetails(true);
                    setNewOrder(newOrderStructure);
                    setOrderList([]);
                    setActiveLeadsWithoutOrder([]);
                    SaveComment("Order Details Updated.");
                }
                else {
                    if (response && response.Message) {
                        enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 3000, });
                    } else if (response && response.StatusCode === -3) {
                        enqueueSnackbar("Application No. already Exists on LeadId: " + response.Message, { variant: 'error', autoHideDuration: 3000, });
                    }
                    else {
                        enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
                    }
                }
            }, function (reason) {
                if (reason.Error == "") {
                    enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });

                } else if (reason.ErrorCode == -3) {
                    enqueueSnackbar("Application No. already Exists on LeadId: " + reason.Error, { variant: 'error', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar(reason.Error, { variant: 'error', autoHideDuration: 3000, });
                }
            });
        }
    };

    const AddOrder = (reqData) => {
        const input = {
            url: "api/Bms/CreateBooking",
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: reqData.Data.InputData
        }
        return CALL_API(input).then(response => {
            return response;
        });
    }

    const OpenUploadDocuments = (leadId, subProductId, isSmePos, transitType) => {
        reduxDispatch(updateStateInRedux({ key: "DocsUploadPanel", value: true }))
        refUploadFiles.current = { leadId, subProductId, isSmePos, transitType, IsSmeFosAgent, IsSmeFosAgentGroup };
    }
    const shouldShowPremiumFields = (item) => {
        return productId === 131 && item.SubProduct && item.SubProduct.ID &&
            [5, 7, 8, 112, 113].includes(item.SubProduct.ID) && item.TotalPremium > 0;
    }

    const IsFosSmeAgent = () => {
        try {
            // Check if FOS 1, FOS 2, FOS 3, FOS 4, Online Hybrid
            if (productId == 131 && User.RoleId == 13) {
                let groupList = User.UserGroupList;
                if (!IsSmeFosAgent) {
                    let smeFosGroupsForBrokerage = SV_CONFIG["SMEFOSGroups"];
                    if (Array.isArray(smeFosGroupsForBrokerage) && Array.isArray(groupList)) {
                        for (var i = 0, len = groupList.length; i < len; i++) {
                            if (smeFosGroupsForBrokerage.indexOf(groupList[i].GroupId) > -1) {
                                setIsSmeFosAgent(true);
                                break;
                            }
                        }
                    }
                }
                if (!IsSmeFosAgentGroup) {
                    let SmeFosGroupsPolicyCopyDisable = SV_CONFIG["SMEFOSGroups"];
                    if (Array.isArray(SmeFosGroupsPolicyCopyDisable) && Array.isArray(groupList)) {
                        for (var i = 0, len = groupList.length; i < len; i++) {
                            if (SmeFosGroupsPolicyCopyDisable.indexOf(groupList[i].GroupId) > -1) {
                                setIsSmeFosAgentGroup(true);
                                break;
                            }
                        }
                    }
                }
            }
        }
        catch (e) {
            console.log(JSON.stringify(e));
        }
    };

    const getSourceFromUrl = () => {
        var value = "";
        try {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.size > 0) {
                value = urlParams.get("src");
            }
        }
        catch {

        }
        return value;
    }

    const ValidateBooking = async (item) => {

        // Determine which original values to pass for ValidateBooking
        let originalValuesToPass = null;
        if (OrderListOriginal && OrderListOriginal[IsShowBookingDetailsSection]) {
            originalValuesToPass = OrderListOriginal[IsShowBookingDetailsSection];
        } else if (NewOrderOriginal && item === newOrder) {
            // For renewal scenarios, pass renewal original values with a flag
            originalValuesToPass = { ...NewOrderOriginal, isRenewal: true };
        }

        let result = IsValidItem(item, 1, 1, "",
            item.ProductId == 131 ? SupplierBySubProduct : [],
            item.ProductId == 131 ? OptimzedPlansListforBookedLeads : [],
            InvoiceFile ? InvoiceFile.name : "",
            isSmeDocsUploadValid,
            IsSmeFosAgent,
            originalValuesToPass);

        if (result.IsValid == false) {
            enqueueSnackbar(result.Message, { variant: 'error', autoHideDuration: 6000, style: { whiteSpace: 'pre-line' } });
        }
        else {
            if (Visible.SubStatus == 1) {
                if (invalids.indexOf(item.SubStatus.SubStatusId) != -1) {
                    enqueueSnackbar("Select Lead SubStatus", { variant: 'error', autoHideDuration: 3000, });
                    return;
                }
            }
            if (item.ShowPaymentDetails == 1) {
                var message = "Please select payment mode";
                var payment = AllPaymentModes.forEach((mode) => {
                    if (mode.PaymentModeValue == item.payment.PaymentStatus && mode.ParentModeValue == 0) {
                        message = "";
                    }
                });
                if (message != "") {
                    enqueueSnackbar(message, { variant: 'error', autoHideDuration: 3000, });
                    return;
                }
            }

            try {
                if (isCustomerVerified != true && Visible.OTP.IsMandatory) {
                    if (Visible.OTP.Payment.indexOf(item.payment.PaymentStatus) != -1) {
                        enqueueSnackbar("Customer OTP Verification Pending", { variant: 'error', autoHideDuration: 3000, });
                        return;
                    }
                }
            }
            catch (error) {
            }
            let data;
            if (rootScopeService.getProductId() === 7) {
                data = ValidationData.Product[0].TermLife;
            } else if (productId == 115) {
                data = ValidationData.Product[0].Investment;
            } else if (productId == 138) {
                data = ValidationData.Product[0].Cancer;
            } else if (productId == 143) {
                data = ValidationData.Product[0].Heart;
            } else if (productId == 144) {
                data = ValidationData.Product[0].ComprehensiveCI;
            }
            if ([7, 115, 138, 143, 144].indexOf(productId) != -1) {
                data.forEach((element, index) => {
                    let SupplierId = item.Supplier.OldSupplierId;
                    let PlanId = item.Plan.OldPlanId;
                    let ApplicationNo = item.ApplicationNo;
                    if (parseInt(element.supplier) == SupplierId && (parseInt(element.Plan) == PlanId || element.Plan == "")) {
                        if (!isValidApplicationNumber(ApplicationNo, element.rule)) {
                            if (item.ApplicationNo.indexOf('123') == 0 && productId == 7 && SupplierId == 10) {
                                result.IsValid = true;
                            }
                            else {
                                enqueueSnackbar(element.msg, { variant: 'error', autoHideDuration: 3000, });
                                result.IsValid = false;
                            }
                        }
                    }
                });
            }
        }

        if (result.IsValid) {
            var reqData = {
                Data: {
                    InputData: {
                        leadId: item.leadId,
                        PaymentStatus: item.payment.PaymentStatus,
                        PaymentSubStatus: item.payment.PaymentSubStatus,
                        Product: {
                            ProductId: rootScopeService.getProductId()
                        },
                        UserTimeStamp: {
                            CreatedById: User.UserId
                        },
                        SubStatusID: item.SubStatus.SubStatusId
                    }
                }
            };

            if (productId == 131 && item.LeadDocuments && item.LeadDocuments.length > 0) {
                item.LeadDocuments.forEach(f => {
                    if (f.DocTypeId == docTypeId) {
                        result.Item.DocumentId = f.DocId;
                    }
                })
            };

            if (productId == 131) {
                const UrlSource = getSourceFromUrl();
                result.Item.Source = UrlSource;
            }

            if (productId == 131 && LeadAssignedAgentId <= 0) {
                enqueueSnackbar("Cannot book unassigned lead, please get this lead assigned for Booking", { variant: 'error', autoHideDuration: 3000, });
                return;
            }

            let callConfirmPayment = true;
            if ((productId === 131) && item.SubProduct && item.SubProduct.ID && (item.SubProduct.ID === 13) && MarineAnualOpenLeadData) {
                try {
                    if (item.TransitType && ['Annual Transit', 'Annual Open', 'Single Transit'].indexOf(item.TransitType) > -1) {
                        for (let i = 0; i < MarineAnualOpenLeadData.length; i++) {
                            let oldData = MarineAnualOpenLeadData[i];
                            if (item.OccupancyId == oldData.OccupancyId && item.ShipmentType == oldData.ShipmentType) {
                                setAnualOpenPopupLeadId(item.leadId);
                                dataForBooking.current = { 'reqData': reqData, 'result': result, 'item': item }
                                callConfirmPayment = false;
                                break;
                            }
                        }
                    }
                }
                catch { }
            }
            if (callConfirmPayment) {
                ConfirmPaymentForLead(reqData, result, item);
            }
        }
    };

    const ConfirmPaymentForLead = (reqData, result, item) => {
        if(rootScopeService.getProductId() === 131 && User.RoleId === 13){
            proceedWithBooking(reqData, result, item);
        }
        else{
        ConfirmPayment(reqData, result.Item).then((response) => {
            if (response && response.IsSaved) {
                enqueueSnackbar("Successfully Saved.", { variant: 'success', autoHideDuration: 3000, });
                setIsDisabled(true);
                try {
                    if (rootScopeService.getProductId() === 7 || rootScopeService.getProductId() === 115) {
                        RejectOtherChildLead(item.leadId);
                    }
                }
                catch (e) {
                    console.log(e.message);
                }
                props.setRefreshLeadToRedux(true);
            }
            else {
                if (response.Message === "") {
                    enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
                } else if (response.StatusCode == -3) {
                    enqueueSnackbar("Application No. already Exists on LeadId: " + response.Message, { variant: 'error', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
                }
            }
            if (response && response.Warnings) {
                enqueueSnackbar(response.Warnings, { variant: 'info', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            }
        }, function (reason) {
            if (reason.Error == "") {
                enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
            } else if (reason.ErrorCode == -3) {
                enqueueSnackbar("Application No. already Exists on LeadId: " + reason.Error, { variant: 'error', autoHideDuration: 3000, });
            } else {
                enqueueSnackbar(reason.Error, { variant: 'error', autoHideDuration: 5000, style: { whiteSpace: 'pre-line' } });
            }
        });
    }
    }

    const proceedWithBooking = (reqData, result, item) => {
        // This method is only used for product 131 (SME) with lead rejection popup
        ConfirmPayment(reqData, result.Item).then(async (response) => {
            if (response && response.IsSaved) {
                enqueueSnackbar("Successfully Saved.", { variant: 'success', autoHideDuration: 3000, });
                setIsDisabled(true);
                
                // Check for lead rejection popup after successful booking
                const popupShown = await checkForOpenLeadsAndShowPopup(item);
                
                // Only continue if no popup was shown, otherwise wait for user action
                if (!popupShown) {
                    props.setRefreshLeadToRedux(true);
                }
            }
            else {
                if (response.Message === "") {
                    enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
                } else if (response.StatusCode == -3) {
                    enqueueSnackbar("Application No. already Exists on LeadId: " + response.Message, { variant: 'error', autoHideDuration: 3000, });
                }
                else {
                    enqueueSnackbar(response.Message, { variant: 'error', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
                }
            }
            if (response && response.Warnings) {
                enqueueSnackbar(response.Warnings, { variant: 'info', autoHideDuration: 3000, style: { whiteSpace: 'pre-line' } });
            }
        }, function (reason) {
            if (reason.Error == "") {
                enqueueSnackbar("Unable to Save.", { variant: 'error', autoHideDuration: 3000, });
            } else if (reason.ErrorCode == -3) {
                enqueueSnackbar("Application No. already Exists on LeadId: " + reason.Error, { variant: 'error', autoHideDuration: 3000, });
            } else {
                enqueueSnackbar(reason.Error, { variant: 'error', autoHideDuration: 5000, style: { whiteSpace: 'pre-line' } });
            }
        });
    };

    const checkForOpenLeadsAndShowPopup = async (item) => {
        // This method is only called for product 131
        try {
            setLoadingOpenLeads(true);
            const customerId = rootScopeService.getCustomerId();
            const subProductId = item.SubProduct?.ID;
            
            if (customerId && subProductId) {
                const openLeadsResponse = await GetCustomerOpenLeads({ CustomerId: customerId, SubProductId: subProductId });
                
                if (openLeadsResponse?.length > 0) {
                    // The API returns an array of lead objects, so filter based on the current lead being booked
                    const currentLeadId = item.leadId || item.LeadID;
                    const otherOpenLeads = openLeadsResponse.filter(lead => lead.LeadID !== currentLeadId);
                    
                    if (otherOpenLeads.length > 0) {
                        // Get current product name from booking lead
                        const currentProductName = options.SubProduct?.find(sp => sp.ID === item.SubProduct?.ID)?.Name || ''

                        // Map the API response to the component format
                        const leadObjects = otherOpenLeads.map(lead => ({
                            LeadId: lead.LeadID,
                            CreatedOn: lead.LastUpdatedOn,
                            ProductName: currentProductName,
                            StatusName: lead.LeadStatus,
                            CompanyName: lead.CompanyName
                        }));
                        
                        // Store booking completion data and show popup
                        setCustomerOpenLeads(leadObjects);
                        setCurrentBookingRequestData({ item }); // Store item for post-popup actions
                        setShowLeadRejectionPrompt(true);
                        setLoadingOpenLeads(false);
                        return true; // Indicates popup was shown
                    }
                }
            }
            setLoadingOpenLeads(false);
        } catch (error) {
            console.error('Error checking customer open leads:', error);
            setLoadingOpenLeads(false);
        }
        return false; // No popup shown
    };

    const ConfirmPayment = (reqData, item) => {
        item.IsConfirmPayment = true;
        item.CustomerId = rootScopeService.getCustomerId();
        const input = {
            url: "api/Bms/CreateBooking",
            method: 'POST',
            service: 'MatrixCoreAPI',
            requestData: item
        }
        return CALL_API(input).then(response => {
            return response;
        });
    };

    const handleLeadRejectionPromptClose = () => {
        setShowLeadRejectionPrompt(false);
    };

    const handleProceedWithBooking = () => {
        // Complete the booking flow after popup action (booking already completed, just finish the flow)
        props.setRefreshLeadToRedux(true);
        setCustomerOpenLeads([]);
        setCurrentBookingRequestData(null); // Store item for post-popup actions
        setShowLeadRejectionPrompt(false);
        setLoadingOpenLeads(false);
    };

    const planList = Array.isArray(options.plan) ? options.plan.filter(plan => {
        let valid = true;
        if (!newOrder.Supplier) return false;
        valid = valid && (plan.ProductId == productId);
        if ([131, 3, 154, 139].indexOf(productId) == -1) {
            if (newOrder.Supplier) {
                valid = valid && (plan.OldSupplierId == newOrder.Supplier.OldSupplierId);
            }
            if (newOrder.SubProduct)
                valid = (valid && (plan.SubProductTypeID == newOrder.SubProduct.ID));
        }
        else {
            if (newOrder.Supplier)
                valid = valid && (plan.SupplierId == newOrder.Supplier.SupplierId);
        }
        return valid;
    }) : [];

    const supplierList = Array.isArray(options.supplier) ? options.supplier.filter((supplier) => {
        let valid = true;
        valid = valid && (supplier.ProductId == productId || 0);
        if ([131, 3, 154, 139].indexOf(productId) === -1) {
            valid = (valid && (supplier.SubCategoryId == newOrder.SubProduct.ID));
        }
        if (productId === 2)
            valid = valid && supplier.IsSaleAllowed;
        return valid;
    }) : [];

    // if (options.activeLeadCollection.length <= 0) return null;
    const getPlanList = (item) => {
        return Array.isArray(options.plan) ? options.plan.filter(plan => {
            let valid = true;
            if (!item.Supplier) return false;
            valid = valid && (plan.ProductId == productId);
            if ([131, 3, 154].indexOf(productId) === -1) {
                if (item.Supplier) {
                    valid = valid && (plan.OldSupplierId == item.Supplier.OldSupplierId);
                }
                if (item.SubProduct)
                    valid = (valid && (plan.SubProductTypeID == (item.SubProduct.ID || 0)));
            }
            else {
                if (item.Supplier)
                    valid = valid && (plan.SupplierId == item.Supplier.SupplierId);
            }
            return valid;
        }) : [];
    }
    const getSupplierList = (item) => {
        return Array.isArray(options.supplier) ? options.supplier.filter((supplier) => {
            let valid = true;
            valid = valid && (supplier.ProductId == productId || 0);
            if ([131, 3, 154, 139].indexOf(productId) === -1 && item.SubProduct) {
                valid = (valid && (supplier.SubCategoryId == (item.SubProduct.ID || 0)));
            }
            return valid;
        }) : [];
    }

    const getOccupationsList = (item) => {
        try {
            let filteredArray = options.Occupations.filter((o) => o.SubProductTypeId == item.SubProduct.ID && o.ParentCategoryId == 0);
            return filteredArray;
        }
        catch { return []; }
    }

    const OptimizedplanList = Array.isArray(OptimzedPlansList) ? OptimzedPlansList.filter(plan => {
        let valid = true;
        if (!newOrder.Supplier) return false;
        valid = valid && (plan.ProductId == productId);
        if ([131, 3, 154, 139].indexOf(productId) == -1) {
            if (newOrder.SubProduct)
                valid = (valid && (plan.SubProductTypeID == newOrder.SubProduct.ID));
        } if (IsNotHealthRenewal && !plan.IsMatrix) {
            valid = false;
        }
        return valid;
    }) : [];

    const BindIfSinglePlan = (SinglePlan, item, index) => {
        try {
            if (SinglePlan) {
                if (index === -1) {
                    setNewOrder(prevState => ({ ...prevState, Plan: SinglePlan }));
                    setInstallment({ ...newOrder, "Plan": SinglePlan }, -1);
                    OnPlanChange({ ...newOrder, "Plan": SinglePlan }, -1);
                }
                else {
                    handleChangeForBooking({ target: { name: 'Plan', value: SinglePlan, index: index } }, item, index);
                }
            }
        }
        catch (e) {
            console.log(e);
        }
    };

    const getPlanListSupplierWise = (item) => {
        try {

            let SupplierId = item.Supplier.OldSupplierId;
            //SupplierId = ([131, 3, 154].indexOf(productId) === -1) ? item.Supplier.OldSupplierId : item.Supplier.SupplierId;
            if (SupplierId) {
                return masterService.GetProductPlansFromCore(rootScopeService.getProductId(), SupplierId, "Booking").then(res => {
                    if (Array.isArray(res)) {
                        if (productId == 131) {
                            let result = [];
                            let subProductId = 0;
                            if (item.SubProduct) {
                                subProductId = item.SubProduct.ID;
                            }
                            else {
                                subProductId = item.ProductTypeId;
                            }
                            res.forEach((plan) => {
                                if (plan.SubProductId == subProductId) {
                                    result.push(plan);
                                }
                            });
                            res = result;
                        }

                        setOptimzedPlansListforBookedLeads(res);
                        return res;
                    }
                });
            }
            else {
                return Promise.resolve([]);
            }
        }
        catch {
            return Promise.resolve([]);
        }
    };

    const getOptimizedPlanList = (item) => {
        return Array.isArray(OptimzedPlansListforBookedLeads) && item ? OptimzedPlansListforBookedLeads.filter(plan => {
            let valid = true;
            if (!item.Supplier) return false;
            valid = valid && (plan.ProductId == productId);
            if ([131, 3, 154].indexOf(productId) === -1) {
                if (item.SubProduct) {
                    valid = (valid && (plan.SubProductTypeID == (item.SubProduct.ID || 0)));
                }
                if (IsNotHealthRenewal && !plan.IsMatrix) {
                    valid = false;
                }
            }
            return valid;
        }) : [];
    }

    const GetShopTypes = () => {
        try {
            if (Array.isArray(ShopTypes) && ShopTypes.length > 0) {
                // Do nothing
            }
            else {
                masterService.GetShopTypes().then(function (shopTypes) {
                    setShopTypes(shopTypes);
                });
            }
        }
        catch {
            return [];
        }
    }

    const GetDoctorAssociations = () => {
        try {
            masterService.GetDoctorAssociations().then(function (association) {
                if (association)
                    setAssociation(association);
            });
        }
        catch {
            return [];
        }
    }

    const GetSmeRenewalLeadBookingDetails = (leadId) => {
        try {
            if (leadId) {
                masterService.GetSmeRenewalLeadBookingDetails(leadId).then(function (response) {
                    if (response && Object.keys(response).length > 0) {
                        // Store original renewal API response for validation bypass
                        setNewOrderOriginal(response);
                        if (SupplierBySubProduct && response.Supplier && response.Supplier.OldSupplierId && response.SubProduct && response.SubProduct.ID
                            && response.Plan && response.Plan.OldPlanId) {
                            let itemSupplier = SupplierBySubProduct.find((e) => e.SupplierId == response.Supplier.OldSupplierId && e.SubProductId == response.SubProduct.ID);
                            setNewOrder(prevState => ({ ...prevState, "Supplier": itemSupplier }));
                            masterService.GetProductPlansFromCore(rootScopeService.getProductId(), response.Supplier.OldSupplierId, "Booking").then(res => {
                                if (Array.isArray(res)) {
                                    if (productId == 131) {
                                        let result = [];
                                        let subProductId = response.SubProduct.ID;
                                        res.forEach((plan) => {
                                            if (plan.SubProductId == subProductId) {
                                                result.push(plan);
                                            }
                                        });
                                        res = result;
                                    }

                                }
                                setOptimzedPlansList(res);
                                let plans = res.filter((p) => p.OldPlanId == response.Plan.OldPlanId)[0];
                                setNewOrder(prevState => ({ ...prevState, "Plan": plans }));
                                if (plans) {
                                    OnPlanChange({ ...newOrder, "Plan": plans }, -1);
                                }
                            })
                        }
                        if (response.SumInsuredType && response.SumInsuredType.Name) {
                            response.SumInsuredType = SumInsuredTypeOptions.find((i) => i.Name == response.SumInsuredType.Name);
                        }
                        if (response.FamilyType && response.FamilyType.Name) {
                            response.FamilyType = FamilyTypes.find((i) => i.Name == response.FamilyType.Name);
                        }
                        if (response.TransitFromCityId && response.TransitFromCityId > 0) {
                            response.TransitFromCityId = options.cities.filter((sp) => sp.CityID == response.TransitFromCityId)[0];
                        }
                        if (response.TransitToCityId && response.TransitToCityId > 0) {
                            response.TransitToCityId = options.cities.filter((sp) => sp.CityID == response.TransitToCityId)[0];
                        }
                        if (response.OccupancyId && response.SubProduct && response.SubProduct.ID) {
                            response.Occupation = options.Occupations.find(o => o.ID == response.OccupancyId && o.SubProductTypeId == response.SubProduct.ID);
                        }
                        if (response.PropertyTypeId) {
                            response.PropertyTypeId = options.SmePropertyTypes.find((type) => type.ID === response.PropertyTypeId)
                        }
                        if (response.Inclusion && response.Inclusion.length > 0) {
                            let inclusions = [];
                            response.Inclusion.forEach(element => {
                                inclusions.push(Inclusions.filter((sp) => sp.Id == element)[0]);
                            });
                            response.Inclusion = inclusions;
                        }

                        if (response.MedicalExtension) {
                            if (MedicalExtensions.findIndex((e) => e.Id == response.MedicalExtension) === -1) {
                                response.OtherMedicalExtension = response.MedicalExtension;
                                setNewOrder(prevState => ({ ...prevState, "OtherMedicalExtension": response.MedicalExtension }));
                                response.MedicalExtension = "Other";
                            }
                        }
                        if (response.ChildOccupancies && response.SubProduct && response.SubProduct.ID && response.SubProduct.ID == 19) {
                            var ids = response.ChildOccupancies.split(',');
                            const childOccArray = options.Occupations.filter(item =>
                                ids.some(id => item.ID == id) &&
                                parseInt(item.SubProductTypeId) === response.SubProduct.ID
                            );
                            response.ChildOccupancies = childOccArray;
                        }
                        else {
                            if (response.ChildOccupancyId && response.ChildOccupancyId > 0) {
                                response.ChildOccupancyId = options.Occupations.find(o => o.ID == response.ChildOccupancyId && o.SubProductTypeId == response.SubProduct.ID);

                            }
                        }
                        if (response.SubProduct && response.SubProduct.ID === 14 && response.AssociationId && Association) {
                            response.Association = Association.find((a) => a.Id === response.AssociationId)
                        }
                        if (response.PolicyCategory && response.PolicyCategory != '') {
                            response.PolicyCategory = Data.PolicyCategory.find((i) => i.Id == response.PolicyCategory);
                        }
                        if (response.BookingCategory && response.BookingCategory != '') {
                            response.BookingCategory = Data.BookingCategory.find((i) => i.Id == response.BookingCategory);
                        }
                        if (response.PlanType && response.PlanType != '') {
                            response.PlanType = Data.PlanType.find((i) => i.Id == response.PlanType);
                        }
                        if (response.ProjectDuration !== null && response.ProjectDuration !== undefined) {
                            response.ProjectDuration = response.ProjectDuration;
                        }
                        if (response.WorkerTypes && response.WorkerTypes.length > 0) {
                            let workerTypes = [];
                            response.WorkerTypes.forEach(e => {
                                workerTypes.push(WorkerTypes.filter((sp) => sp.Name == e.WorkerType)[0]);
                                if (e.WorkerType === "Skilled") {
                                    setNewOrder(prevState => ({ ...prevState, "NoOfWorkerSkilled": e.NoOfWorker, "SalaryOfWorkerSkilled": e.Salary }));
                                }
                                else if (e.WorkerType === "Semi-Skilled") {
                                    setNewOrder(prevState => ({ ...prevState, "NoOfWorkerSemiSkilled": e.NoOfWorker, "SalaryOfWorkerSemiSkilled": e.Salary }));
                                }
                                else if (e.WorkerType === "Un-Skilled") {
                                    setNewOrder(prevState => ({ ...prevState, "NoOfWorkerUnSkilled": e.NoOfWorker, "SalaryOfWorkerUnSkilled": e.Salary }));
                                }
                                else if (e.WorkerType === "Other") {
                                    setNewOrder(prevState => ({ ...prevState, "NoOfWorkerOther": e.NoOfWorker, "SalaryOfWorkerOther": e.Salary }));
                                }
                            });

                            response.WorkerTypes = workerTypes;
                        }
                        if (response.RiskLocations && response.RiskLocations.length > 0) {
                            let riskLocation = [];
                            response.RiskLocations.forEach(e => {
                                let city = null;
                                if (e.CityId) {
                                    city = options.cities.filter((sp) => sp.CityID == e.CityId)[0];
                                }
                                riskLocation.push({ RiskAddress: e.RiskAddress, City: city, PinCode: e.PinCode });
                            });
                            response.RiskLocations = riskLocation;
                        }
                        if (response.BuildingSI || response.ContentSI || response.StockSI) {
                            response.InsuredScope = [];
                            if (response.BuildingSI && response.BuildingSI > 0) {
                                response.BuildingValue = response.BuildingSI;
                                response.InsuredScope.push(Data.InsuredScopes[0]);
                            }
                            if (response.ContentSI && response.ContentSI > 0) {
                                response.ContentValue = response.ContentSI;
                                response.InsuredScope.push(Data.InsuredScopes[1]);
                            }
                            if (response.StockSI && response.StockSI > 0) {
                                response.StockValue = response.StockSI;
                                response.InsuredScope.push(Data.InsuredScopes[2]);
                            }

                        }
                        if (response.TotalNoOfEmployees || response.TotalNoOfLives) {
                            response.NoOfLives = response.TotalNoOfLives;
                            response.NoOfEmployees = response.TotalNoOfEmployees;
                            response.SMERenewal = true;
                        }

                        if (response.CityState) {
                            var obj = {
                                "CityID": response.CityState.CityId || '',
                                "CityName": response.CityState.city || '',
                                "StateID": response.CityState.StateId || '',
                                "StateName": response.CityState.State || '',
                                "CityStateName": (response.CityState.city || '') + "(" + (response.CityState.State || '') + ")"
                            };

                            response.CityState = obj;
                        }
                        let subProduct = null
                        if (response.SubProduct && response.SubProduct.ID) {
                            subProduct = options.SubProduct.find((sub) => (sub.ID == response.SubProduct.ID));
                            OnSubProductChange({ ...response, "SubProduct": subProduct }, -1, false);
                        }
                        onOccupationChange(response, -1, false);
                        // Handle ShipmentType conversion based on Transit Type
                        let shipmentTypeValue;
                        if (response.ShipmentType) {
                            if (Array.isArray(response.ShipmentType)) {
                                // Already in correct format
                                shipmentTypeValue = response.ShipmentType;
                            } else {
                                // Check Transit Type to determine correct format
                                if (response.TransitType === "Annual Open") {
                                    // Convert to array format for multi-select
                                    let shipmentTypeArray = [];
                                    const shipmentTypeValues = response.ShipmentType.split(',').map(val => val.trim());
                                    shipmentTypeValues.forEach(value => {
                                        const shipmentType = ShipmentTypes.find(st => st.Id === value || st.Name === value);
                                        if (shipmentType) {
                                            shipmentTypeArray.push(shipmentType);
                                        }
                                    });
                                    shipmentTypeValue = shipmentTypeArray;
                                } else {
                                    // Keep as ID string for single-select dropdown
                                    const shipmentType = ShipmentTypes.find(st => st.Id === response.ShipmentType || st.Name === response.ShipmentType);
                                    shipmentTypeValue = shipmentType ? shipmentType.Id : '';
                                }
                            }
                        } else {
                            // Set default based on Transit Type
                            shipmentTypeValue = response.TransitType === "Annual Open" ? [] : '';
                        }

                        setNewOrder(prevState => ({
                            ...prevState, "SumInsured": response.SumInsured, "TotalPremium": response.TotalPremium, "NoOfEmployees": response.TotalNoOfEmployees,
                            "NoOfLives": response.TotalNoOfLives, "InsuredName": response.InsuredName, "TypeOfPolicy": response.TypeOfPolicy, "Occupation": response.Occupation,
                            "CompanyName": response.CompanyName, "TransitType": response.TransitType, "Name": response.Name,
                            "PrevPolicyNo": response.PrevPolicyNo, "PreviousBookingNo": response.PreviousBookingNo, "SumInsuredType": response.SumInsuredType,
                            "FamilyType": response.FamilyType, "ExpiringInsurer": response.ExpiringInsurer, "ShipmentType": shipmentTypeValue,"OtherOccupany": response.OtherOccupany,
                            "ProjectDuration": response.ProjectDuration || '', "BookingCategory": response.BookingCategory || '', "PlanType": response.PlanType || '',
                            "ShopTypeId": response.ShopTypeId, "TransitFrom": response.TransitFromCityId, "TransitTo": response.TransitToCityId, "OccupationType": response.OccupationType,
                            "ManufacturerTraderName": response.ManufacturerTraderName, "ManufacturerTraderContactNo": response.ManufacturerTraderContactNo,
                            "ConstitutionOfBusiness": response.ConstitutionOfBusiness, "MarineCoverType": response.MarineCoverType, "MedicalExtension": response.MedicalExtension,
                            "PropertyType": response.PropertyTypeId, "Inclusion": response.Inclusion, "ChildOccupation": response.ChildOccupancyId,
                            "ChildOccupancies": response.ChildOccupancies, "Association": response.Association, "PolicyCategory": response.PolicyCategory, "CityState": response.CityState,
                            "ApplicationNo": response.ApplicationNo, "RiderSI": response.RiderSI, "Rider": response.Rider, "TermTenure": response.TermTenure, "TermSI": response.TermSI,
                            "WorkerType": response.WorkerTypes, "RiskLocations": response.RiskLocations, "InsuredScope": response.InsuredScope, "BuildingValue": response.BuildingValue,
                            "ContentValue": response.ContentValue, "StockValue": response.StockValue,"InlandSI": response.InlandSI || '', "ImportSI": response.ImportSI || '', 
                            "ExportSI": response.ExportSI || '', "MerchantTradingSI": response.MerchantTradingSI || '',
                        }));



                    }
                    else {
                        setSmeRenewalNewOrder();
                    }
                });
            }
        }
        catch (e) {
            console.log(e);
        }

    }
    const setSmeRenewalNewOrder = () => {
        setNewOrder(prevState => ({
            ...prevState, "SumInsured": '', "TotalPremium": '', "NoOfEmployees": '', "NoOfLives": '',
            "InsuredName": '', "TypeOfPolicy": '', "CompanyName": '', "TransitType": '', "Name": '', "PrevPolicyNo": '',
            "PreviousBookingNo": '', "SumInsuredType": { Id: 0 }, "FamilyType": { Id: 0 }, "ExpiringInsurer": 0, "ShipmentType": '', "OtherOccupany": '',
            "ShopTypeId": 0, "TransitFrom": 0, "TransitTo": 0, "OccupationType": '', "ManufacturerTraderName": '', "ManufacturerTraderContactNo": '',
            "ConstitutionOfBusiness": '', "MarineCoverType": 0, "MedicalExtension": '', "Occupation": '', "PropertyType": 0, "Inclusion": '', "ChildOccupation": '',
            "ChildOccupancies": null, "Association": { Id: 0 }, "PolicyCategory": { Id: '' }, "CityState": {
                CityStateName: ''
            }, "RiderSI": 0, "Rider": '', "TermTenure": 0, "TermSI": 0, "WorkerType": null, "RiskLocations": null, "InsuredScope": '',
            "BuildingValue": '', "ContentValue": '', "StockValue": '', "SIPerPerson": ''
        }));
    }
    const FetchSmeRenewal = (ActiveLead) => {
        if (ActiveLead) {
            if (allLeads && allLeads.find((e) => e.LeadID == ActiveLead)?.LeadSource == 'Renewal') {
                GetSmeRenewalLeadBookingDetails(ActiveLead);
            }
            else {
                setSmeRenewalNewOrder();
            }
        }
    }
    return <>
        <Grid item sm={12} md={12} xs={12}>
            {DocsUploadPanel && <UploadDocuments {...refUploadFiles.current} />}
            {Array.isArray(options.activeLeadCollection) && options.activeLeadCollection.length > 0 &&
                <div name='CreateBooking' className="createbooking-section"> <h3>Create Booking</h3>
                    <div className="expandmoreIcon"><ExpandMore onClick={handleToggle}
                        style={{ transform: ShowCreateBookingSection ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                    <span className="caption">Sell another plan to customer</span>
                    <Grid container spacing={3}>
                        <SelectDropdown
                            name="ActiveLead"
                            label="Select Lead"
                            value={newOrder.ActiveLead}
                            options={options.activeLeadCollection}
                            labelKeyInOptions='leadId'
                            valueKeyInOptions='_all'
                            handleChange={handleChange}
                            show={ShowCreateBookingSection}
                            sm={6} md={4} xs={12}
                            disabled={isDisabled}
                        />
                    </Grid>


                    {ShowCreateBookingSection && <>
                        <Grid container spacing={3}>
                            <Grid item sm={12} md={12} xs={12}>
                                <hr />
                            </Grid>
                            <SelectDropdown
                                name="SubProduct"
                                label="Product Type"
                                value={newOrder.SubProduct}
                                handleChange={handleChange}
                                options={options.SubProduct}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                sm={6} md={4} xs={12}
                                show={!!Visible.ProductType}
                                disabled={isDisabled || (productId == 131 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                            />
                            <Grid item sm={6} md={4} xs={12}>
                                <Autocomplete
                                    onChange={(event, value) => handleChange({ target: { name: 'Supplier', value } })}
                                    id="Supplier"
                                    options={(productId == 131 && IsShowSupplierBySubProduct && SupplierBySubProduct) ? SupplierBySubProduct : supplierList}
                                    name="Supplier"
                                    value={newOrder.Supplier}
                                    getOptionLabel={(option) => (option.SupplierDisplayName || "")}
                                    disabled={isDisabled}
                                    show={true}
                                    renderInput={(params) =>
                                        <TextField {...params}
                                            label={IsCoInsurer(newOrder) ? "Leader Supplier" : "Supplier"}
                                            variant='outlined'
                                        />}
                                />
                            </Grid>
                            <SelectDropdown
                                name="Plan"
                                label="Plan"
                                value={newOrder.Plan}
                                options={OptimizedplanList}
                                labelKeyInOptions="PlanName"
                                valueKeyInOptions="_all"
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={true}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="PolicyType"
                                label={productId === 117 ? "Purchase type" : "Policy Type"}
                                value={newOrder.PolicyType}
                                options={options.policyType}
                                labelKeyInOptions="PolicyTypeName"
                                valueKeyInOptions="_all"
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={!!Visible.PolicyType}
                                disabled={isDisabled || (productId === 131 && IsDisabledForSmeElements)}
                            />
                            <SelectDropdown
                                name="SumInsuredType"
                                label="Sum Insured Type"
                                value={newOrder.SumInsuredType}
                                handleChange={handleChange}
                                options={SumInsuredTypeOptions}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                sm={6} md={4} xs={12}
                                show={!!(productId == 131 && newOrder.SubProduct && SmeGhiSubProducts.indexOf(newOrder.SubProduct.ID) > -1)}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="FamilyType"
                                label="Family Type"
                                value={newOrder.FamilyType}
                                handleChange={handleChange}
                                options={FamilyTypes}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                sm={6} md={4} xs={12}
                                show={!!(productId == 131 && newOrder.SubProduct && SmeGhiSubProducts.indexOf(newOrder.SubProduct.ID) > -1)}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="SIPerPerson"
                                label="Sum Insured per person"
                                value={newOrder.SIPerPerson}
                                handleChange={handleChange}
                                show={!!SIPerPerson}
                                maxLength={7}
                                disabled={isDisabled}
                            />
                            <Tooltip title={GetNumLabel(newOrder.SumInsured)}>
                                <TextInput
                                    name="SumInsured"
                                    label={productId === 117 ? "IDV" : "Sum Insured"}
                                    value={newOrder.SumInsured}
                                    handleChange={handleChange}
                                    show={!!Visible.SumInsured}
                                    maxLength={12}
                                    disabled={isDisabled || SIPerPerson || IsSIdisabled || IsTPChecked || productId === 101}
                                />
                            </Tooltip>
                            <Tooltip title={GetNumLabel(newOrder.TotalPremium)}>
                                <TextInput
                                    name="TotalPremium"
                                    label={"Premium"}
                                    value={newOrder.TotalPremium}
                                    handleChange={handleChange}
                                    show={!!Visible.Premium}
                                    maxLength={9}
                                    disabled={isDisabled}
                                />
                            </Tooltip>
                            <TextInput
                                name="PaidPremium"
                                label="Paid Premium"
                                value={newOrder.PaidPremium}
                                handleChange={handleChange}
                                show={productId == 148}
                                disabled={isDisabled}
                            />

                            {!!shouldShowPremiumFields(newOrder) && (
                                <>
                                    <SelectDropdown
                                        name="TerrorismPremiumVal"
                                        label="Is Terrorism Premium Included"
                                        value={newOrder.TerrorismPremiumVal || null}
                                        handleChange={handleChange}
                                        options={YesNo}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="_all"
                                        sm={6} md={4} xs={12}
                                        show={true}
                                        disabled={isDisabled}
                                    />
                                    {newOrder.TerrorismPremiumVal?.Id === 1 && (
                                        <Tooltip title={GetNumLabel(newOrder.TerrorismPremium)}>
                                            <TextInput
                                                name="TerrorismPremium"
                                                label="Terrorism Premium"
                                                value={(newOrder.TerrorismPremium && newOrder.TerrorismPremium > 0) ? newOrder.TerrorismPremium : null}
                                                handleChange={handleChange}
                                                disabled={isDisabled || IsTerrorismPremiumDisabled(newOrder)}
                                            />
                                        </Tooltip>
                                    )}

                                    <SelectDropdown
                                        name="BurglaryPremiumVal"
                                        label="Is Burglary Premium Included"
                                        value={newOrder.BurglaryPremiumVal || null}
                                        handleChange={handleChange}
                                        options={YesNo}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="_all"
                                        sm={6} md={4} xs={12}
                                        disabled={isDisabled}
                                    />
                                    {newOrder.BurglaryPremiumVal?.Id === 1 && (
                                        <Tooltip title={GetNumLabel(newOrder.BurglaryPremium)}>
                                            <TextInput
                                                name="BurglaryPremium"
                                                label="Burglary Premium"
                                                value={(newOrder.BurglaryPremium && newOrder.BurglaryPremium > 0) ? newOrder.BurglaryPremium : null}
                                                handleChange={handleChange}
                                                disabled={isDisabled}
                                            />
                                        </Tooltip>
                                    )}
                                    {((newOrder.BurglaryPremiumVal?.Id === 1) || (newOrder.TerrorismPremiumVal?.Id === 1)) && (
                                        <Tooltip title={GetNumLabel(newOrder.FirePremium)}>
                                            <TextInput
                                                name="FirePremium"
                                                label="Fire Premium"
                                                value={(newOrder.FirePremium && newOrder.FirePremium > 0) ? newOrder.FirePremium : null}
                                                handleChange={handleChange}
                                                disabled={true}
                                            />
                                        </Tooltip>
                                    )}
                                </>
                            )}


                            <SelectDropdown
                                name="LD"
                                label="Loading/Discounting"
                                value={newOrder.LD}
                                handleChange={handleChange}
                                options={LDTypes}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                sm={6} md={4} xs={12}
                                show={productId === 131}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="LDAmount"
                                label={(newOrder.LD && newOrder.LD.Id === 1) ? "Loading Amount" : "Discounting Amount"}
                                value={newOrder.LDAmount}
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.LD && newOrder.LD.Id > 0}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="PrevPolicyNo"
                                label={productId == 131 ? "Last Year Policy Number" : "Previous Policy No"}
                                value={newOrder.PrevPolicyNo}
                                handleChange={handleChange}
                                show={(productId == 2 || (productId === 131 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1 || newOrder.PolicyType.PolicyTypeId == 2)) || (productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1)))}
                                disabled={isDisabled || (productId === 131 && (IsDisabledExpiringPolicyNo || IsDisabledForSmeElements))}
                                maxLength={150}
                            />
                            <TextInput
                                name="PreviousBookingNo"
                                label={productId == 131 ? "Last Year Booking ID" : "Previous BookingNo."}
                                value={newOrder.PreviousBookingNo}
                                handleChange={handleChange}
                                show={((!!Visible.PreviousBookingNo) && (newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1 || newOrder.PolicyType.PolicyTypeId == 2)))
                                    || (productId === 131 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                disabled={isDisabled || (productId === 131 && (IsDisabledExpiringBookingId || IsDisabledForSmeElements))}
                                maxLength={50}
                            />
                            <SelectDropdown
                                name="ExpiringInsurer"
                                label="Last Year Insurer"
                                value={newOrder.ExpiringInsurer}
                                options={SupplierBySubProduct ? SupplierBySubProduct : supplierList}
                                labelKeyInOptions="SupplierDisplayName"
                                valueKeyInOptions='OldSupplierId'
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={(productId === 131 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 2)}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="InsuredName"
                                label={productId === 131 ? "Insured Name (Name on Policy)" : "Insured Name"}
                                value={newOrder.InsuredName}
                                handleChange={handleChange}
                                show={!!Visible.InsuredName || productId === 131 || (productId === 2 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 0)}
                                maxLength={50}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="Name"
                                label="Contact Person's Name"
                                value={newOrder.Name}
                                handleChange={handleChange}
                                show={productId === 131}
                                maxLength={50}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="Brokerage"
                                label="% Brokerage"
                                value={newOrder.Brokerage}
                                handleChange={handleChange}
                                show={!!Visible.Brokerage && IsFosSmeAgentForBrokerage}
                                maxLength={6}
                                disabled={true}
                            />
                            <SelectDropdown
                                name="CoverType"
                                label="Cover Type"
                                value={newOrder.CoverType}
                                options={options.CoverTypes}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={!!Visible.CoverType}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="BuildingSI"
                                label="Structure SI"
                                value={newOrder.BuildingSI}
                                handleChange={handleChange}
                                show={!!Visible.BuildingSI}
                                maxLength={10}
                                disabled={(isDisabled || IsdisabledBuildingSI)}
                            />
                            <TextInput
                                name="ContentSI"
                                label="Content SI"
                                value={newOrder.ContentSI}
                                handleChange={handleChange}
                                show={!!Visible.ContentSI}
                                maxLength={10}
                                disabled={(isDisabled || IsdisabledContentSI)}
                            />
                            <SelectDropdown
                                name="PropertyType"
                                label="Property Type"
                                value={newOrder.PropertyType}
                                options={IsSmePropertyProduct(productId, newOrder) ? options.SmePropertyTypes : options.PropertyTypes}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={!!Visible.PropertyType || IsSmePropertyProduct(productId, newOrder)}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="PropertyPurpose"
                                label="Purpose"
                                value={newOrder.PropertyPurpose}
                                options={options.PropertyPurpose}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                handleChange={handleChange}
                                sm={6} md={4} xs={12}
                                show={!!Visible.PropertyType}
                                disabled={isDisabled}
                            />

                            <SelectDropdown
                                name="BookingType"
                                label="Booking Type"
                                value={newOrder.BookingType}
                                options={options.BookingTypes}
                                labelKeyInOptions='label'
                                valueKeyInOptions='value'
                                handleChange={handleChange}
                                show={!!Visible.BookingType}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="TermTenure"
                                label="Term Tenure"
                                value={newOrder.TermTenure}
                                handleChange={handleChange}
                                show={!!Visible.TermTenure && newOrder.Plan && newOrder.Plan.PlanId == 3846}
                                maxLength={9}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="TermSI"
                                label="Term SI"
                                value={newOrder.TermSI}
                                handleChange={handleChange}
                                show={!!Visible.TermSI && newOrder.Plan && newOrder.Plan.PlanId == 3846}
                                maxLength={9}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="CompanyName"
                                label={productId === 131 ? "Company Name (Name on PAN)" : "Company Name"}
                                value={newOrder.CompanyName}
                                handleChange={handleChange}
                                show={!!Visible.CompanyName || productId === 131}
                                maxLength={200}
                                disabled={isDisabled}
                            />
                            {Visible.City &&
                                <Grid item sm={6} md={4} xs={12} >
                                    <Autocomplete
                                        name="CityState"
                                        label="City"
                                        value={newOrder.CityState || null}
                                        onChange={(event, value) => handleChange({ target: { name: 'CityState', value } })}
                                        options={options.cities}
                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                        renderInput={(params) => <TextField name="city" label="City" variant="outlined" {...params} />}
                                        disabled={isDisabled}
                                        fullWidth
                                    />
                                </Grid>
                            }
                            <SelectDropdown
                                name="PolicyCategory"
                                label="Policy Category"
                                value={newOrder.PolicyCategory || null}
                                handleChange={handleChange}
                                options={Data.PolicyCategory}
                                labelKeyInOptions="Name"
                                valueKeyInOptions="_all"
                                sm={6} md={4} xs={12}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID == 19}
                                disabled={isDisabled}
                            />

                            <TextInput
                                name="ApplicationNo"
                                label="Application number"
                                value={newOrder.ApplicationNo}
                                handleChange={handleChange}
                                show={productId == 7}
                                maxLength={50}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="ODPremium"
                                label="ODPremium"
                                value={newOrder.ODPremium}
                                handleChange={handleChange}
                                show={!!Visible.ODPremium}
                                maxLength={16}
                                disabled={isDisabled || IsTPChecked}
                            />

                            {Visible.InspectionRequired && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1) &&
                                <Grid item sm={12} md={12} xs={12}>
                                    {[<p className="emiTxt" key="100" >Inspection Required</p>,
                                    ...(inspectionRequired.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.Medical_or_InspectionRequired == values.Id}
                                            name="Medical_or_InspectionRequired"
                                            disabled={isDisabled}
                                        />
                                    )))]}
                                </Grid>
                            }


                            {Visible.IsRSA &&
                                <Grid item sm={12} md={12} xs={12}>
                                    {[<p className="emiTxt" key="100">Is RSA Required</p>,
                                    ...isRSA.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.IsRSA == values.Id}
                                            name="IsRSA"
                                            disabled={isDisabled}
                                        />
                                    ))]}
                                </Grid>
                            }
                            {productId === 117 && newOrder.LeadSource && newOrder.LeadSource === "Renewal" &&
                                <Grid item sm={12} md={12} xs={12}>
                                    {[<p className="emiTxt" key="100">IsTP</p>,
                                    ...isTP.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.IsTP == values.Id}
                                            name="IsTP"
                                            disabled={isDisabled}
                                        />
                                    ))]}
                                </Grid>
                            }
                            {Visible.DocRequired &&
                                <Grid item sm={12} md={4} xs={12}>
                                    {[<p className="emiTxt" key="101">Is DocRequired</p>,
                                    ...isRSA.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.DocumentsRequired == values.Id}
                                            name="DocumentsRequired"
                                            disabled={isDisabled}
                                        />
                                    ))]}
                                </Grid>
                            }
                            {Visible.DocRecieved &&
                                <Grid item sm={12} md={4} xs={12}>
                                    {[<p className="emiTxt" key="101">Is DocRecieved</p>,
                                    ...isRSA.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.IsDocReceived == values.Id}
                                            name="IsDocReceived"
                                            disabled={isDisabled}
                                        />
                                    ))]}
                                </Grid>
                            }
                            {Visible.Portability &&
                                <Grid item sm={12} md={4} xs={12}>
                                    {[<p className="emiTxt" key="101">Is Portability Case</p>,
                                    <FormControlLabel
                                        key={102}
                                        value={!!newOrder.Portability}
                                        control={<Checkbox color="secondary" />}
                                        label={''}
                                        color="secondary"
                                        onChange={handleChange}
                                        checked={!!newOrder.Portability}
                                        name="Portability"
                                        disabled={isDisabled}
                                    />
                                    ]}
                                </Grid>
                            }
                            <TextInput
                                name="NoOfLives"
                                label="No Of Lives"
                                value={newOrder.NoOfLives}
                                handleChange={handleChange}
                                show={!!Visible.NoOfLives}
                                maxLength={8}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="NoOfEmployees"
                                label="No Of Employees"
                                value={newOrder.NoOfEmployees}
                                handleChange={handleChange}
                                show={!!Visible.NoOfEmployee}
                                maxLength={8}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="RegistrationNo"
                                label="RegistrationNo"
                                value={newOrder.RegistrationNo}
                                handleChange={handleChange}
                                show={!!Visible.RegistrationNo}
                                maxLength="20"
                                disabled={isDisabled}
                            />
                            {!!Visible.Occupany &&
                                <Grid item sm={6} md={4} xs={12}>
                                    <Autocomplete
                                        onChange={(event, value) => handleChange({ target: { name: 'Occupation', value } })}
                                        id="Occupation"
                                        options={getOccupationsList(newOrder)}
                                        name="Occupation"
                                        value={newOrder.Occupation}
                                        getOptionLabel={(option) => (option.Name || '')}
                                        disabled={isDisabled}
                                        forcePopupIcon={true}
                                        renderInput={(params) =>
                                            <TextField {...params}
                                                label={"Parent risk category"}
                                                variant='outlined'
                                            />}
                                    />
                                </Grid>
                            }
                            {(newOrder.Occupation && ChildOccupancyList && ChildOccupancyList.length > 0 &&
                                newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID != 19) &&
                                <Grid item sm={6} md={4} xs={12}>
                                    <Autocomplete
                                        onChange={(event, value) => handleChange({ target: { name: 'ChildOccupation', value } })}
                                        id="ChildOccupation"
                                        options={ChildOccupancyList}
                                        name="ChildOccupation"
                                        value={newOrder.ChildOccupation || null}
                                        getOptionLabel={(option) => (option.Name || '')}
                                        disabled={isDisabled}
                                        forcePopupIcon={true}
                                        renderInput={(params) =>
                                            <TextField {...params}
                                                label={"Child risk category"}
                                                variant='outlined'
                                            />}
                                    />
                                </Grid>
                            }
                            {(newOrder.Occupation && ChildOccupancyList && ChildOccupancyList.length > 0 &&
                                newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID == 19) &&
                                <Grid item sm={6} md={4} xs={12}>
                                    <Autocomplete
                                        multiple
                                        onChange={(event, value) => handleChange({ target: { name: 'ChildOccupancies', value } })}
                                        id="checkboxes-tags-demo"
                                        limitTags={1}
                                        disableCloseOnSelect
                                        size="small"
                                        options={ChildOccupancyList}
                                        value={newOrder.ChildOccupancies || []}
                                        name="ChildOccupancies"
                                        getOptionLabel={(option) => (option.Name || '')}
                                        disabled={isDisabled}
                                        forcePopupIcon={true}
                                        renderOption={(props, option, { selected }) => {
                                            const { key, ...optionProps } = props;
                                            return (
                                                <li key={key} {...optionProps} className="AutoCompleteData">                                                    <Checkbox
                                                    icon={icon}
                                                    checkedIcon={checkedIcon}
                                                    style={{ marginRight: 8 }}
                                                    checked={selected}
                                                />
                                                    {option.Name}
                                                </li>
                                            );
                                        }}
                                        renderInput={(params) =>
                                            <TextField {...params}
                                                label={"Child risk category"}
                                                variant="outlined"
                                                className="AutoComplete"
                                            />}

                                    />
                                </Grid>
                            }
                            <TextInput
                                name="OtherOccupany"
                                label="Other Occupany"
                                value={newOrder.OtherOccupany}
                                handleChange={handleChange}
                                show={!!Visible.OtherOccupany}
                                maxLength="50"
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="PolicyTerm"
                                label={
                                    productId === 101
                                        ? "Policy Tenure (In Years)"
                                        : ("Policy Term" + (productId === 131 ? "(In month)" : ""))
                                }
                                value={newOrder.PolicyTerm}
                                handleChange={handleChange}
                                show={!!Visible.PolicyTerm || !!Visible.Tenure}
                                maxLength={[131, 101].productId ? 3 : 2}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="PayTerm"
                                label={productId === 131 ? "Installments" : "Pay Term"}
                                value={newOrder.PayTerm}
                                handleChange={handleChange}
                                show={!!Visible.PayTerm}
                                maxLength={2}
                                disabled={isDisabled}
                            />
                            <DatePicker
                                name="PolicyStartDate"
                                label="Policy StartDate"
                                value={newOrder.PolicyStartDate}
                                handleChange={handleChange}
                                show={!!Visible.PolicyStartDate}
                                disabled={isDisabled}
                            />
                            <DatePicker
                                name="PolicyEndDate"
                                label="Policy EndDate"
                                value={newOrder.PolicyEndDate}
                                handleChange={handleChange}
                                show={!!Visible.PolicyEndDate}
                                disabled={isDisabled || IsdisabledPED}
                            />
                            {IsSmeEngineeringSubproduct(newOrder) &&
                                <>
                                    <TextInput
                                        name="ProjectDuration"
                                        label="Project Duration (in Months)"
                                        value={newOrder.ProjectDuration || null}
                                        handleChange={handleChange}
                                        show={true}
                                        disabled={isDisabled}
                                    />
                                    <SelectDropdown
                                        name="BookingCategory"
                                        label="Booking Category"
                                        value={newOrder.BookingCategory || null}
                                        handleChange={handleChange}
                                        options={Data.BookingCategory}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="_all"
                                        sm={6} md={4} xs={12}
                                        show={true}
                                        disabled={isDisabled}
                                    />
                                </>
                            }
                            {
                                !!(productId == 131 && newOrder && newOrder.SubProduct && newOrder.SubProduct.ID
                                && [186].indexOf(newOrder.SubProduct.ID) != -1 )&&
                                <SelectDropdown
                                    name="PlanType"
                                    label="Plan Type"
                                    value={newOrder.PlanType || null}
                                    handleChange={handleChange}
                                    options={Data.PlanType}
                                    labelKeyInOptions="Name"
                                    valueKeyInOptions="_all"
                                    sm={6} md={4} xs={12}
                                    show={true}
                                    disabled={isDisabled}
                                />
                            }
                            <SelectDropdown
                                name="IsSTP"
                                label="STP/NSTP"
                                value={newOrder.IsSTP}
                                options={options.IsSTPOptions}
                                labelKeyInOptions='label'
                                valueKeyInOptions='value'
                                handleChange={handleChange}
                                show={productId === 2}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="ShopTypeId"
                                label="Shop Type"
                                value={newOrder.ShopTypeId}
                                options={ShopTypes}
                                labelKeyInOptions='ShopTypeName'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                disabled={isDisabled}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID === 8}
                                sm={6} md={4} xs={12}
                            />
                            <TextInput
                                name="RiderSI"
                                label="Rider SI"
                                value={newOrder.RiderSI}
                                handleChange={handleChange}
                                show={!!Visible.RiderSI}
                                maxLength={15}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="Rider"
                                label="Rider"
                                value={newOrder.Rider}
                                handleChange={handleChange}
                                show={!!Visible.Rider}
                                maxLength={100}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="ReferenceNo"
                                label="Reference Number"
                                value={newOrder.ReferenceNo}
                                handleChange={handleChange}
                                show={(!!Visible.InspectionRequired) && newOrder.Medical_or_InspectionRequired == 1 && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                maxLength={50}
                                disabled={isDisabled}

                            />
                            {(productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID === 14) &&
                                <Grid item sm={6} md={4} xs={12} >
                                    <Autocomplete
                                        onChange={(event, value) => handleChange({ target: { name: 'Association', value } })}
                                        id="Association"
                                        options={Association}
                                        name="Association"
                                        value={newOrder.Association}
                                        getOptionLabel={(option) => (option.Name || '')}
                                        disabled={isDisabled}
                                        renderInput={(params) =>
                                            <TextField {...params}
                                                label={"Association"}
                                                variant='outlined'
                                            />}
                                    />
                                </Grid>
                            }
                            <DatePicker
                                name="DateOfInspection"
                                label="Date of Inspection"
                                value={newOrder.DateOfInspection || 0}
                                handleChange={handleChange}
                                show={(!!Visible.InspectionRequired) && newOrder.Medical_or_InspectionRequired == 1 && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="InspectionStatus"
                                label="Status:"
                                value={newOrder.InspectionStatus}
                                options={InspectionType}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChange={handleChange}
                                show={(!!Visible.InspectionRequired) && newOrder.Medical_or_InspectionRequired == 1 && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="TransitType"
                                label="Transit Type"
                                value={newOrder.TransitType}
                                options={TransitTypes}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                show={!!Visible.TransitType && newOrder.SubProduct && newOrder.SubProduct.ID == 13}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            {IsSmeMarineAnualOpen(newOrder) &&
                                <Grid item sm={6} md={4} xs={12} >
                                    <Autocomplete
                                        name="TransitFrom"
                                        label="TransitFrom"
                                        value={newOrder.TransitFrom || null}
                                        onChange={(event, value) => handleChange({ target: { name: 'TransitFrom', value } })}
                                        options={options.cities}
                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                        renderInput={(params) => <TextField name="city" label="Transit From" variant="outlined" {...params} />}
                                        disabled={isDisabled}
                                        fullWidth
                                    />
                                </Grid>
                            }
                            {IsSmeMarineAnualOpen(newOrder) &&
                                <Grid item sm={6} md={4} xs={12} >
                                    <Autocomplete
                                        name="TransitTo"
                                        label="TransitTo"
                                        value={newOrder.TransitTo || null}
                                        onChange={(event, value) => handleChange({ target: { name: 'TransitTo', value } })}
                                        options={options.cities}
                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                        renderInput={(params) => <TextField name="city" label="Transit To" variant="outlined" {...params} />}
                                        disabled={isDisabled}
                                        fullWidth
                                    />
                                </Grid>
                            }
                            <SelectDropdown
                                name="OccupationType"
                                label="Occupation Type"
                                value={newOrder.OccupationType}
                                options={OccupationTypes}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                show={IsSmeMarineAnualOpen(newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="ManufacturerTraderName"
                                label={(newOrder.OccupationType && newOrder.OccupationType === "Manufacturer") ? "Manufacturer Name" : "Trader Name"}
                                value={newOrder.ManufacturerTraderName}
                                handleChange={handleChange}
                                show={IsSmeMarineAnualOpen(newOrder) && newOrder.OccupationType && (newOrder.OccupationType === "Manufacturer" || newOrder.OccupationType === "Trader")}
                                maxLength={50}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="ManufacturerTraderContactNo"
                                label={(newOrder.OccupationType && newOrder.OccupationType === "Manufacturer") ? "Manufacturer Contact No" : "Trader Contact No"}
                                value={newOrder.ManufacturerTraderContactNo}
                                handleChange={handleChange}
                                show={IsSmeMarineAnualOpen(newOrder) && newOrder.OccupationType && (newOrder.OccupationType === "Manufacturer" || newOrder.OccupationType === "Trader")}
                                maxLength={12}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="ConstitutionOfBusiness"
                                label="Constitution Of Business"
                                value={newOrder.ConstitutionOfBusiness}
                                options={ConstitutionOfBusinesses}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                show={IsSmeMarineAnualOpen(newOrder) || IsSmeWC(newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <MultipleSelectDropdown
                                name="Inclusion"
                                label="Inclusions"
                                value={newOrder.Inclusion || []}
                                options={Inclusions}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChangeMultiple={handleChange}
                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                show={IsSmeMarineAnualOpen(newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                shrink={{ shrink: newOrder.Inclusion && newOrder.Inclusion.length > 0 }}
                            />
                            {productId === 131 && newOrder.SubProduct?.ID === 13 && (
                                newOrder.TransitType === "Annual Open" ? (
                                    <MultipleSelectDropdown
                                        name="ShipmentType"
                                        label="Shipment Type"
                                        options={ShipmentTypes}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="_all"
                                        value={newOrder.ShipmentType || []}
                                        handleChangeMultiple={handleChange}
                                        renderValue={(selected) => Array.isArray(selected) ? selected.map((s) => s.Name).join(", ") : ""}
                                        sm={6} md={4} xs={12}
                                        disabled={isDisabled}
                                        shrink={{ shrink: newOrder.ShipmentType?.length > 0 }}
                                    />
                                ) : (
                                    <SelectDropdown
                                        name="ShipmentType"
                                        label="Shipment Type"
                                        options={ShipmentTypes}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="Id"
                                        value={newOrder.ShipmentType}
                                        handleChange={handleChange}
                                        sm={6} md={4} xs={12}
                                        disabled={isDisabled}
                                    />
                                )
                            )}
                            <TextInput
                                name="InlandSI"
                                label="Inland SI"
                                value={newOrder.InlandSI}
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID === 13 && newOrder.TransitType === "Annual Open" && IsShipmentTypeSelected(newOrder, 'Inland')}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                maxLength={15}
                            />
                            <TextInput
                                name="ImportSI"
                                label="Import SI"
                                value={newOrder.ImportSI}
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID === 13 && newOrder.TransitType === "Annual Open" && IsShipmentTypeSelected(newOrder, 'Import')}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                maxLength={15}
                            />
                            <TextInput
                                name="ExportSI"
                                label="Export SI"
                                value={newOrder.ExportSI}
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID === 13 && newOrder.TransitType === "Annual Open" && IsShipmentTypeSelected(newOrder, 'Export')}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                maxLength={15}
                            />
                            <TextInput
                                name="MerchantTradingSI"
                                label="Merchant Trading SI"
                                value={newOrder.MerchantTradingSI}
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID && newOrder.SubProduct.ID === 13 && newOrder.TransitType === "Annual Open" && IsShipmentTypeSelected(newOrder, 'Merchant Trading')}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                maxLength={15}
                            />
                       
                            <SelectDropdown
                                name="MarineCoverType"
                                label="Cover Type"
                                value={newOrder.MarineCoverType}
                                options={MarineCoverTypes}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID === 13}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <SelectDropdown
                                name="MedicalExtension"
                                label="Medical Extension"
                                value={newOrder.MedicalExtension}
                                options={MedicalExtensions}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='Id'
                                handleChange={handleChange}
                                show={IsSmeWC(newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                            />
                            <TextInput
                                name="OtherMedicalExtension"
                                label="Other Medical Extension"
                                value={newOrder.OtherMedicalExtension}
                                handleChange={handleChange}
                                show={IsSmeWC(newOrder) && newOrder.MedicalExtension && newOrder.MedicalExtension === "Other"}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                maxLength={10}
                            />
                            <MultipleSelectDropdown
                                name="WorkerType"
                                label="Worker Type"
                                value={newOrder.WorkerType || []}
                                options={WorkerTypes}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChangeMultiple={handleChange}
                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                show={IsSmeWC(newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                shrink={{ shrink: newOrder.WorkerType && newOrder.WorkerType.length > 0 }}
                            />
                            {
                                IsSmeWC(newOrder) && Array.isArray(newOrder.WorkerType) && newOrder.WorkerType.map((worker, workerIndex) => {
                                    return (
                                        <Fragment key={"KeyWorker" + newOrder.WorkerType.length + workerIndex}>
                                            <TextInput
                                                name={GetNameOfWorker(worker.Name, true)}
                                                label={"No Of " + worker.Name + " Workers"}
                                                value={GetNumOfWorker(newOrder, worker.Name, true)}
                                                handleChange={handleChange}
                                                sm={4} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={10}
                                            />
                                            <TextInput
                                                name={GetNameOfWorker(worker.Name, false)}
                                                label={"Salary " + worker.Name + " Worker Per Month"}
                                                value={GetNumOfWorker(newOrder, worker.Name, false)}
                                                handleChange={handleChange}
                                                sm={4} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={10}
                                            />
                                        </Fragment>
                                    )
                                })
                            }
                            <MultipleSelectDropdown
                                name="InsuredScope"
                                label="Insured Scope"
                                value={newOrder.InsuredScope || []}
                                options={Data.InsuredScopes}
                                labelKeyInOptions='Name'
                                valueKeyInOptions='_all'
                                handleChangeMultiple={handleChange}
                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                show={IsSmePropertyProduct(productId, newOrder)}
                                sm={6} md={4} xs={12}
                                disabled={isDisabled}
                                shrink={{ shrink: newOrder.InsuredScope && newOrder.InsuredScope.length > 0 }}
                            />
                            {
                                IsSmePropertyProduct(productId, newOrder) && Array.isArray(newOrder.InsuredScope) && newOrder.InsuredScope.map((insuredScope, indx) => {
                                    return (
                                        <Fragment key={"KeyInsuredScope" + newOrder.InsuredScope.length + indx}>
                                            <TextInput
                                                name={GetInsuredScopeName(insuredScope.Name)}
                                                label={insuredScope.Name + " Value"}
                                                value={GetInsuredScopeValue(newOrder, insuredScope.Name)}
                                                handleChange={handleChange}
                                                sm={4} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={10}
                                            />
                                        </Fragment>
                                    )
                                })
                            }
                            {productId === 131 &&
                                <Grid item sm={12} md={4} xs={12}>
                                    {[<p className="emiTxt" key="131">Co-Insurance</p>,
                                    ...CoInsuranceTypes.map((values) => (
                                        <FormControlLabel
                                            key={values.Id}
                                            value={values.Id}
                                            control={<Radio color="primary" />}
                                            label={values.Name}
                                            onChange={handleChange}
                                            checked={newOrder.CoInsurance == values.Id}
                                            name="CoInsurance"
                                            disabled={isDisabled}
                                        />
                                    ))]}
                                </Grid>
                            }
                            <TextInput
                                name="LeadersPercentage"
                                label="Leader Supplier's Percentage"
                                value={newOrder.LeadersPercentage}
                                handleChange={handleChange}
                                show={IsCoInsurer(newOrder)}
                                maxLength={3}
                                disabled={isDisabled}
                            />
                        </Grid>
                        {(productId === 139) &&
                            <Grid container spacing={3}>
                                <TextInput
                                    name="TPPremium"
                                    label="TPPremium"
                                    value={newOrder.TPPremium}
                                    handleChange={handleChange}
                                    show={!!Visible.TPPremium}
                                    maxLength={6}
                                    disabled={isDisabled}
                                />
                                <TextInput
                                    name="AddonPremium"
                                    label="AddonPremium"
                                    value={newOrder.AddonPremium}
                                    handleChange={handleChange}
                                    show={!!Visible.AddonPremium}
                                    maxLength={6}
                                    disabled={isDisabled}
                                />
                                <TextInput
                                    name="ServiceTax"
                                    label="ServiceTax"
                                    value={newOrder.ServiceTax}
                                    handleChange={handleChange}
                                    show={!!Visible.ServiceTax}
                                    maxLength={6}
                                    disabled={isDisabled}
                                />
                            </Grid>
                        }
                        {IsCoInsurer(newOrder) &&
                            <Grid container spacing={3}>
                                {
                                    <Grid item sm={12} md={12} xs={12}>
                                        <Button
                                            variant="text"
                                            color="secondary"
                                            onClick={(e) => handleChange({ target: { name: 'AddFollowerSupplier' } })}
                                            disabled={isDisabled}
                                            className="addbtn"
                                        >
                                            <AddCircleOutline /> Add Follower Supplier
                                        </Button>
                                    </Grid>
                                }
                                {newOrder.FollowerSuppliers && Array.isArray(newOrder.FollowerSuppliers) && newOrder.FollowerSuppliers.map((followerSuppliers, followerIndex) => {
                                    return (
                                        <>
                                            <Grid item sm={6} md={6} xs={12}>
                                                <Autocomplete
                                                    onChange={(event, value) => handleChange({ target: { name: 'FollowerSuppliers', value, index: followerIndex } })}
                                                    id="FollowerSuppliers"
                                                    options={(productId == 131 && IsShowSupplierBySubProduct && SupplierBySubProduct) ? SupplierBySubProduct : supplierList}
                                                    name="FollowerSuppliers"
                                                    value={followerSuppliers.FollowerSupplier}
                                                    getOptionLabel={(option) => (option.SupplierDisplayName || '')}
                                                    getOptionDisabled={(option) => DisableFollowerSupplier(option, { ...newOrder })}
                                                    disabled={isDisabled}
                                                    show={true}
                                                    renderInput={(params) =>
                                                        <TextField {...params}
                                                            label="Follower Supplier"
                                                            variant='outlined'
                                                        />}
                                                />
                                            </Grid>
                                            <TextInput
                                                name="FollowerPercentage"
                                                label="Follower's Percentage"
                                                value={followerSuppliers.FollowerPercentage ? followerSuppliers.FollowerPercentage : ""}
                                                handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: followerIndex } })}
                                                show={true}
                                                sm={4} md={4} xs={12}
                                                maxLength={3}
                                                disabled={isDisabled}
                                            />
                                            {
                                                <Grid item sm={2} md={2} xs={12} className="text-center">
                                                    <Button
                                                        onClick={(e) => handleChange({ target: { name: 'HandleFollowerSupplierDelete', index: followerIndex } })}
                                                        disabled={isDisabled}
                                                    >
                                                        <DeleteIcon className="red" />
                                                    </Button>
                                                </Grid>
                                            }
                                        </>
                                    )
                                })}
                            </Grid>
                        }
                        {!!(productId == 131 && newOrder.PayTerm && newOrder.PayTerm > 1) &&
                            <Grid container spacing={3}>
                                {
                                    <Grid item sm={12} md={12} xs={12}>
                                        <Button
                                            variant="text"
                                            color="secondary"
                                            onClick={(e) => handleChange({ target: { name: 'HandleAddNewInstallment' } })}
                                            disabled={isDisabled}
                                            className="addbtn"
                                        >
                                            <AddCircleOutline />  Add Installments
                                        </Button>
                                    </Grid>
                                }
                                {
                                    newOrder.InstallmentsData && Array.isArray(newOrder.InstallmentsData) && newOrder.InstallmentsData.map((installment, installmentIndex) => {
                                        return (
                                            <Fragment key={"Key" + newOrder.InstallmentsData.length + installmentIndex}>
                                                <DatePicker
                                                    name="InstallmentDate"
                                                    label={"Installment " + (installmentIndex + 1)}
                                                    value={installment.Date ? new Date(installment.Date) : null}
                                                    handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: installmentIndex } })}
                                                    sm={4} md={4} xs={12}
                                                    disabled={isDisabled}
                                                    default={null}
                                                />
                                                <TextInput
                                                    name="InstallmentAmount"
                                                    label="Amount"
                                                    value={installment.Amount}
                                                    handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: installmentIndex } })}
                                                    sm={4} md={4} xs={12}
                                                    disabled={isDisabled}
                                                    maxLength={15}
                                                />
                                                {
                                                    <Grid item sm={2} md={2} xs={12} className="text-center">
                                                        <Button
                                                            onClick={(e) => handleChange({ target: { name: 'HandleInstallmentDelete', index: installmentIndex } })}
                                                            disabled={isDisabled}
                                                        >
                                                            <DeleteIcon className="red" />
                                                        </Button>
                                                    </Grid>
                                                }
                                            </Fragment>
                                        )
                                    })
                                }
                            </Grid>
                        }
                        {(IsSmeWC(newOrder) || IsSmePropertyProduct(productId, newOrder) || IsSmeEngineeringSubproduct(newOrder))&&
                            <Grid container spacing={3}>
                                {
                                    <Grid item sm={12} md={12} xs={12}>
                                        <Button
                                            variant="text"
                                            color="secondary"
                                            onClick={(e) => handleChange({ target: { name: 'HandleAddRiskLocation' } })}
                                            disabled={isDisabled}
                                            className="addbtn"
                                        >
                                            <AddCircleOutline />  Add Risk Location
                                        </Button>
                                    </Grid>
                                }
                                {
                                    Array.isArray(newOrder.RiskLocations) && newOrder.RiskLocations.map((riskLocation, riskIndex) => {
                                        return <>
                                            <TextInput
                                                name="RiskAddress"
                                                label="Address"
                                                value={riskLocation.RiskAddress}
                                                handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: riskIndex } })}
                                                sm={4} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={100}
                                            />
                                            <TextInput
                                                name="RiskCityPincode"
                                                label="Pincode"
                                                value={riskLocation.PinCode}
                                                handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: riskIndex } })}
                                                sm={4} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={6}
                                            />
                                            <Grid item sm={3} md={3} xs={12} >
                                                <Autocomplete
                                                    name="RiskLocationCity"
                                                    label="City"
                                                    value={riskLocation.City || null}
                                                    onChange={(event, value) => handleChange({ target: { name: 'RiskLocationCity', value, index: riskIndex } })}
                                                    options={options.cities}
                                                    getOptionLabel={(option) => (option.CityStateName || '')}
                                                    isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                                    renderInput={(params) => <TextField name="city" label="City" variant="outlined" {...params} />}
                                                    disabled={isDisabled}
                                                />
                                            </Grid>
                                            {
                                                <Grid item sm={1} md={1} xs={12} className="text-center">
                                                    <Button
                                                        onClick={(e) => handleChange({ target: { name: 'HandleRiskLocationDelete', index: riskIndex } })}
                                                        disabled={isDisabled}
                                                    >
                                                        <DeleteIcon className="red" />
                                                    </Button>
                                                </Grid>
                                            }
                                        </>;
                                    })
                                }
                            </Grid>
                        }
                        {!!(productId == 131 && newOrder.SubProduct && SmeGhiSubProducts.indexOf(newOrder.SubProduct.ID) > -1 && newOrder.SumInsuredType && newOrder.SumInsuredType.Id == 2) &&
                            <Grid container spacing={3}>
                                {
                                    <Grid item sm={12} md={12} xs={12}>
                                        <Button
                                            variant="text"
                                            color="secondary"
                                            onClick={(e) => handleChange({ target: { name: 'HandleAddNewGrade' } })}
                                            disabled={isDisabled}
                                            className="addbtn"
                                        >
                                            <AddCircleOutline />  Add Grades
                                        </Button>
                                    </Grid>
                                }
                                {
                                    newOrder.Grades && Array.isArray(newOrder.Grades) && newOrder.Grades.map((grade, gradeIndex) => {
                                        return (
                                            <>
                                                <TextInput
                                                    name="GradeName"
                                                    value={grade.Name}
                                                    sm={4} md={4} xs={12}
                                                    disabled={true}
                                                    maxLength={12}
                                                />
                                                <TextInput
                                                    name="GradeSumInsured"
                                                    label="Sum Insured"
                                                    value={(grade && grade.SumInsured) ? grade.SumInsured : ""}
                                                    handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: gradeIndex } })}
                                                    sm={4} md={4} xs={12}
                                                    disabled={isDisabled}
                                                    maxLength={15}
                                                />
                                                {
                                                    <Grid item sm={2} md={2} xs={12} className="text-center">
                                                        <Button
                                                            onClick={(e) => handleChange({ target: { name: 'HandleGradeDelete', index: gradeIndex } })}
                                                            disabled={isDisabled}
                                                        >
                                                            <DeleteIcon className="red" />
                                                        </Button>
                                                    </Grid>
                                                }
                                            </>
                                        )
                                    })
                                }
                            </Grid>
                        }
                        <h3 className="paymentDetails-title" onClick={() => setNewOrder({ ...newOrder, ShowPaymentDetails: !newOrder.ShowPaymentDetails })}>Payment Details</h3>
                        {newOrder.ShowPaymentDetails &&
                            <>
                                <Grid container spacing={3}>

                                    <SelectDropdown
                                        name="paymentMode"
                                        label="Payment Mode"
                                        value={newOrder.payment.PaymentStatus}
                                        options={options.paymentMode}
                                        labelKeyInOptions="PaymentModeName"
                                        valueKeyInOptions="PaymentModeValue"
                                        handleChange={(e) => handleChange(e, 'payment', 'PaymentStatus')}
                                        sm={6} md={4} xs={12}
                                        show={true}
                                        disabled={isDisabled}
                                    />
                                    <SelectDropdown
                                        name="subPaymentMode"
                                        label="Payment Sub Mode"
                                        value={newOrder.payment.PaymentSubStatus}
                                        options={SubPaymentModesOptions}
                                        labelKeyInOptions="PaymentModeName"
                                        valueKeyInOptions="PaymentModeValue"
                                        handleChange={(e) => handleChange(e, 'payment', 'PaymentSubStatus')}
                                        sm={6} md={4} xs={12}
                                        show={((productId === 2 || productId === 131) && !IsNotPaySubMode) && !(productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1))}
                                        disabled={isDisabled}
                                    />
                                    <SelectDropdown
                                        name="paymentFrequency"
                                        label="Payment Frequency"
                                        value={newOrder.payment.PaymentPeriodicity}
                                        options={options.paymentFrequency}
                                        labelKeyInOptions="label"
                                        valueKeyInOptions="value"
                                        handleChange={(e) => handleChange(e, 'payment', 'PaymentPeriodicity')}
                                        sm={6} md={4} xs={12}
                                        show={(!!Visible.PaymentPeriodicity) && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="ChequeNo"
                                        label="Cheque No."
                                        value={newOrder.payment.ChequeNo}
                                        handleChange={(e) => handleChange(e, 'payment', 'ChequeNo')}
                                        show={(newOrder.payment.PaymentStatus == 5001) && !(productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1))}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="TransRefNo"
                                        label="Transaction Number"
                                        value={newOrder.payment.TransRefNo}
                                        handleChange={(e) => handleChange(e, 'payment', 'TransRefNo')}
                                        show={(newOrder.payment.PaymentStatus != 5001 && !!Visible.TransRefNo) && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                        disabled={isDisabled}
                                    />

                                    {Visible.EMI && (productId === 106 || productId === 118 || productId === 130 || productId === 114) &&
                                        <Grid item sm={12} md={12} xs={12}>
                                            {[<p className="emiTxt" key="100">EMI</p>,
                                            ...(EMI.map((values) => (
                                                <FormControlLabel
                                                    key={values.Id}
                                                    value={values.Id}
                                                    control={<Radio color="primary" />}
                                                    label={values.Name}
                                                    onChange={handleChange}
                                                    checked={newOrder.payment.IsEMI == values.Id}
                                                    name="emi"
                                                    disabled={isDisabled}
                                                />
                                            )))]}

                                        </Grid>
                                    }


                                    <SelectDropdown
                                        name="PaymentSource"
                                        label="Payment Source"
                                        value={newOrder.PaymentSource}
                                        options={options.paymentSource}
                                        labelKeyInOptions="label"
                                        valueKeyInOptions="value"
                                        handleChange={handleChange}
                                        sm={6} md={4} xs={12}
                                        show={(!!Visible.PaymentSource) && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="BankNameBranch"
                                        label="Bank Name"
                                        value={(newOrder.payment && newOrder.payment.BankNameBranch)}
                                        handleChange={(e) => handleChange(e, 'payment', 'BankNameBranch')}
                                        show={(!!Visible.BankName) && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                        maxLength={255}
                                        disabled={isDisabled}
                                    />
                                    <SelectDropdown
                                        name="BookingFrom"
                                        label="Booking From"
                                        value={newOrder.BookingFrom || null}
                                        handleChange={handleChange}
                                        options={(newOrder.SubProduct && ([1, 2, 3, 4].indexOf(newOrder.SubProduct.ID) != -1)) ? Data.BookingFromTypes : Data.BookingFromTypesSME}
                                        labelKeyInOptions="Name"
                                        valueKeyInOptions="_all"
                                        sm={6} md={4} xs={12}
                                        show={productId === 131}
                                        disabled={isDisabled}
                                    />
                                    {newOrder.BookingFrom && newOrder.BookingFrom.Id && newOrder.BookingFrom.Id == "CJ" && <TextInput
                                        name="QuoteId"
                                        label="Quote Id"
                                        value={newOrder.QuoteId || ""}
                                        handleChange={handleChange}
                                        show={productId === 131 && newOrder.SubProduct && newOrder.SubProduct.ID && ([1].indexOf(newOrder.SubProduct.ID) != -1)}
                                        maxLength={255}
                                        disabled={isDisabled}
                                    />}
                                    <TextInput
                                        name="ApplicationNo"
                                        label="Application number"
                                        value={newOrder.ApplicationNo}
                                        handleChange={handleChange}
                                        show={(productId !== 7 && (!!Visible.ApplicationNo)) && !(productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1)}
                                        maxLength={255}
                                        disabled={isDisabled}
                                    />
                                    <DatePicker
                                        name="PaymentDate"
                                        label="Payment Date"
                                        value={newOrder.PaymentDate}
                                        handleChange={handleChange}
                                        show={(!!Visible.PaymentDate) && !(productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1))}
                                        disabled={isDisabled}
                                    />
                                    <DatePicker
                                        name="IssuanceDate"
                                        label="Issuance Date"
                                        value={newOrder.IssuanceDate}
                                        handleChange={handleChange}
                                        show={(!!Visible.IssuanceDate) && !(productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1))}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="InstallmentPaid"
                                        label="Installment"
                                        value={newOrder.InstallmentPaid}
                                        handleChange={handleChange}
                                        show={(!!Visible.Installment) && !(productId === 117 && newOrder.PolicyType && (newOrder.PolicyType.PolicyTypeId == 1))}
                                        maxLength={2}
                                        disabled={true}
                                    />
                                    {Visible.EMI && !(productId === 106 || productId === 118 || productId === 130 || productId === 114) &&
                                        <Grid item sm={12} md={12} xs={12}>

                                            <p className="emiTxt"> EMI: </p>
                                            <FormControlLabel
                                                value={1}
                                                control={<Radio color="primary" />}
                                                label="Yes"
                                                onClick={(e) => handleChange(e, 'payment', 'IsEMI')}
                                                checked={newOrder.payment.IsEMI == 1}
                                                name="emi"
                                                disabled={isDisabled}

                                            />
                                            <FormControlLabel
                                                value={0}
                                                control={<Radio color="primary" />}
                                                label="No"
                                                onClick={(e) => handleChange(e, 'payment', 'IsEMI')}
                                                checked={newOrder.payment.IsEMI == 0}
                                                name="emi"
                                                disabled={isDisabled}
                                            />

                                        </Grid>
                                    }
                                    <TextInput
                                        name="PolicyNo"
                                        label="Policy No"
                                        value={newOrder.PolicyNo}
                                        handleChange={handleChange}
                                        show={productId !== 7 && !!Visible.PolicyNo}
                                        maxLength={100}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="ProposalNo"
                                        label="ProposalNo"
                                        value={newOrder.ProposalNo}
                                        handleChange={handleChange}
                                        show={productId === 117 && newOrder.PolicyType && newOrder.PolicyType.PolicyTypeId == 1}
                                        maxLength={16}
                                        disabled={isDisabled}
                                    />
                                    <TextInput
                                        name="SalesAgent"
                                        label={"Primary Agent"}
                                        value={PrimaryAgent}
                                        show={productId == 131}
                                        disabled={true}
                                    />
                                    <TextInput
                                        name="PrimaryAgentSharePercentage"
                                        label="Share Percentage"
                                        value={newOrder.PrimaryAgentSharePercentage}
                                        show={productId == 131}
                                        disabled={true}
                                        maxLength={6}
                                    />
                                    {IsSmePos(newOrder) &&
                                        <>
                                            <TextInput
                                                name="PAN"
                                                label="PAN"
                                                value={newOrder.PAN}
                                                handleChange={handleChange}
                                                show={true}
                                                maxLength={10}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="GST"
                                                label="GSTIN"
                                                value={newOrder.GST}
                                                handleChange={handleChange}
                                                show={true}
                                                maxLength={15}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="CIN"
                                                label="CIN"
                                                value={newOrder.CIN}
                                                handleChange={handleChange}
                                                show={true}
                                                maxLength={21}
                                                disabled={isDisabled}
                                            />
                                        </>}
                                </Grid>
                                {(productId == 131) &&
                                    <Grid container spacing={3}>
                                        {
                                            <Grid item sm={12} md={12} xs={12}>
                                                <Button
                                                    variant="text"
                                                    color="secondary"
                                                    onClick={(e) => handleChange({ target: { name: 'HandleAddNewSalesPartner' } })}
                                                    disabled={isDisabled}
                                                    className="addbtn"
                                                >
                                                    <AddCircleOutline /> Add Secondary Sales Agent
                                                </Button>
                                            </Grid>
                                        }
                                        {newOrder.SalesPartners && Array.isArray(newOrder.SalesPartners) && newOrder.SalesPartners.map((salesPartner, partnerIndex) => {
                                            return (
                                                <>
                                                    <Grid item sm={6} md={6} xs={12}>
                                                        <Autocomplete
                                                            loading={true}
                                                            loadingText={IsLoading ? 'Loading...' : 'No options'}
                                                            onChange={(event, value) => handleChange({ target: { name: 'SalesPartners', value, index: partnerIndex } })}
                                                            id="SalesPartner"
                                                            options={SalesPartners}
                                                            name="SalesPartner"
                                                            value={salesPartner || null}
                                                            getOptionLabel={(option) => (option.UserName) || ''}
                                                            getOptionDisabled={(option) => DisableSelectedSalesPartnerOrSalesSpecialist(option, { ...newOrder })}
                                                            disabled={isDisabled}
                                                            onInputChange={(event, inputSalesPartner) => {
                                                                setIsLoading(true);
                                                                setInputSalesPartner(inputSalesPartner);

                                                                if (!inputSalesPartner) {
                                                                    let partner = { SharePercentage: "", UserId: "0", UserName: "" };
                                                                    OnSalesPartnerChanged('SalesPartners', partner, partnerIndex, newOrder, -1);
                                                                    setIsLoading(false);
                                                                }
                                                            }}
                                                            renderInput={(params) =>
                                                                <TextField {...params}
                                                                    label="Secondary Sales Agent"
                                                                    variant='outlined'
                                                                />}
                                                        />
                                                    </Grid>
                                                    <TextInput
                                                        name="OnSalesPartnerSharePercentageChanged"
                                                        label="Share Percentage"
                                                        value={(salesPartner && salesPartner.SharePercentage) ? salesPartner.SharePercentage : ""}
                                                        handleChange={(e) => handleChange({ target: { name: e.target.name, value: e.target.value, index: partnerIndex } })}
                                                        show={productId === 131}
                                                        sm={4} md={4} xs={12}
                                                        disabled={isDisabled}
                                                        maxLength={6}
                                                    />
                                                    {
                                                        productId == 131 &&
                                                        <Grid item sm={2} md={2} xs={12} className="text-center">
                                                            <Button
                                                                onClick={(e) => handleChange({ target: { name: 'HandleSalesPartnerDelete', index: partnerIndex } })}
                                                                disabled={isDisabled}
                                                            >
                                                                <DeleteIcon className="red" />
                                                            </Button>
                                                        </Grid>
                                                    }
                                                </>
                                            )
                                        })}
                                        {
                                            <Grid item sm={12} md={12} xs={12} className="pdTop-0">
                                                <Button
                                                    variant="text"
                                                    color="secondary"
                                                    onClick={(e) => handleChange({ target: { name: 'HandleAddNewSalesSpecialist' } })}
                                                    disabled={isDisabled}
                                                    className="addbtn"
                                                >
                                                    <AddCircleOutline /> Add Sales Specialist
                                                </Button>
                                            </Grid>
                                        }
                                        {newOrder.SalesSpecialists && Array.isArray(newOrder.SalesSpecialists) && newOrder.SalesSpecialists.map((salesSpecialist, specialistIndex) => {
                                            return (
                                                <>
                                                    <Grid item sm={6} md={6} xs={12}>
                                                        <Autocomplete
                                                            loading={true}
                                                            loadingText={IsLoading ? 'Loading...' : 'No options'}
                                                            onChange={(event, value) => handleChange({ target: { name: 'SalesSpecialists', value, index: specialistIndex } })}
                                                            id="SalesSpecialist"
                                                            options={SalesSpecialists}
                                                            name="SalesSpecialist"
                                                            value={salesSpecialist || null}
                                                            getOptionLabel={(option) => (option.UserName) || ''}
                                                            getOptionDisabled={(option) => DisableSelectedSalesPartnerOrSalesSpecialist(option, { ...newOrder })}
                                                            disabled={isDisabled}
                                                            onInputChange={(event, inputSalesSpecialist) => {
                                                                setIsLoading(true);
                                                                setInputSalesSpecialist(inputSalesSpecialist);

                                                                if (!inputSalesSpecialist) {
                                                                    setIsLoading(false);
                                                                    let specialist = { SharePercentage: "", UserId: "0", UserName: "" };
                                                                    OnSalesSpecialistChanged('SalesSpecialists', specialist, specialistIndex, newOrder, -1);
                                                                }
                                                            }}
                                                            renderInput={(params) =>
                                                                <TextField {...params}
                                                                    label="Sales Specialist"
                                                                    variant='outlined'
                                                                />}
                                                        />
                                                    </Grid>
                                                    {
                                                        <Grid item sm={2} md={2} xs={12} className="text-center">
                                                            <Button
                                                                onClick={(e) => handleChange({ target: { name: 'HandleSalesSpecialistDelete', index: specialistIndex } })}
                                                                disabled={isDisabled}
                                                            >
                                                                <DeleteIcon className="red" />
                                                            </Button>
                                                        </Grid>
                                                    }
                                                </>
                                            )
                                        })}
                                    </Grid>
                                }
                            </>
                        }

                        <Grid item sm={12} md={12} xs={12} className="text-center" >
                            {!isDisabled && <Button className="editBtn" onClick={CancelNewOrder}>Cancel</Button>}
                            {
                                !isDisabled &&
                                <Button
                                    className="confirmBtn"
                                    onClick={() => ValidateOrder(newOrder, 0)}
                                    disabled={!IsSaveUpdateEnabled}
                                >
                                    Save
                                </Button>
                            }
                        </Grid>
                    </>
                    }
                </div>
            }

            {/* Booking Details*/}
            {Array.isArray(OrderList) && OrderList.length > 0 &&
                <div className="bookingDeatilsHead">
                    <h3>Booking Details</h3>
                    <span className="caption">All existing bookings</span>
                    {OrderList.map((item, index) => (
                        <div key={item.leadId} name='Order' className="orderbooking-section">
                            <div className="expandmoreIcon"><ExpandMore onClick={() => { GetBookingDetails(index, item.leadId, "Toggle") }}
                                style={{ transform: (item.ShowBookingDetails) ? 'rotate(180deg)' : 'rotate(0deg)' }}
                            />
                            </div>
                            {(item.leadId > 0) &&
                                <>
                                    <Grid container spacing={3}>
                                        <Grid item sm={3} md={3} xs={3}>
                                            <span className="leadId">{item.leadId}</span>
                                        </Grid>
                                        <Grid item sm={3} md={3} xs={3}>
                                            <span className="planName"> {(item.Supplier && item.Supplier.SupplierDisplayName) ?
                                                item.Supplier.SupplierDisplayName.substr(0, 50) : ''}
                                                <br />({(item.Plan && item.Plan.PlanName) ? item.Plan.PlanName.substr(0, 50) : ''})</span>

                                        </Grid>
                                        <Grid item sm={6} md={6} xs={6}>
                                            <span className="planName"><label>Premuim</label> <br />  
                                                {(() => {
                                                    // Only check currency for investment products
                                                    if (productId === 115) {
                                                        const lead = allLeads.find(lead => lead.LeadID === item.leadId);
                                                        // Show $ for CurrencyId 1, otherwise show ₹
                                                        return (lead?.CurrencyId === 1) 
                                                            ? currency(item.TotalPremium) 
                                                            : currency(item.TotalPremium, "INR");
                                                    }
                                                    // For all other products, always show ₹
                                                    return currency(item.TotalPremium, "INR");
                                                })()}
                                            </span>
                                        </Grid>

                                    </Grid>
                                </>
                            }

                            {item.ShowBookingDetails &&
                                <div className="bookingMidSection">
                                    <>
                                        <Grid container spacing={3}>
                                            <SelectDropdown
                                                name="SubProduct"
                                                label="Product Type"
                                                value={item.SubProduct}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={options.SubProduct}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={!!Visible.ProductType}
                                                disabled={isDisabled || (productId == 131 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                            />
                                            <Grid item sm={6} md={4} xs={12}>
                                                <Autocomplete
                                                    onChange={(event, value) => handleChangeForBooking({ target: { name: 'Supplier', value } }, item, index)}
                                                    id="Supplier"
                                                    options={(productId == 131 && IsShowSupplierBySubProduct && SupplierBySubProduct) ? SupplierBySubProduct : getSupplierList(item)}
                                                    name="Supplier"
                                                    value={item.Supplier}
                                                    getOptionLabel={(option) => (option.SupplierDisplayName || '')}
                                                    disabled={isDisabled}
                                                    show={true}
                                                    renderInput={(params) =>
                                                        <TextField {...params}
                                                            label={IsCoInsurer(item) ? "Leader Supplier" : "Supplier"}
                                                            variant='outlined'
                                                        />}
                                                />
                                            </Grid>
                                            <SelectDropdown
                                                name="Plan"
                                                label="Plan"
                                                value={item.Plan}
                                                options={getOptimizedPlanList(item)}
                                                labelKeyInOptions="PlanName"
                                                valueKeyInOptions="_all"
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={true}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="PolicyType"
                                                label={productId === 117 ? "Purchase type" : "Policy Type"}
                                                value={item.PolicyType}
                                                options={options.policyType}
                                                labelKeyInOptions="PolicyTypeName"
                                                valueKeyInOptions="_all"
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={!!Visible.PolicyType}
                                                disabled={isDisabled || (productId === 131 && IsDisabledForSmeElements)}
                                            />
                                            <SelectDropdown
                                                name="SumInsuredType"
                                                label="Sum Insured Type"
                                                value={item.SumInsuredType}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={SumInsuredTypeOptions}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={!!(productId == 131 && item.SubProduct && SmeGhiSubProducts.indexOf(item.SubProduct.ID) > -1)}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="FamilyType"
                                                label="Family Type"
                                                value={item.FamilyType}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={FamilyTypes}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={!!(productId == 131 && item.SubProduct && SmeGhiSubProducts.indexOf(item.SubProduct.ID) > -1)}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="SIPerPerson"
                                                label="Sum Insured per person"
                                                value={item.SIPerPerson}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!SIPerPerson}
                                                maxLength={7}
                                                disabled={isDisabled}
                                            />
                                            <Tooltip title={GetNumLabel(item.SumInsured)}>
                                                <TextInput
                                                    name="SumInsured"
                                                    label={productId === 117 ? "IDV" : "Sum Insured"}
                                                    value={item.SumInsured}
                                                    handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                    show={!!Visible.SumInsured}
                                                    maxLength={12}
                                                    disabled={isDisabled || SIPerPerson || IsSIdisabled || IsTPChecked}
                                                />
                                            </Tooltip>
                                            <Tooltip title={GetNumLabel(item.TotalPremium)}>
                                                <TextInput
                                                    name="TotalPremium"
                                                    label={"Premium"}
                                                    value={item.TotalPremium}
                                                    handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                    show={!!Visible.Premium}
                                                    maxLength={9}
                                                    disabled={isDisabled}
                                                />
                                            </Tooltip>
                                            <TextInput
                                                name="PaidPremium"
                                                label="Paid Premium"
                                                value={item.PaidPremium}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 148}
                                                disabled={isDisabled}
                                            />

                                            {!!shouldShowPremiumFields(item) && (
                                                <>
                                                    <SelectDropdown
                                                        name="TerrorismPremiumVal"
                                                        label="Is Terrorism Premium Included"
                                                        value={item.TerrorismPremiumVal || null}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        options={YesNo}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        sm={6} md={4} xs={12}
                                                        disabled={isDisabled}
                                                    />
                                                    {item.TerrorismPremiumVal?.Id === 1 && (
                                                        <Tooltip title={GetNumLabel(item.TerrorismPremium)}>
                                                            <TextInput
                                                                name="TerrorismPremium"
                                                                label="Terrorism Premium"
                                                                value={(item.TerrorismPremium && item.TerrorismPremium > 0) ? item.TerrorismPremium : null}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                disabled={isDisabled || IsTerrorismPremiumDisabled(item)}
                                                            />
                                                        </Tooltip>
                                                    )}

                                                    <SelectDropdown
                                                        name="BurglaryPremiumVal"
                                                        label="Is Burglary Premium Included"
                                                        value={item.BurglaryPremiumVal || null}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        options={YesNo}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        sm={6} md={4} xs={12}
                                                        disabled={isDisabled}
                                                    />

                                                    {item.BurglaryPremiumVal?.Id === 1 && (
                                                        <Tooltip title={GetNumLabel(item.BurglaryPremium)}>
                                                            <TextInput
                                                                name="BurglaryPremium"
                                                                label="Burglary Premium"
                                                                value={(item.BurglaryPremium && item.BurglaryPremium > 0) ? item.BurglaryPremium : null}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                disabled={isDisabled}
                                                            />
                                                        </Tooltip>
                                                    )}
                                                    {((item.BurglaryPremiumVal?.Id === 1) || (item.TerrorismPremiumVal?.Id === 1)) && (
                                                        <Tooltip title={GetNumLabel(item.FirePremium)}>
                                                            <TextInput
                                                                name="FirePremium"
                                                                label="Fire Premium"
                                                                value={(item.FirePremium && item.FirePremium > 0) ? item.FirePremium : null}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                disabled={true}
                                                            />
                                                        </Tooltip>
                                                    )}
                                                </>
                                            )}

                                            <SelectDropdown
                                                name="LD"
                                                label="Loading/Discounting"
                                                value={item.LD || null}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={LDTypes}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={productId === 131}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="LDAmount"
                                                label={(item.LD && item.LD.Id === 1) ? "Loading Amount" : "Discounting Amount"}
                                                value={item.LDAmount || null}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!(productId === 131 && item.LD && item.LD.Id && item.LD.Id > 0)}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="PrevPolicyNo"
                                                label={productId == 131 ? "Last Year Policy Number" : "Previous Policy No"}
                                                value={item.PrevPolicyNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={(productId == 2 || (productId === 131 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1 || item.PolicyType.PolicyTypeId == 2)) || (productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1)))}
                                                disabled={isDisabled || (productId === 131 && (IsDisabledExpiringPolicyNo || IsDisabledForSmeElements))}
                                                maxLength={150}
                                            />
                                            <TextInput
                                                name="PreviousBookingNo"
                                                label={productId == 131 ? "Last Year Booking ID" : "Previous BookingNo."}
                                                value={item.PreviousBookingNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={((!!Visible.PreviousBookingNo) && (item.PolicyType && (item.PolicyType.PolicyTypeId == 1 || item.PolicyType.PolicyTypeId == 2)))
                                                    || (productId === 131 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                disabled={isDisabled || (productId === 131 && (IsDisabledExpiringBookingId || IsDisabledForSmeElements))}
                                                maxLength={50}
                                            />
                                            <SelectDropdown
                                                name="ExpiringInsurer"
                                                label="Last Year Insurer"
                                                value={item.ExpiringInsurer}
                                                options={SupplierBySubProduct ? SupplierBySubProduct : supplierList}
                                                labelKeyInOptions="SupplierDisplayName"
                                                valueKeyInOptions='OldSupplierId'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={(productId === 131 && item.PolicyType && item.PolicyType.PolicyTypeId == 2)}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="InsuredName"
                                                label={productId === 131 ? "Insured Name (Name on Policy)" : "Insured Name"}
                                                value={item.InsuredName}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.InsuredName || productId === 131 || (productId === 2 && item.PolicyType && item.PolicyType.PolicyTypeId == 0)}
                                                maxLength={50}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="Name"
                                                label="Contact Person's Name"
                                                value={item.Name}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131}
                                                maxLength={50}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="Brokerage"
                                                label="% Brokerage"
                                                value={item.Brokerage}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.Brokerage && IsFosSmeAgentForBrokerage}
                                                maxLength={6}
                                                disabled={true}
                                            />
                                            <SelectDropdown
                                                name="CoverType"
                                                label="Cover Type"
                                                value={item.CoverType}
                                                options={options.CoverTypes}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={!!Visible.CoverType}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="BuildingSI"
                                                label="Structure SI"
                                                value={item.BuildingSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.BuildingSI}
                                                maxLength={10}
                                                disabled={(isDisabled || IsdisabledBuildingSI)}
                                            />
                                            <TextInput
                                                name="ContentSI"
                                                label="Content SI"
                                                value={item.ContentSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.ContentSI}
                                                maxLength={10}
                                                disabled={(isDisabled || IsdisabledContentSI)}
                                            />
                                            <SelectDropdown
                                                name="PropertyType"
                                                label="Property Type"
                                                value={item.PropertyType}
                                                options={IsSmePropertyProduct(productId, item) ? options.SmePropertyTypes : options.PropertyTypes}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={!!Visible.PropertyType || IsSmePropertyProduct(productId, item)}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="PropertyPurpose"
                                                label="Purpose"
                                                value={item.PropertyPurpose}
                                                options={options.PropertyPurpose}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                sm={6} md={4} xs={12}
                                                show={!!Visible.PropertyType}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="BookingType"
                                                label="Booking Type"
                                                value={item.BookingType}
                                                options={options.BookingTypes}
                                                labelKeyInOptions='label'
                                                valueKeyInOptions='value'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.BookingType}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="TermTenure"
                                                label="Term Tenure"
                                                value={item.TermTenure}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.TermTenure && item.Plan && item.Plan.PlanId == 3846}
                                                maxLength={9}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="TermSI"
                                                label="Term SI"
                                                value={item.TermSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.TermSI && item.Plan && item.Plan.PlanId == 3846}
                                                maxLength={9}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="CompanyName"
                                                label={productId === 131 ? "Company Name (Name on PAN)" : "Company Name"}
                                                value={item.CompanyName}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.CompanyName || productId === 131}
                                                maxLength={200}
                                                disabled={isDisabled}
                                            />
                                            {Visible.City &&
                                                <Grid item sm={6} md={4} xs={12} >
                                                    <Autocomplete
                                                        name="CityState"
                                                        label="City"
                                                        value={item.CityState || null}
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'CityState', value } }, item, index)}
                                                        options={options.cities}
                                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                                        renderInput={(params) => <TextField name="city" label="City" variant="outlined" {...params} />}
                                                        disabled={isDisabled}
                                                        fullWidth
                                                    />
                                                </Grid>
                                            }
                                            <SelectDropdown
                                                name="PolicyCategory"
                                                label="Policy Category"
                                                value={item.PolicyCategory || null}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={Data.PolicyCategory}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID == 19}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="ApplicationNo"
                                                label="Application number"
                                                value={item.ApplicationNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId == 7}
                                                maxLength={50}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="ODPremium"
                                                label="ODPremium"
                                                value={item.ODPremium}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.ODPremium}
                                                maxLength={16}
                                                disabled={isDisabled || IsTPChecked}
                                            />

                                            {Visible.InspectionRequired && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1) &&
                                                <Grid item sm={12} md={12} xs={12}>
                                                    {[<p className="emiTxt" key="100" >Inspection Required</p>,
                                                    ...(inspectionRequired.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.Medical_or_InspectionRequired == values.Id}
                                                            name="Medical_or_InspectionRequired"
                                                            disabled={isDisabled}
                                                        />
                                                    )))]}
                                                </Grid>
                                            }
                                            {Visible.IsRSA &&
                                                <Grid item sm={12} md={12} xs={12}>
                                                    {[<p className="emiTxt" key="100">Is RSA Required</p>,
                                                    ...isRSA.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.IsRSA == values.Id}
                                                            name="IsRSA"
                                                            disabled={isDisabled}
                                                        />
                                                    ))]}
                                                </Grid>
                                            }
                                            {productId === 117 && item.LeadSource && item.LeadSource === "Renewal" &&
                                                <Grid item sm={12} md={12} xs={12}>
                                                    {[<p className="emiTxt" key="100">IsTP</p>,
                                                    ...isTP.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.IsTP == values.Id}
                                                            name="IsTP"
                                                            disabled={isDisabled}
                                                        />
                                                    ))]}
                                                </Grid>
                                            }
                                            {Visible.DocRequired &&
                                                <Grid item sm={12} md={4} xs={12}>
                                                    {[<p className="emiTxt" key="101">Is DocRequired</p>,
                                                    ...isRSA.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.DocumentsRequired == values.Id}
                                                            name="DocumentsRequired"
                                                            disabled={isDisabled}
                                                        />
                                                    ))]}
                                                </Grid>
                                            }
                                            {Visible.DocRecieved &&
                                                <Grid item sm={12} md={4} xs={12}>
                                                    {[<p className="emiTxt" key="101">Is DocRecieved</p>,
                                                    ...isRSA.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.IsDocReceived == values.Id}
                                                            name="IsDocReceived"
                                                            disabled={isDisabled}
                                                        />
                                                    ))]}
                                                </Grid>
                                            }
                                            {Visible.Portability &&
                                                <Grid item sm={12} md={4} xs={12}>
                                                    {[<p className="emiTxt" key="101">Is Portability Case</p>,
                                                    <FormControlLabel
                                                        key={102}
                                                        // value={values.Id}
                                                        control={<Checkbox color="secondary" />}
                                                        // label={}
                                                        onChange={(e) => handleChangeForBooking(e, item, index)}
                                                        checked={item.Portability}
                                                        name="Portability"
                                                        disabled={isDisabled}
                                                    />
                                                    ]}
                                                </Grid>
                                            }
                                            <TextInput
                                                name="NoOfLives"
                                                label="No Of Lives"
                                                value={item.NoOfLives}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.NoOfLives}
                                                maxLength={8}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="NoOfEmployees"
                                                label="No Of Employees"
                                                value={item.NoOfEmployees}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.NoOfEmployee}
                                                maxLength={8}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="RegistrationNo"
                                                label="RegistrationNo"
                                                value={item.RegistrationNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.RegistrationNo}
                                                maxLength={20}
                                                disabled={isDisabled}
                                            />
                                            {!!Visible.Occupany &&
                                                <Grid item sm={6} md={4} xs={12}>
                                                    <Autocomplete
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'Occupation', value } }, item, index)}
                                                        id="Occupation"
                                                        options={getOccupationsList(item)}
                                                        name="Occupation"
                                                        value={item.Occupation}
                                                        getOptionLabel={(option) => (option.Name || '')}
                                                        disabled={isDisabled}
                                                        forcePopupIcon={true}
                                                        renderInput={(params) =>
                                                            <TextField {...params}
                                                                label={"Parent risk category"}
                                                                variant='outlined'
                                                            />}
                                                    />
                                                </Grid>
                                            }
                                            {(item.Occupation && ChildOccupancyList && ChildOccupancyList.length > 0 &&
                                                item.SubProduct && item.SubProduct.ID && item.SubProduct.ID != 19) &&
                                                <Grid item sm={6} md={4} xs={12}>
                                                    <Autocomplete
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'ChildOccupation', value } }, item, index)}
                                                        id="ChildOccupation"
                                                        options={ChildOccupancyList}
                                                        name="ChildOccupation"
                                                        value={item.ChildOccupation || null}
                                                        getOptionLabel={(option) => (option.Name || '')}
                                                        disabled={isDisabled}
                                                        forcePopupIcon={true}
                                                        renderInput={(params) =>
                                                            <TextField {...params}
                                                                label={"Child risk category"}
                                                                variant='outlined'
                                                            />}
                                                    />
                                                </Grid>
                                            }
                                            {(item.Occupation && ChildOccupancyList && ChildOccupancyList.length > 0 &&
                                                item.SubProduct && item.SubProduct.ID && item.SubProduct.ID == 19) &&
                                                <Grid item sm={6} md={4} xs={12}>
                                                    <Autocomplete
                                                        multiple
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'ChildOccupancies', value } }, item, index)}
                                                        id="checkboxes-tags-demo"
                                                        limitTags={1}
                                                        disableCloseOnSelect
                                                        size="small"
                                                        options={ChildOccupancyList}
                                                        value={item.ChildOccupancies || []}
                                                        name="ChildOccupancies"
                                                        getOptionLabel={(option) => (option.Name || '')}
                                                        disabled={isDisabled}
                                                        forcePopupIcon={true}
                                                        renderOption={(props, option, { selected }) => {
                                                            const { key, ...optionProps } = props;
                                                            return (
                                                                <li key={key} {...optionProps} className="AutoCompleteData">
                                                                    <Checkbox
                                                                        icon={icon}
                                                                        checkedIcon={checkedIcon}
                                                                        style={{ marginRight: 8 }}
                                                                        checked={selected}
                                                                    />
                                                                    <p> {option.Name}</p>
                                                                </li>
                                                            );
                                                        }}
                                                        renderInput={(params) =>
                                                            <TextField {...params}
                                                                label={"Child risk category"}
                                                                variant="outlined"
                                                                className="AutoComplete"
                                                            />}

                                                    />
                                                </Grid>
                                            }
                                            <TextInput
                                                name="OtherOccupany"
                                                label="Other Occupany"
                                                value={item.OtherOccupany}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.OtherOccupany}
                                                maxLength="50"
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="PolicyTerm"
                                                label={
                                                    productId === 101
                                                        ? "Policy Tenure (In Years)"
                                                        : ("Policy Term" + (productId === 131 ? "(In month)" : ""))
                                                }
                                                value={item.PolicyTerm}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.PolicyTerm || !!Visible.Tenure}
                                                maxLength={productId === 131 ? 3 : 2}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="PayTerm"
                                                label={productId === 131 ? "Installments" : "Pay Term"}
                                                value={item.PayTerm}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.PayTerm}
                                                maxLength={2}
                                                disabled={isDisabled}
                                            />
                                            <DatePicker
                                                name="PolicyStartDate"
                                                label="Policy StartDate"
                                                value={item.PolicyStartDate}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.PolicyStartDate}
                                                disabled={isDisabled}
                                            />
                                            <DatePicker
                                                name="PolicyEndDate"
                                                label="Policy EndDate"
                                                value={item.PolicyEndDate}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.PolicyEndDate}
                                                disabled={isDisabled || IsdisabledPED}
                                            />
                                            {IsSmeEngineeringSubproduct(item) &&
                                                <>
                                                    <TextInput
                                                        name="ProjectDuration"
                                                        label="Project Duration"
                                                        value={item.ProjectDuration || null}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={true}
                                                        disabled={isDisabled}
                                                    />

                                                    <SelectDropdown
                                                        name="BookingCategory"
                                                        label="Booking Category"
                                                        value={item.BookingCategory || null}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        options={Data.BookingCategory}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        sm={6} md={4} xs={12}
                                                        show={true}
                                                        disabled={isDisabled}
                                                    />
                                                </>
                                            }
                                            {
                                                !!( productId==131 && item && item.SubProduct && item.SubProduct.ID 
                                                && [186].indexOf(item.SubProduct.ID) !=-1 )&&
                                                <SelectDropdown
                                                name="PlanType"
                                                label="Plan Type"
                                                value={item.PlanType || null}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                options={Data.PlanType}
                                                labelKeyInOptions="Name"
                                                valueKeyInOptions="_all"
                                                sm={6} md={4} xs={12}
                                                show={true}
                                                disabled={isDisabled}
                                            />
                                            }
                                            <SelectDropdown
                                                name="IsSTP"
                                                label="STP/NSTP"
                                                value={item.IsSTP}
                                                options={options.IsSTPOptions}
                                                labelKeyInOptions='label'
                                                valueKeyInOptions='value'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 2}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="ShopTypeId"
                                                label="Shop Type"
                                                value={item.ShopTypeId}
                                                options={ShopTypes}
                                                labelKeyInOptions='ShopTypeName'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                disabled={isDisabled}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID === 8}
                                                sm={6} md={4} xs={12}
                                            />
                                            <TextInput
                                                name="RiderSI"
                                                label="Rider SI"
                                                value={item.RiderSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.RiderSI}
                                                maxLength={15}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="Rider"
                                                label="Rider"
                                                value={item.Rider}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.Rider}
                                                maxLength={100}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="ReferenceNo"
                                                label="Reference Number"
                                                value={item.ReferenceNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={(!!Visible.InspectionRequired) && item.Medical_or_InspectionRequired == 1 && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                maxLength={50}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="EmailID"
                                                label="Email ID"
                                                value={item.EmailID}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                disabled={isDisabled}
                                                maxLength={100}
                                                show={!!Visible.EmailID}
                                            />
                                            {(productId === 131 && item.SubProduct && item.SubProduct.ID === 14) &&
                                                <Grid item sm={6} md={4} xs={12}>
                                                    <Autocomplete
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'Association', value } }, item, index)}
                                                        id="Association"
                                                        options={Association}
                                                        name="Association"
                                                        value={item.Association}
                                                        getOptionLabel={(option) => (option.Name || '')}
                                                        disabled={isDisabled}
                                                        show={false}
                                                        renderInput={(params) =>
                                                            <TextField {...params}
                                                                label={"Association"}
                                                                variant='outlined'
                                                            />}
                                                    />
                                                </Grid>
                                            }
                                            <DatePicker
                                                name="DateOfInspection"
                                                label="Date of Inspection"
                                                value={item.DateOfInspection}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={(!!Visible.InspectionRequired) && item.Medical_or_InspectionRequired == 1 && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="InspectionStatus"
                                                label="Status:"
                                                value={item.InspectionStatus}
                                                options={InspectionType}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='_all'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={(!!Visible.InspectionRequired) && item.Medical_or_InspectionRequired == 1 && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="TransitType"
                                                label="Transit Type"
                                                value={item.TransitType}
                                                options={TransitTypes}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={!!Visible.TransitType && item.SubProduct && item.SubProduct.ID == 13}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            {IsSmeMarineAnualOpen(item) &&
                                                <Grid item sm={6} md={4} xs={12} >
                                                    <Autocomplete
                                                        name="TransitFrom"
                                                        label="TransitFrom"
                                                        value={item.TransitFrom || null}
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'TransitFrom', value } }, item, index)}
                                                        options={options.cities}
                                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                                        renderInput={(params) => <TextField name="city" label="Transit From" variant="outlined" {...params} />}
                                                        disabled={isDisabled}
                                                        fullWidth
                                                    />
                                                </Grid>
                                            }
                                            {IsSmeMarineAnualOpen(item) &&
                                                <Grid item sm={6} md={4} xs={12} >
                                                    <Autocomplete
                                                        name="TransitTo"
                                                        label="TransitTo"
                                                        value={item.TransitTo || null}
                                                        onChange={(event, value) => handleChangeForBooking({ target: { name: 'TransitTo', value } }, item, index)}
                                                        options={options.cities}
                                                        getOptionLabel={(option) => (option.CityStateName || '')}
                                                        isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                                        renderInput={(params) => <TextField name="city" label="Transit To" variant="outlined" {...params} />}
                                                        disabled={isDisabled}
                                                        fullWidth
                                                    />
                                                </Grid>
                                            }
                                            <SelectDropdown
                                                name="OccupationType"
                                                label="Occupation Type"
                                                value={item.OccupationType}
                                                options={OccupationTypes}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeMarineAnualOpen(item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="ManufacturerTraderName"
                                                label={(item.OccupationType && item.OccupationType === "Manufacturer") ? "Manufacturer Name" : "Trader Name"}
                                                value={item.ManufacturerTraderName}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeMarineAnualOpen(item) && item.OccupationType && (item.OccupationType === "Manufacturer" || item.OccupationType === "Trader")}
                                                maxLength={50}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="ManufacturerTraderContactNo"
                                                label={(item.OccupationType && item.OccupationType === "Manufacturer") ? "Manufacturer Contact No" : "Trader Contact No"}
                                                value={item.ManufacturerTraderContactNo}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeMarineAnualOpen(item) && item.OccupationType && (item.OccupationType === "Manufacturer" || item.OccupationType === "Trader")}
                                                maxLength={12}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="ConstitutionOfBusiness"
                                                label="Constitution Of Business"
                                                value={item.ConstitutionOfBusiness}
                                                options={ConstitutionOfBusinesses}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeMarineAnualOpen(item) || IsSmeWC(item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <MultipleSelectDropdown
                                                name="Inclusion"
                                                label="Inclusions"
                                                value={item.Inclusion || []}
                                                options={Inclusions}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='_all'
                                                handleChangeMultiple={(e) => handleChangeForBooking(e, item, index)}
                                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                                show={IsSmeMarineAnualOpen(item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                shrink={{ shrink: item.Inclusion && item.Inclusion.length > 0 }}
                                            />
                                            {productId === 131 && item.SubProduct?.ID === 13 && (
                                                item.TransitType === "Annual Open" ? (
                                                    <MultipleSelectDropdown
                                                        name="ShipmentType"
                                                        label="Shipment Type"
                                                        value={item.ShipmentType || []}
                                                        options={ShipmentTypes}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        handleChangeMultiple={(e) => handleChangeForBooking(e, item, index)}
                                                        renderValue={(selected) => Array.isArray(selected) ? selected.map((s) => s.Name).join(", ") : ""}
                                                        sm={6} md={4} xs={12}
                                                        disabled={isDisabled}
                                                        shrink={{ shrink: item.ShipmentType?.length > 0 }}
                                                    />
                                                ) : (
                                                    <SelectDropdown
													name="ShipmentType"
													label="Shipment Type"
													value={item.ShipmentType}
													options={ShipmentTypes}
													labelKeyInOptions='Name'
													valueKeyInOptions='Id'
													handleChange={(e) => handleChangeForBooking(e, item, index)}
													show={productId === 131 && item.SubProduct && item.SubProduct.ID === 13}
													sm={6} md={4} xs={12}
													disabled={isDisabled}
												/>
                                                )
                                            )}
                                            <TextInput
                                                name="InlandSI"
                                                label="Inland SI"
                                                value={item.InlandSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID && item.SubProduct.ID === 13 && item.TransitType === "Annual Open" && IsShipmentTypeSelected(item, 'Inland')}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={15}
                                            />
                                            <TextInput
                                                name="ImportSI"
                                                label="Import SI"
                                                value={item.ImportSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID && item.SubProduct.ID === 13 && item.TransitType === "Annual Open" && IsShipmentTypeSelected(item, 'Import')}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={15}
                                            />
                                            <TextInput
                                                name="ExportSI"
                                                label="Export SI"
                                                value={item.ExportSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID && item.SubProduct.ID === 13 && item.TransitType === "Annual Open" && IsShipmentTypeSelected(item, 'Export')}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={15}
                                            />
                                            <TextInput
                                                name="MerchantTradingSI"
                                                label="Merchant Trading SI"
                                                value={item.MerchantTradingSI}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID && item.SubProduct.ID === 13 && item.TransitType === "Annual Open" && IsShipmentTypeSelected(item, 'Merchant Trading')}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={15}
                                            />

                                           
                                            <SelectDropdown
                                                name="MarineCoverType"
                                                label="Cover Type"
                                                value={item.MarineCoverType}
                                                options={MarineCoverTypes}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={productId === 131 && item.SubProduct && item.SubProduct.ID === 13}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <SelectDropdown
                                                name="MedicalExtension"
                                                label="Medical Extension"
                                                value={item.MedicalExtension}
                                                options={MedicalExtensions}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='Id'
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeWC(item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                            />
                                            <TextInput
                                                name="OtherMedicalExtension"
                                                label="Other Medical Extension"
                                                value={item.OtherMedicalExtension}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsSmeWC(item) && item.MedicalExtension && item.MedicalExtension === "Other"}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                maxLength={10}
                                            />
                                            <MultipleSelectDropdown
                                                name="WorkerType"
                                                label="Worker Type"
                                                value={item.WorkerType || []}
                                                options={WorkerTypes}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='_all'
                                                handleChangeMultiple={(e) => handleChangeForBooking(e, item, index)}
                                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                                show={IsSmeWC(item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                shrink={{ shrink: item.WorkerType && item.WorkerType.length > 0 }}
                                            />
                                            {
                                                IsSmeWC(item) && Array.isArray(item.WorkerType) && item.WorkerType.map((worker, workerIndex) => {
                                                    return (
                                                        <Fragment key={"KeyWorker" + item.WorkerType.length + workerIndex}>
                                                            <TextInput
                                                                name={GetNameOfWorker(worker.Name, true)}
                                                                label={"No Of " + worker.Name + " Workers"}
                                                                value={GetNumOfWorker(item, worker.Name, true)}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                sm={4} md={4} xs={12}
                                                                disabled={isDisabled}
                                                                maxLength={10}
                                                            />
                                                            <TextInput
                                                                name={GetNameOfWorker(worker.Name, false)}
                                                                label={"Salary " + worker.Name + " Worker Per Month"}
                                                                value={GetNumOfWorker(item, worker.Name, false)}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                sm={4} md={4} xs={12}
                                                                disabled={isDisabled}
                                                                maxLength={10}
                                                            />
                                                        </Fragment>
                                                    )
                                                })
                                            }
                                            <MultipleSelectDropdown
                                                name="InsuredScope"
                                                label="Insured Scope"
                                                value={item.InsuredScope || []}
                                                options={Data.InsuredScopes}
                                                labelKeyInOptions='Name'
                                                valueKeyInOptions='_all'
                                                handleChangeMultiple={(e) => handleChangeForBooking(e, item, index)}
                                                renderValue={(selected) => selected.map((s) => { return s['Name'] }).join(', ')}
                                                show={IsSmePropertyProduct(productId, item)}
                                                sm={6} md={4} xs={12}
                                                disabled={isDisabled}
                                                shrink={{ shrink: item.InsuredScope && item.InsuredScope.length > 0 }}
                                            />
                                            {
                                                IsSmePropertyProduct(productId, item) && Array.isArray(item.InsuredScope) && item.InsuredScope.map((insuredScope, indx) => {
                                                    return (
                                                        <Fragment key={"KeyInsuredScope" + item.InsuredScope.length + indx}>
                                                            <TextInput
                                                                name={GetInsuredScopeName(insuredScope.Name)}
                                                                label={insuredScope.Name + " Value"}
                                                                value={GetInsuredScopeValue(item, insuredScope.Name)}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                sm={4} md={4} xs={12}
                                                                disabled={isDisabled}
                                                                maxLength={10}
                                                            />
                                                        </Fragment>
                                                    )
                                                })
                                            }
                                            {productId === 131 &&
                                                <Grid item sm={12} md={4} xs={12}>
                                                    {[<p className="emiTxt" key="131">Co-Insurance</p>,
                                                    ...CoInsuranceTypes.map((values) => (
                                                        <FormControlLabel
                                                            key={values.Id}
                                                            value={values.Id}
                                                            control={<Radio color="primary" />}
                                                            label={values.Name}
                                                            onChange={(e) => handleChangeForBooking(e, item, index)}
                                                            checked={item.CoInsurance == values.Id}
                                                            name="CoInsurance"
                                                            disabled={isDisabled}
                                                        />
                                                    ))]}
                                                </Grid>
                                            }
                                            <TextInput
                                                name="LeadersPercentage"
                                                label="Leader Supplier's Percentage"
                                                value={item.LeadersPercentage}
                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                show={IsCoInsurer(item)}
                                                maxLength={3}
                                                disabled={isDisabled}
                                            />
                                        </Grid>
                                        {(productId === 139) &&
                                            <Grid container spacing={3}>
                                                <TextInput
                                                    name="TPPremium"
                                                    label="TPPremium"
                                                    value={item.TPPremium}
                                                    handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                    show={!!Visible.TPPremium}
                                                    maxLength={6}
                                                    disabled={isDisabled}
                                                />
                                                <TextInput
                                                    name="AddonPremium"
                                                    label="AddonPremium"
                                                    value={item.AddonPremium}
                                                    handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                    show={!!Visible.AddonPremium}
                                                    maxLength={6}
                                                    disabled={isDisabled}
                                                />
                                                <TextInput
                                                    name="ServiceTax"
                                                    label="ServiceTax"
                                                    value={item.ServiceTax}
                                                    handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                    show={!!Visible.ServiceTax}
                                                    maxLength={6}
                                                    disabled={isDisabled}
                                                />
                                            </Grid>
                                        }
                                        {IsCoInsurer(item) &&
                                            <Grid container spacing={3}>
                                                {
                                                    <Grid item sm={12} md={12} xs={12} className="pd-0 pd-top">
                                                        <Button
                                                            variant="text"
                                                            color="secondary"
                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'AddFollowerSupplier', index } }, item, index)}
                                                            disabled={isDisabled}
                                                            className="addbtn"
                                                        >
                                                            <AddCircleOutline /> Add Follower Supplier
                                                        </Button>
                                                    </Grid>
                                                }
                                                {item.FollowerSuppliers && Array.isArray(item.FollowerSuppliers) && item.FollowerSuppliers.map((followerSuppliers, followerIndex) => {
                                                    return (
                                                        <>
                                                            <Grid item sm={6} md={6} xs={12}>
                                                                <Autocomplete
                                                                    onChange={(event, value) => handleChangeForBooking({ target: { name: 'FollowerSuppliers', value, index: followerIndex } }, item, index)}
                                                                    id="FollowerSuppliers"
                                                                    options={(productId == 131 && IsShowSupplierBySubProduct && SupplierBySubProduct) ? SupplierBySubProduct : supplierList}
                                                                    name="FollowerSuppliers"
                                                                    value={followerSuppliers.FollowerSupplier}
                                                                    getOptionLabel={(option) => (option.SupplierDisplayName || '')}
                                                                    getOptionDisabled={(option) => DisableFollowerSupplier(option, { ...item })}
                                                                    disabled={isDisabled}
                                                                    show={true}
                                                                    renderInput={(params) =>
                                                                        <TextField {...params}
                                                                            label="Follower Supplier"
                                                                            variant='outlined'
                                                                        />}
                                                                />
                                                            </Grid>
                                                            <TextInput
                                                                name="FollowerPercentage"
                                                                label="Follower's Percentage"
                                                                value={followerSuppliers.FollowerPercentage ? followerSuppliers.FollowerPercentage : ""}
                                                                handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: followerIndex } }, item, index)}
                                                                show={true}
                                                                sm={4} md={4} xs={12}
                                                                maxLength={3}
                                                                disabled={isDisabled}
                                                            />
                                                            {
                                                                <Grid item sm={2} md={2} xs={12} className="text-center">
                                                                    <Button
                                                                        onClick={(e) => handleChangeForBooking({ target: { name: 'HandleFollowerSupplierDelete', index: followerIndex } }, item, index)}
                                                                        disabled={isDisabled}
                                                                    >
                                                                        <DeleteIcon className="red" />
                                                                    </Button>
                                                                </Grid>
                                                            }
                                                        </>
                                                    )
                                                })}
                                            </Grid>
                                        }
                                        {!!(productId == 131 && item.PayTerm && item.PayTerm > 1) &&
                                            <Grid container spacing={3}>
                                                {
                                                    <Grid item sm={12} md={12} xs={12} className="pd-0 pd-top">
                                                        <Button
                                                            variant="text"
                                                            color="secondary"
                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'HandleAddNewInstallment', index } }, item, index)}
                                                            disabled={isDisabled}
                                                            className="addbtn"
                                                        >
                                                            <AddCircleOutline />  Add Installments
                                                        </Button>
                                                    </Grid>
                                                }
                                                {
                                                    item.InstallmentsData && Array.isArray(item.InstallmentsData) && item.InstallmentsData.map((installment, installmentIndex) => {
                                                        return (
                                                            <Fragment key={"Key" + item.InstallmentsData.length + installmentIndex}>
                                                                <DatePicker
                                                                    name="InstallmentDate"
                                                                    label={"Installment " + (installmentIndex + 1)}
                                                                    value={installment.Date ? new Date(installment.Date) : null}
                                                                    handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: installmentIndex } }, item, index)}
                                                                    sm={4} md={4} xs={12}
                                                                    disabled={isDisabled}
                                                                    default={null}
                                                                />
                                                                <TextInput
                                                                    name="InstallmentAmount"
                                                                    label="Amount"
                                                                    value={installment.Amount}
                                                                    handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: installmentIndex } }, item, index)}
                                                                    sm={4} md={4} xs={12}
                                                                    disabled={isDisabled}
                                                                    maxLength={15}
                                                                />
                                                                {
                                                                    <Grid item sm={2} md={2} xs={12} className="text-center">
                                                                        <Button
                                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'HandleInstallmentDelete', index: installmentIndex } }, item, index)}
                                                                            disabled={isDisabled}
                                                                        >
                                                                            <DeleteIcon className="red" />
                                                                        </Button>
                                                                    </Grid>
                                                                }
                                                            </Fragment>
                                                        )
                                                    })
                                                }
                                            </Grid>
                                        }
                                        {(IsSmeWC(item) || IsSmePropertyProduct(productId, item) || IsSmeEngineeringSubproduct(item))  &&
                                            <Grid container spacing={3}>
                                                {
                                                    <Grid item sm={12} md={12} xs={12} className="pd-0 pd-top">
                                                        <Button
                                                            variant="text"
                                                            color="secondary"
                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'HandleAddRiskLocation', index } }, item, index)}
                                                            disabled={isDisabled}
                                                            className="addbtn"
                                                        >
                                                            <AddCircleOutline />  Add Risk Location
                                                        </Button>
                                                    </Grid>
                                                }
                                                {
                                                    Array.isArray(item.RiskLocations) && item.RiskLocations.map((riskLocation, riskIndex) => {
                                                        return <>
                                                            <TextInput
                                                                name="RiskAddress"
                                                                label="Address"
                                                                value={riskLocation.RiskAddress}
                                                                handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: riskIndex } }, item, index)}
                                                                sm={4} md={4} xs={12}
                                                                disabled={isDisabled}
                                                                maxLength={100}
                                                            />
                                                            <TextInput
                                                                name="RiskCityPincode"
                                                                label="Pincode"
                                                                value={riskLocation.PinCode}
                                                                handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: riskIndex } }, item, index)}
                                                                sm={4} md={4} xs={12}
                                                                disabled={isDisabled}
                                                                maxLength={6}
                                                            />
                                                            <Grid item sm={3} md={3} xs={12} >
                                                                <Autocomplete
                                                                    name="RiskLocationCity"
                                                                    label="City"
                                                                    value={riskLocation.City || null}
                                                                    onChange={(event, value) => handleChangeForBooking({ target: { name: 'RiskLocationCity', value, index: riskIndex } }, item, index)}
                                                                    options={options.cities}
                                                                    getOptionLabel={(option) => (option.CityStateName || '')}
                                                                    isOptionEqualToValue={(option, value) => (option.CityID == value.CityID)}
                                                                    renderInput={(params) => <TextField name="city" label="City" variant="outlined" {...params} />}
                                                                    disabled={isDisabled}
                                                                />
                                                            </Grid>
                                                            {
                                                                <Grid item sm={1} md={1} xs={12} className="text-center">
                                                                    <Button
                                                                        onClick={(e) => handleChangeForBooking({ target: { name: 'HandleRiskLocationDelete', index: riskIndex } }, item, index)}
                                                                        disabled={isDisabled}
                                                                    >
                                                                        <DeleteIcon className="red" />
                                                                    </Button>
                                                                </Grid>
                                                            }
                                                        </>;
                                                    })
                                                }
                                            </Grid>
                                        }
                                        {!!(productId == 131 && item.SubProduct && SmeGhiSubProducts.indexOf(item.SubProduct.ID) > -1 && item.SumInsuredType && item.SumInsuredType.Id == 2) &&
                                            <Grid container spacing={3}>
                                                {
                                                    <Grid item sm={12} md={12} xs={12} className="pd-0 pd-top">
                                                        <Button
                                                            variant="text"
                                                            color="secondary"
                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'HandleAddNewGrade', index } }, item, index)}
                                                            disabled={isDisabled}
                                                            className="addbtn"
                                                        >
                                                            <AddCircleOutline />  Add Grades
                                                        </Button>
                                                    </Grid>
                                                }
                                                {
                                                    item.Grades && Array.isArray(item.Grades) && item.Grades.map((grade, gradeIndex) => {
                                                        return (
                                                            <>
                                                                <TextInput
                                                                    name="GradeName"
                                                                    value={grade.Name}
                                                                    sm={4} md={4} xs={12}
                                                                    disabled={true}
                                                                    maxLength={12}
                                                                />
                                                                <TextInput
                                                                    name="GradeSumInsured"
                                                                    label="Sum Insured"
                                                                    value={(grade && grade.SumInsured) ? grade.SumInsured : ""}
                                                                    handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: gradeIndex } }, item, index)}
                                                                    sm={4} md={4} xs={12}
                                                                    disabled={isDisabled}
                                                                    maxLength={15}
                                                                />
                                                                {
                                                                    <Grid item sm={2} md={2} xs={12} className="text-center">
                                                                        <Button
                                                                            onClick={(e) => handleChangeForBooking({ target: { name: 'HandleGradeDelete', index: gradeIndex } }, item, index)}
                                                                            disabled={isDisabled}
                                                                        >
                                                                            <DeleteIcon className="red" />
                                                                        </Button>
                                                                    </Grid>
                                                                }
                                                            </>
                                                        )
                                                    })
                                                }
                                            </Grid>
                                        }
                                        <h3 className="paymentDetails-title"
                                            onClick={() => {
                                                setOrderList(prevState => prevState.map((o, i) => {
                                                    if (i === index) { o.ShowPaymentDetails = !o.ShowPaymentDetails; } return o;
                                                }));
                                                setSubPaymentModes(item);
                                            }}
                                        > Payment Details </h3>
                                        {item.ShowPaymentDetails &&
                                            <>
                                                <Grid container spacing={3}>

                                                    <SelectDropdown
                                                        name="paymentMode"
                                                        label="Payment Mode"
                                                        value={item.payment.PaymentStatus}
                                                        options={options.paymentMode}
                                                        labelKeyInOptions="PaymentModeName"
                                                        valueKeyInOptions="PaymentModeValue"
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'PaymentStatus')}
                                                        sm={6} md={4} xs={12}
                                                        show={true}
                                                        disabled={isDisabled}
                                                    />
                                                    <SelectDropdown
                                                        name="subPaymentMode"
                                                        label="Payment Sub Mode"
                                                        value={item.payment.PaymentSubStatus}
                                                        options={SubPaymentModesOptions}
                                                        labelKeyInOptions="PaymentModeName"
                                                        valueKeyInOptions="PaymentModeValue"
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'PaymentSubStatus')}
                                                        sm={6} md={4} xs={12}
                                                        show={((productId === 2 || productId === 131) && !IsNotPaySubMode) && !(productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1))}
                                                        disabled={isDisabled}
                                                    />
                                                    <SelectDropdown
                                                        name="paymentFrequency"
                                                        label="Payment Frequency"
                                                        value={item.payment.PaymentPeriodicity}
                                                        options={options.paymentFrequency}
                                                        labelKeyInOptions="label"
                                                        valueKeyInOptions="value"
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'PaymentPeriodicity')}
                                                        sm={6} md={4} xs={12}
                                                        show={(!!Visible.PaymentPeriodicity) && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="ChequeNo"
                                                        label="Cheque No."
                                                        value={item.payment.ChequeNo}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'ChequeNo',)}
                                                        show={(item.payment.PaymentStatus == 5001) && !(productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1))}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="TransRefNo"
                                                        label="Transaction Number"
                                                        value={item.payment.TransRefNo}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'TransRefNo')}
                                                        show={(item.payment.PaymentStatus != 5001 && !!Visible.TransRefNo) && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        disabled={isDisabled}
                                                    />

                                                    {Visible.EMI && (productId === 106 || productId === 118 || productId === 130 || productId === 114) &&
                                                        <Grid item sm={12} md={12} xs={12}>
                                                            {[<p className="emiTxt" key="100">EMI</p>,
                                                            ...(EMI.map((values) => (
                                                                <FormControlLabel
                                                                    key={values.Id}
                                                                    value={values.Id}
                                                                    control={<Radio color="primary" />}
                                                                    label={values.Name}
                                                                    onChange={(e) => handleChangeForBooking(e, item, index)}
                                                                    checked={item.payment.IsEMI == values.Id}
                                                                    name="emi"
                                                                    disabled={isDisabled}
                                                                />
                                                            )))]}

                                                        </Grid>
                                                    }
                                                    <SelectDropdown
                                                        name="PaymentSource"
                                                        label="Payment Source"
                                                        value={item.PaymentSource}
                                                        options={options.paymentSource}
                                                        labelKeyInOptions="label"
                                                        valueKeyInOptions="value"
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        sm={6} md={4} xs={12}
                                                        show={(!!Visible.PaymentSource) && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="BankNameBranch"
                                                        label="Bank Name"
                                                        value={(item.payment && item.payment.BankNameBranch)}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index, 'payment', 'BankNameBranch')}
                                                        show={(!!Visible.BankName) && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        maxLength={255}
                                                        disabled={isDisabled}
                                                    />
                                                    <SelectDropdown
                                                        name="BookingFrom"
                                                        label="Booking From"
                                                        value={item.BookingFrom || null}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        options={(item.SubProduct && ([1, 2, 3, 4].indexOf(item.SubProduct.ID) != -1)) ? Data.BookingFromTypes : Data.BookingFromTypesSME}
                                                        labelKeyInOptions="Name"
                                                        valueKeyInOptions="_all"
                                                        sm={6} md={4} xs={12}
                                                        show={productId === 131}
                                                        disabled={isDisabled}
                                                    />
                                                    {item.BookingFrom && item.BookingFrom.Id && item.BookingFrom.Id == "CJ" && <TextInput
                                                        name="QuoteId"
                                                        label="Quote Id"
                                                        value={item.QuoteId || ""}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={item.BookingFrom && productId === 131 && item.SubProduct && item.SubProduct.ID && ([1].indexOf(item.SubProduct.ID) != -1) && item.BookingFrom.Id == "CJ"}
                                                        maxLength={255}
                                                        disabled={isDisabled}
                                                    />}
                                                    <TextInput
                                                        name="ApplicationNo"
                                                        label="Application number"
                                                        value={item.ApplicationNo}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={(productId !== 7 && (!!Visible.ApplicationNo)) && !(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        maxLength={255}
                                                        disabled={isDisabled}
                                                    />
                                                    <DatePicker
                                                        name="PaymentDate"
                                                        label="Payment Date"
                                                        value={item.PaymentDate}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={(!!Visible.PaymentDate) && !(productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1))}
                                                        disabled={isDisabled}
                                                    />
                                                    <DatePicker
                                                        name="IssuanceDate"
                                                        label="Issuance Date"
                                                        value={item.IssuanceDate}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={(!!Visible.IssuanceDate) && !(productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1))}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="InstallmentPaid"
                                                        label="Installment"
                                                        value={item.InstallmentPaid}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={(!!Visible.Installment) && !(productId === 117 && item.PolicyType && (item.PolicyType.PolicyTypeId == 1))}
                                                        maxLength={2}
                                                        disabled={true}
                                                    />
                                                    {Visible.EMI && !(productId === 106 || productId === 118 || productId === 130 || productId === 114) &&
                                                        <Grid item sm={12} md={12} xs={12}>

                                                            <p className="emiTxt"> EMI: </p>
                                                            <FormControlLabel
                                                                value={1}
                                                                control={<Radio color="primary" />}
                                                                label="Yes"
                                                                onClick={(e) => !isDisabled ? handleChangeForBooking(e, item, index, 'payment', 'IsEMI') : null}
                                                                checked={item.payment.IsEMI == 1}
                                                                name="emi"
                                                                disabled={isDisabled}

                                                            />
                                                            <FormControlLabel
                                                                value={0}
                                                                control={<Radio color="primary" />}
                                                                label="No"
                                                                onClick={(e) => !isDisabled ? handleChangeForBooking(e, item, index, 'payment', 'IsEMI') : null}
                                                                checked={item.payment.IsEMI == 0}
                                                                name="emi"
                                                                disabled={isDisabled}
                                                            />

                                                        </Grid>
                                                    }
                                                    <TextInput
                                                        name="PolicyNo"
                                                        label="Policy No"
                                                        value={item.PolicyNo}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={productId !== 7 && !!Visible.PolicyNo}
                                                        maxLength={100}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="ProposalNo"
                                                        label="ProposalNo"
                                                        value={item.ProposalNo}
                                                        handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                        show={productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1}
                                                        maxLength={16}
                                                        disabled={isDisabled}
                                                    />
                                                    <TextInput
                                                        name="SalesAgent"
                                                        label={productId == 131 ? "Primary Agent" : "Sales Agent"}
                                                        value={(item.SalesAgentDetails && item.SalesAgentDetails.EmployeeId != '') ? `${item.SalesAgentDetails.UserName}(${item.SalesAgentDetails.EmployeeId})` : ''}
                                                        show={!(productId === 117 && item.PolicyType && item.PolicyType.PolicyTypeId == 1)}
                                                        handleChange={() => { }}
                                                        disabled={true}
                                                    />
                                                    <TextInput
                                                        name="PrimaryAgentSharePercentage"
                                                        label="Share Percentage"
                                                        value={item.PrimaryAgentSharePercentage}
                                                        show={productId == 131}
                                                        disabled={true}
                                                        maxLength={6}
                                                    />
                                                    {IsSmePos(item) &&
                                                        <>
                                                            <TextInput
                                                                name="PAN"
                                                                label="PAN"
                                                                value={item.PAN}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                show={true}
                                                                maxLength={10}
                                                                disabled={isDisabled}
                                                            />
                                                            <TextInput
                                                                name="GST"
                                                                label="GSTIN"
                                                                value={item.GST}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                show={true}
                                                                maxLength={15}
                                                                disabled={isDisabled}
                                                            />
                                                            <TextInput
                                                                name="CIN"
                                                                label="CIN"
                                                                value={item.CIN}
                                                                handleChange={(e) => handleChangeForBooking(e, item, index)}
                                                                show={true}
                                                                maxLength={21}
                                                                disabled={isDisabled}
                                                            />
                                                        </>}
                                                    {(productId === 131) &&
                                                        <Grid item sm={6} md={4} xs={12}>
                                                            <Button
                                                                disabled={isDisabled}
                                                                fullWidth={true}
                                                                color="secondary"
                                                                className="fileUploadAndViewBtn"
                                                                component="span"
                                                                startIcon="Upload Documents"
                                                                onClick={() => OpenUploadDocuments(item.leadId, item.SubProduct.ID, IsSmePos(item), item.TransitType)}
                                                            >
                                                            </Button>
                                                        </Grid>
                                                    }
                                                </Grid>
                                                {(productId == 131) &&
                                                    <Grid container spacing={3}>
                                                        {
                                                            <Grid item sm={12} md={12} xs={12} >
                                                                <Button
                                                                    variant="text"
                                                                    color="secondary"
                                                                    onClick={(e) => handleChangeForBooking({ target: { name: 'HandleAddNewSalesPartner', index } }, item, index)}
                                                                    disabled={isDisabled}
                                                                    className={isDisabled ? "addbtn agentTxtBold" : "addbtn"}
                                                                >
                                                                    <AddCircleOutline /> Add Secondary Sales Agent
                                                                </Button>
                                                            </Grid>
                                                        }
                                                        {item.SalesPartners && Array.isArray(item.SalesPartners) && item.SalesPartners.map((salesPartner, partnerIndex) => {
                                                            return (
                                                                <>
                                                                    <Grid item sm={6} md={6} xs={12}>
                                                                        <Autocomplete
                                                                            loading={true}
                                                                            loadingText={IsLoading ? 'Loading...' : 'No options'}
                                                                            onChange={(event, value) => handleChangeForBooking({ target: { name: 'SalesPartners', value, index: partnerIndex } }, item, index)}
                                                                            id="SalesPartner"
                                                                            options={SalesPartners}
                                                                            name="SalesPartner"
                                                                            value={salesPartner || null}
                                                                            getOptionLabel={(option) => (option.UserName) || ''}
                                                                            getOptionDisabled={(option) => DisableSelectedSalesPartnerOrSalesSpecialist(option, item)}
                                                                            disabled={isDisabled}
                                                                            onInputChange={(event, inputSalesPartner) => {
                                                                                setIsLoading(true);
                                                                                setInputSalesPartner(inputSalesPartner);

                                                                                if (!inputSalesPartner) {
                                                                                    setIsLoading(false);
                                                                                    let partner = { SharePercentage: "", UserId: "0", UserName: "" };
                                                                                    OnSalesPartnerChanged('SalesPartners', partner, partnerIndex, item, index);
                                                                                }
                                                                            }}
                                                                            renderInput={(params) =>
                                                                                <TextField {...params}
                                                                                    label="Secondary Sales Agent"
                                                                                    variant='outlined'
                                                                                />}
                                                                        />
                                                                    </Grid>
                                                                    <TextInput
                                                                        name="OnSalesPartnerSharePercentageChanged"
                                                                        label="Share Percentage"
                                                                        value={(salesPartner && salesPartner.SharePercentage) ? salesPartner.SharePercentage : ""}
                                                                        handleChange={(e) => handleChangeForBooking({ target: { name: e.target.name, value: e.target.value, index: partnerIndex } }, item, index)}
                                                                        show={true}
                                                                        sm={4} md={4} xs={12}
                                                                        disabled={isDisabled}
                                                                        maxLength={6}
                                                                    />
                                                                    {
                                                                        <Grid item sm={2} md={2} xs={12}>
                                                                            <Button
                                                                                onClick={(e) => handleChangeForBooking({ target: { name: 'HandleSalesPartnerDelete', index: partnerIndex } }, item, index)}
                                                                                disabled={isDisabled}
                                                                            >
                                                                                <DeleteIcon className="red" />
                                                                            </Button>
                                                                        </Grid>
                                                                    }
                                                                </>
                                                            )
                                                        })}
                                                        {
                                                            <Grid item sm={12} md={12} xs={12} className="pdTop-0">
                                                                <Button
                                                                    variant="text"
                                                                    color="secondary"
                                                                    onClick={(e) => handleChangeForBooking({ target: { name: 'HandleAddNewSalesSpecialist', index } }, item, index)}
                                                                    disabled={isDisabled}
                                                                    className={isDisabled ? "agentTxtBold" : ""}

                                                                >
                                                                    <AddCircleOutline /> Add Sales Specialist
                                                                </Button>
                                                            </Grid>
                                                        }
                                                        {item.SalesSpecialists && Array.isArray(item.SalesSpecialists) && item.SalesSpecialists.map((salesSpecialist, specialistIndex) => {
                                                            return (
                                                                <>
                                                                    <Grid item sm={6} md={6} xs={12}>
                                                                        <Autocomplete
                                                                            loading={true}
                                                                            loadingText={IsLoading ? 'Loading...' : 'No options'}
                                                                            onChange={(event, value) => handleChangeForBooking({ target: { name: 'SalesSpecialists', value, index: specialistIndex } }, item, index)}
                                                                            id="SalesSpecialist"
                                                                            options={SalesSpecialists}
                                                                            name="SalesSpecialist"
                                                                            value={salesSpecialist || null}
                                                                            getOptionLabel={(option) => (option.UserName) || ''}
                                                                            getOptionDisabled={(option) => DisableSelectedSalesPartnerOrSalesSpecialist(option, item)}
                                                                            disabled={isDisabled}
                                                                            onInputChange={(event, inputSalesSpecialist) => {
                                                                                setIsLoading(true);
                                                                                setInputSalesSpecialist(inputSalesSpecialist);

                                                                                if (!inputSalesSpecialist) {
                                                                                    setIsLoading(false);
                                                                                    let specialist = { SharePercentage: "", UserId: "0", UserName: "" };
                                                                                    OnSalesSpecialistChanged('SalesSpecialists', specialist, specialistIndex, item, index);
                                                                                }
                                                                            }}
                                                                            renderInput={(params) =>
                                                                                <TextField {...params}
                                                                                    label="Sales Specialist"
                                                                                    variant='outlined'
                                                                                />}
                                                                        />
                                                                    </Grid>
                                                                    {
                                                                        <Grid item sm={2} md={2} xs={12}>
                                                                            <Button
                                                                                onClick={(e) => handleChangeForBooking({ target: { name: 'HandleSalesSpecialistDelete', index: specialistIndex } }, item, index)}
                                                                                disabled={isDisabled}
                                                                            >
                                                                                <DeleteIcon className="red" />
                                                                            </Button>
                                                                        </Grid>
                                                                    }
                                                                </>
                                                            )
                                                        })}
                                                    </Grid>
                                                }
                                            </>
                                        }
                                        <SelectDropdown
                                            name="SubStatus"
                                            label="Lead SubStatus"
                                            value={item.SubStatus}
                                            options={options.SubStatusList}
                                            labelKeyInOptions="Name"
                                            valueKeyInOptions="_all"
                                            handleChange={(e) => handleChangeForBooking(e, item, index)}
                                            sm={6} md={4} xs={12}
                                            show={isDisabled && payment == 1 && !!Visible.SubStatus}
                                        />
                                        <Grid item sm={12} md={12} xs={12} className="text-center" >
                                            {!isDisabled && <Button className="editBtn" onClick={() => CancelEditOrder(index)}>Cancel</Button>}
                                            {isDisabled && <Button className="editBtn" onClick={() => EditClicked(item, index)}>Edit</Button>}
                                            {
                                                !isDisabled &&
                                                <Button
                                                    className="confirmBtn"
                                                    onClick={() => ValidateOrder(item, 1)}
                                                    disabled={!IsSaveUpdateEnabled}
                                                >
                                                    Save
                                                </Button>
                                            }
                                            {(isDisabled && payment == 1) && <Button className="confirmBtn" onClick={() => ValidateBooking(item)}>Confirm Payment</Button>}
                                        </Grid>
                                    </>
                                </div>
                            }
                        </div>
                    ))}
                </div>
            }
        </Grid>
        {(AnualOpenPopupLeadId && AnualOpenPopupLeadId > 0) &&
            <ErrorBoundary name="ShowAnualOpenPopup">
                <AnualOpenLeadComments
                    open={true}
                    handleClose={() => { setAnualOpenPopupLeadId(null) }}
                    Title="Customer is already having the Annual Open Policy"
                    message="Customer is already having the Annual Open Policy"
                    LeadId={AnualOpenPopupLeadId}
                    setConfirmBooking={setConfirmBooking}
                />
            </ErrorBoundary>
        }
        
        {/* Lead Auto-Rejection Prompt */}
        <ErrorBoundary name="LeadRejectionPrompt">
            <LeadRejectionPrompt
                open={showLeadRejectionPrompt}
                onClose={handleLeadRejectionPromptClose}
                customerOpenLeads={customerOpenLeads}
                loadingOpenLeads={loadingOpenLeads}
                onProceedWithBooking={handleProceedWithBooking}
            />
        </ErrorBoundary>
       
    </>;
}

const mapStateToProps = state => {
    return {
    };
};

const mapDispatchToProps = dispatch => {
    return {
        setRefreshLeadToRedux: (value) => dispatch(setRefreshLead({ RefreshLead: value })),
        setIsCallCreateBookingToRedux: (value) => dispatch(setIsCallCreateBooking({ IsCallCreateBooking: value }))
    };
};

export default connect(mapStateToProps, mapDispatchToProps)(CreateBooking);