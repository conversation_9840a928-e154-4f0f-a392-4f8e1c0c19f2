import React, { useState, useEffect } from "react";
import ModalPopup from "../../../components/Dialogs/ModalPopup";
import { Grid } from "@mui/material";

const TermUpsellPopup = (props) => {
    const [customerData, setCustomerData] = useState({});
    const [policyData, setPolicyData] = useState({});
    
    useEffect(() => {
        // Parse utm_term and utm_medium data
        try {
            if (props.utm_term) {
                // Remove the surrounding curly braces and parse
                const termData = props.utm_term.replace(/[{}]/g, '');
                const termObj = {};
                
                // Split by comma and parse each key-value pair
                termData.split(',').forEach(item => {
                    const [key, value] = item.split(':').map(s => s.trim());
                    if (key && value) {
                        termObj[key] = value;
                    }
                });
                
                setCustomerData(termObj);
            }
            
            if (props.utm_medium) {
                // Remove the surrounding curly braces and parse
                const mediumData = props.utm_medium.replace(/[{}]/g, '');
                const mediumObj = {};
                
                // Split by comma and parse each key-value pair
                mediumData.split(',').forEach(item => {
                    const [key, value] = item.split(':').map(s => s.trim());
                    if (key && value) {
                        mediumObj[key] = value;
                    }
                });
                
                setPolicyData(mediumObj);
            }
        } catch (error) {
            console.error('Error parsing UTM data:', error);
        }
    }, [props.utm_term, props.utm_medium]);

    return (
        <ModalPopup 
            open={props.open} 
            handleClose={props.handleClose} 
            title="Term Plan Purchase Details" 
            className="ReferalPopup"
        >
            <Grid container spacing={2}>

            {customerData && Object.keys(customerData).length > 0 && Object.entries(customerData).map(([key, value]) => (
                <React.Fragment key={key}>
                    <Grid item md={6}>
                        <label>{key}</label>
                    </Grid>
                    <Grid item md={6}>
                        {value}
                    </Grid>
                </React.Fragment>
            ))}
            {policyData &&Object.keys(policyData).length > 0 &&  Object.entries(policyData).map(([key, value]) => (
                <React.Fragment key={key}>
                    <Grid item md={6}>
                        <label>{key}</label>
                    </Grid>
                    <Grid item md={6}>
                        {value}
                    </Grid>
                </React.Fragment>
            ))}
            </Grid>
        </ModalPopup>
    );
};

export default TermUpsellPopup;