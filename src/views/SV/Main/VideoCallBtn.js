import { Button } from "@mui/material";
import { VideocamRounded } from "@mui/icons-material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import { gaEventNames, gaEventTracker } from "../../../helpers";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { useInterval } from "./helpers/useInterval";
import VideoCallPopup from "./Modals/VideoCallPopup";
import { localStorageCache } from "../../../utils/utility";


export default function VideoCallBtn() {

    const [IsVideoCallBtnVisible, setIsVideoCallBtnVisible] = useState(false);
    // const [IsShowVCBtnAlert, setIsShowVCBtnAlert] = useState(false);
    // const [IsVcEligible, setIsVcEligible] = useState(false);

    const [openVCpopup, setOpenVCpopup] = useState(false);
    const [autoShowPopupCount, setAutoShowPopupCount] = useState(0);
    const parentLeadId = useSelector(({ salesview }) => (salesview.parentLeadId))
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    let [IsVcEligible] = useSelector(({ salesview }) => [salesview.IsVcEligible]);
    const [IsClickedGotIt, setIsClickedGotIt] = useState(true);

    const [ invitesTs, setInvitesTs ] = useState(() => {
        const storedInvites = localStorageCache.readFromCache('vcInvites');
        return storedInvites ?? {};
    });

    const closePopup = () => {
        setOpenVCpopup(false);
    }
    const openPopup = function () {
        setOpenVCpopup(true);
    }
    const handleBtnClick = () => {
        gaEventTracker(gaEventNames.VCButton, 'SalesView', parentLeadId, 'PbmeetTracking');
        openPopup();
    }

    /**
     * Function to determine whether to show the video call button based on various conditions.
     */
    const IsShowVideoCall = () => {

        let showVCButton = false;

        // Retrieve VideoCallGroups based on the environment from SV_CONFIG
        let VideoCallGroups = SV_CONFIG["VideoCallGroups"][SV_CONFIG["environment"]];

        // Extract allowed role IDs from SV_CONFIG or use an empty array if not available
        const roleIdArray = (SV_CONFIG && Array.isArray(SV_CONFIG.pbmeetAllowedRoleIds) ? SV_CONFIG.pbmeetAllowedRoleIds : []) || [];

        // Extract allowed product IDs from SV_CONFIG or use an empty array if not available
        const productIdArray = (SV_CONFIG && Array.isArray(SV_CONFIG.pbmeetAllowedProductIds) ? SV_CONFIG.pbmeetAllowedProductIds : []) || [];

        // Initialize a flag to check if the user is in a video call group
        let userInVCGroup = false;

        // Extract the user group list or use an empty array if not available
        const userGroup = User.UserGroupList || [];

        // Iterate through each user group
        userGroup.forEach((item, key) => {
            // Check if the user is in a video call group and has a lead ID
            if (rootScopeService.getLeadId() && VideoCallGroups && (VideoCallGroups.indexOf(item.GroupId) > -1)) {
                userInVCGroup = true;
            }
        });

        // Check conditions to determine if the video call button should be visible
        if (roleIdArray.includes(User.RoleId) &&
            (productIdArray.includes(rootScopeService.getProductId()) ||
            User.IsEnableVC ||
            userInVCGroup)
        ) {
            showVCButton = true;
        }

        if (showVCButton) {
            // Set the visibility of the video call button to true
            setIsVideoCallBtnVisible(true);
        }
    };

    useInterval(function () {
        if (!IsVideoCallBtnVisible) return;

        let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
        const onCall = window.localStorage.getItem('onCall');

        if (ConnectCallSF && onCall === 'true') {
            ConnectCallSF = JSON.parse(ConnectCallSF);
            var CallinitateTime = ConnectCallSF.CallInitTime;
            let durationTime = 0;
            if (CallinitateTime) {
                durationTime = (new Date() - new Date(CallinitateTime)) / 1000;
            }
            if (IsVideoCallBtnVisible && !openVCpopup && User.TenureYears < 1) {              
                if (durationTime >= 200 && durationTime <= 205 && autoShowPopupCount < 1) {
                    setAutoShowPopupCount(1);
                    openPopup();
                    gaEventTracker(gaEventNames.autoPopupOpen, 'SalesView', parentLeadId, 'PbmeetTracking');
                }
                // else if (durationTime >= 300 && durationTime <= 305 && autoShowPopupCount < 2) {
                //     setAutoShowPopupCount(2);
                //     openPopup();
                //     gaEventTracker(gaEventNames.autoPopupOpen, 'SalesView', parentLeadId);
                // }
            }
        }
    }, 2000)

    useEffect(() => {
        if (RefreshLead) {
            IsShowVideoCall();
            setAutoShowPopupCount(0);
            //setIsClickedGotIt(true);
        }
    }, [RefreshLead])
    useEffect(() => {
        IsShowVideoCall();
        setAutoShowPopupCount(0);
    }, [])

    useEffect(() => {
        if (IsVcEligible) {
            setIsClickedGotIt(false);
        }
        if (!IsVcEligible) {
            setIsClickedGotIt(true);
        }
    }, [IsVcEligible])

    useEffect(() => {
        let timer;
        if (!IsClickedGotIt) {
            timer = setTimeout(() => {
                setIsClickedGotIt(true); // Auto-close after 5 seconds
            }, 5000);
        }
        return () => clearTimeout(timer);
    }, [IsClickedGotIt]);


    return <>
        {IsVideoCallBtnVisible && !IsVcEligible &&
            <Button
                variant="contained"
                color="secondary"
                onClick={handleBtnClick}
                className="videoButton"
            >
                <VideocamRounded fontSize="default" />
            </Button>
        }


        {IsVideoCallBtnVisible && IsVcEligible &&
            <div className="videoBtn">
                <span onClick={handleBtnClick}>
                    <img src={CONFIG.PUBLIC_URL + "/images/videoIcon.gif"} alt="VC" />
                </span>
                {!IsClickedGotIt && <span class="tooltiptext">This looks like a prospect customer with high conversion probability, Try pitching ScreenSharing to close the sale! <button className="gotItBtn" onClick={() => { setIsClickedGotIt(true) }}>GOT IT</button></span>}
            </div>
        }

        {openVCpopup &&
            <VideoCallPopup
                open={openVCpopup}
                handleClose={closePopup}
                invitesTs={invitesTs}
                setInvitesTs={setInvitesTs}
            />
        }

    </>;
}

