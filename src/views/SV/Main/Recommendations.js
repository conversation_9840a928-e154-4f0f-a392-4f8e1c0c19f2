import React, { useEffect, useState } from "react";
import RecommendationCard from "./RecommendationCard";
import { AddCircle, ExpandMore } from '@mui/icons-material';
import { Grid, button, Tabs } from "@mui/material";
import rootScopeService from "../../../services/rootScopeService";
import { CALL_API } from "../../../services";
import masterService from "../../../services/masterService";
import { useDispatch, useSelector } from "react-redux";
import { useSnackbar } from "notistack";
import { SelectDropdown } from "../../../components";
import { AddNewRecommendationPopup } from "./Modals/AddNewRecommendationPopup";
import Common, { IsValidUserGroup } from "../../../services/Common";
import { updateStateInRedux } from "../../../store/actions/SalesView/SalesView";
import { AntTab } from "../../../components/TabsAndContents/TabAndContents.js";
import MultipleSelectDropdown from "../../../components/MultipleSelectDropdown";


export const savePRservice = (reqData) => {
    try {
        let _input = {
            url: 'coremrs/api/MRSCore/SaveMultipleRecommendations',
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        }
        return CALL_API(_input).then((response) => {

            return {
                IsSaved: response.status,
                ...response
            }

        });
    }
    catch (e) {
        console.error("Unable to use coremrs/api/MRSCore/SaveRecommendations")
    }
}
const AddNewForm = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const { activeLeadCollection, Suppliers, Plans, newPR, setNewPR, productId, subProductFilter, subProductTypeID, UserId } = props;
    const [OptimizedPlansListRec, setOptimizedPlansListRec] = useState([]);
    let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);

    const supplierList = Suppliers.filter(supplier => {
        let valid = true;
        if (subProductFilter) {
            valid = (valid && (supplier.SubCategoryId == subProductTypeID));
        }
        valid = (valid && (supplier.ProductId == productId || supplier.ProductId == 0));
        return valid;
    });
    const planList = Plans.filter(plan => {
        let valid = true;
        if (subProductFilter) {
            valid = valid && (plan.SubProductTypeID == subProductTypeID);
        }
        valid = valid && (plan.ProductId == productId);
        valid = valid && (plan.SupplierId == newPR.Supplier);
        return valid;
    });
    const OptimizedplanList = Array.isArray(OptimizedPlansListRec) ? OptimizedPlansListRec.filter(plan => {
        let valid = true;
        if (subProductFilter) {
            valid = valid && (plan.SubProductTypeID == subProductTypeID);
        }
        valid = valid && (plan.ProductId == productId);
        //valid = valid && (plan.SupplierId == newPR.Supplier);
        return valid;
    }) : [];
    const SaveRecommendations = () => {
        let valid = false;
        if ([undefined, null, ''].indexOf(newPR.activeLeadId) !== -1)
            enqueueSnackbar("Please Select LeadId", { variant: 'error', autoHideDuration: 3000, });
        else if ([undefined, null, ''].indexOf(newPR.Supplier) !== -1)
            enqueueSnackbar("Please Select Supplier", { variant: 'error', autoHideDuration: 3000, });
        else if ([undefined, null, ''].indexOf(newPR.Plan) !== -1)
            enqueueSnackbar("Please Select Plan", { variant: 'error', autoHideDuration: 3000, });
        else valid = true;

        if (!valid) return;

        let selectedPlan = [];
        if (Array.isArray(newPR.Plan)) {
            selectedPlan = OptimizedplanList.filter(e => {
                return newPR.Plan.some(n => n.PlanId === e.PlanId);
            });
        }
        else {
            selectedPlan = OptimizedplanList.filter((p) => p.PlanId == newPR.Plan);
        }
        let selectedSupplier = supplierList.filter((s) => s.SupplierId == newPR.Supplier)[0];
        var changedPR = [];
        var isPlan = !([undefined, null].indexOf(newPR.Plan) !== -1);

        selectedPlan.map((sp) => {
            changedPR.push({
                OldPlanId: isPlan ? sp.OldPlanId : 0,
                PlanId: isPlan ? sp.PlanId : 0,
                PlanName: isPlan ? sp.PlanName : "None",
                OldSupplierId: selectedSupplier.OldSupplierId,
                SupplierDisplayName: selectedSupplier.SupplierDisplayName,
                SupplierId: selectedSupplier.SupplierId,
                ProductId: productId,
                Premium: 0,
                Rating: 0,
                RequireDocCount: 0,
                SelectionType: 2,
                SumInsured: 0,
                IsActive: true,
                IsNew: true,
            });
        })

        props.SavePR(changedPR, newPR.activeLeadId)?.then((response) => {
            if (response.IsSaved) {
                //Todo need to check  for term SubProductID will zero
                props.getProductRecommendations();
                enqueueSnackbar("Recommendation saved successfully", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
                setNewPR({ Plan: '', Supplier: '', activeLeadId: '' })
            };
        }, function (reason) {
            enqueueSnackbar("Error saving PR", {
                variant: 'failure',
                autoHideDuration: 3000,
            })
        });



        /*notification*/
        var notificationdata = {
            parentID: parentLeadId,
            UserId: UserId,
            action: 5
        };
        //To do list
        Common.SetNotificationAction(notificationdata);
        /*notification*/
    }

    const handleChange = (e) => {
        let pr = {};
        pr = { ...newPR, [e.target.name]: e.target.value }
        if (e.target.name == 'Supplier') {
            let SupplierId = e.target.value;
            if (SupplierId) {
                masterService.GetProductPlansFromCore(rootScopeService.getProductId(), SupplierId, "Recommendation").then(res => {
                    setOptimizedPlansListRec(res);
                    //pr.Plan = res;
                })
            }
            pr.Plan = '';
        }
        setNewPR(pr);
    }

    const ShowMultipleSelectDropdown = () => {
        return productId === 2 && IsValidUserGroup([3010], [13]) // Groupd id's to be added
    }

    return (
        <div className="addnew-form-wrapper">

            <Grid item sm={12} md={12} xs={12}>
                <Grid container spacing={3}>

                    <SelectDropdown
                        name="activeLeadId"
                        label="Select LeadId"
                        value={newPR.activeLeadId}
                        options={activeLeadCollection}
                        labelKeyInOptions='LeadID'
                        valueKeyInOptions='LeadID'
                        handleChange={handleChange}
                        show={true}
                        sm={8} md={8} xs={12}
                    />
                    <SelectDropdown
                        name="Supplier"
                        label="Supplier"
                        value={newPR.Supplier}
                        options={supplierList}
                        labelKeyInOptions='SupplierDisplayName'
                        valueKeyInOptions='SupplierId'
                        handleChange={handleChange}
                        show={true}
                        sm={8} md={8} xs={12}
                    />
                    <SelectDropdown
                        name="Plan"
                        label="Plan"
                        value={newPR.Plan}
                        options={OptimizedplanList}
                        labelKeyInOptions='PlanName'
                        valueKeyInOptions='PlanId'
                        handleChange={handleChange}
                        show={!ShowMultipleSelectDropdown()}
                        sm={8} md={8} xs={12}
                    />
                    <MultipleSelectDropdown
                        name="Plan"
                        label="Plan"
                        value={newPR.Plan || []}
                        options={OptimizedplanList}
                        labelKeyInOptions='PlanName'
                        valueKeyInOptions='_all'
                        handleChangeMultiple={handleChange}
                        renderValue={(selected) => selected.map((s) => { return s['PlanName'] }).join(', ')}
                        show={ShowMultipleSelectDropdown()}
                        limitToDisableOptions={3}
                        sm={8} md={8} xs={12}
                        shrink={{ shrink: newPR.Plan && newPR.Plan.length > 0 }}
                    />
                    <Grid item md={12} sm={12} xs={12}><button onClick={() => { SaveRecommendations() }} className="goBtn">Go</button></Grid>
                </Grid>
            </Grid>
        </div>
    )
}



export default function Recommendations(props) {

    const [recommendations, setRecommendations] = useState([]);
    const [Suppliers, setSuppliers] = useState([]);
    const [Plans, setPlans] = useState([]);
    const [subProductFilter, setSubProductFilter] = useState(false);
    const [subProductTypeID, setSubProductTypeID] = useState(0);
    const [selectedSubProductId, setSelectedSubProductId] = useState(0);
    const [subProducts, setSubProducts] = useState([]);
    const [productId, setProductId] = useState(rootScopeService.getProductId());
    const [leadId, setLeadId] = useState(0);
    const [activeLeadCollection, setActiveLeadCollection] = useState([]);
    const [ShowRecommendationSection, setShowRecommendationSection] = useState(false);
    const [subProductTabs, setsubProductTabs] = useState([]);
    const [newPR, setNewPR] = useState({
        activeLeadId: '', Supplier: '', Plan: []
    });
    const [activeTab, setActiveTab] = React.useState(0);
    let [RefreshLead, RefreshProductRecommendations] = useSelector(({ salesview }) => [salesview.RefreshLead, salesview.RefreshProductRecommendations]);
    const [OpenAddNewRecommendation, setOpenAddNewRecommendation] = useState(false);
    let [allLeads, SubProductID, parentLeadId, SetManualStamping] = useSelector(({ salesview }) => [salesview.allLeads, salesview.subProductId, salesview.parentLeadId, salesview.SetManualStamping]);
    // const RecommendV2 = Array.isArray(SV_CONFIG['RecommendV2']) ? SV_CONFIG['RecommendV2'] : [];
    const dispatch = useDispatch();
    useEffect(() => {
        let _subProductFilter = (productId == 115)
        getSuppliers(_subProductFilter);
        //GetProductPlans(_subProductFilter);
        if (_subProductFilter) {
            setSubProductFilter(true);
            getSubProductByProductID();
        }
    }, []);




    useEffect(() => {
        getSubProductTabs();

    }, [subProducts]);


    useEffect(() => {
        if (RefreshLead || RefreshProductRecommendations) {
            setRecommendations([])
            setShowRecommendationSection(false);
            dispatch(updateStateInRedux({ key: "RefreshProductRecommendations", value: false }));

        }
    }, [RefreshLead, RefreshProductRecommendations]);

    const handleToggle = (e) => {

        if (ShowRecommendationSection == false) {
            getPRBySubProdId(SubProductID);
        }

        setShowRecommendationSection(!ShowRecommendationSection);
    };
    const getSuppliers = (subProductfilter) => {
        masterService.getSuppliers().then(function (response) {
            let uniqueSuppliers = [], map = {};
            if (!subProductfilter) {
                response.forEach((supplier) => {
                    let valid = (supplier.ProductId == productId && !map[supplier.OldSupplierId]);
                    if (valid) {
                        uniqueSuppliers.push(supplier);
                        map[supplier.OldSupplierId] = 1;
                    }
                });
                setSuppliers(uniqueSuppliers);
            }
            else {
                setSuppliers(response)
            }
        });
    }

    const GetProductPlans = (subProductFilter) => {
        masterService.GetProductPlans(productId).then(function (response) {
            let uniquePlans = [], map = {};
            if (!subProductFilter) {
                Array.isArray(response) && response.forEach((plan) => {
                    let valid = (plan.ProductId == productId && !map[plan.OldPlanId] && plan.IsMatrix);
                    if (valid) {
                        uniquePlans.push(plan);
                        map[plan.OldPlanId] = 1;
                    }
                });
                setPlans(uniquePlans);
            }
            else {
                setPlans(response)

            }
        });
    }

    const getPRBySubProdId = (subProductTypeID) => {
        setSelectedSubProductId(subProductTypeID);
        setNewPR({ activeLeadId: '', Supplier: '', Plan: '' });
        setLeadId(0);
        if (subProductFilter && subProducts[activeTab] && subProductTypeID !== subProducts[activeTab].ID) { setRecommendations([]); }
        let activeLead = [];
        let activeStatus = [1, 2, 3, 4, 11];
        let data = allLeads.filter(lead => lead.SubProductTypeId == subProductTypeID);
        let pos = 0;
        for (var i = 0; i < subProducts.length; i++) {
            if (subProductTypeID === subProducts[i].ID) {
                pos = i;
                break;
            }

        }
        setActiveTab(pos)
        data.forEach(item => {
            // if (activeStatus.indexOf(item.StatusId) !== -1) {
            // if (item.OfferNumber == undefined || item.OfferNumber == null || item.OfferNumber == "") {
            activeLead.push(item);
            // }
            // }
        });
        setActiveLeadCollection(activeLead);
        var _leadID = 0;
        if (activeLead.length > 0) {
            setLeadId(activeLead[0].LeadID);
            _leadID = activeLead[0].LeadID
            newPR.ActiveLead = activeLead[0].LeadID;
        }
        if (allLeads.length > 0) {
            if (subProductFilter == 0) {
                setLeadId(allLeads[0].LeadID);
                _leadID = activeLead[0].LeadID
            }
            if (subProductTypeID == 1 || subProductTypeID == 6) {
                let InvestmentData = allLeads.filter(lead => lead.SubProductTypeId == 6);
                if (InvestmentData.length > 0) {
                    _leadID = InvestmentData[0].LeadID;
                }
            }
            // if (subProductTypeID != undefined) {
            getProductRecommendations(subProductTypeID, _leadID)
            // }
        }

    }

    const getProductRecommendations = (SubProductTypeID, _leadID) => {
        setSubProductTypeID(SubProductTypeID);
        setRecommendations([]);

        let _input = {
            url: 'coremrs/api/MRSCore/GetProductRecommendations',
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: { "selectionData": { "Data": { "NeedId": 0, "ProductId": productId, "GroupId": "", "MatrixLeadId": _leadID, "CoreLeadId": 0, "SubProductType": SubProductTypeID } } }
        }
        // }
        if (_leadID > 0) {

            CALL_API(_input).then((resultData) => {
                if (resultData) {
                    var res = resultData.GetProductRecommendationsResult.Data.ProductRecommendations;
                    var ProductRec = [];
                    if (subProductFilter) {
                        if (SubProductTypeID == 1 || SubProductTypeID == 6) {
                            ProductRec = res.filter(r => r.SubProductType === 1 || r.SubProductType === 6)
                        }
                        else {
                            ProductRec = res.filter(r => r.SubProductType === SubProductTypeID);
                        }
                    }
                    else {
                        ProductRec = res;
                    }
                    setRecommendations(ProductRec)
                }
            })
        }


    }

    const getSubProductByProductID = () => {
        masterService.getSubProductByProductID(productId).then(response => {
            setSubProducts(response);
        });
    }



    const SavePR = function (changedPR, leadId) {
        let CustomerID = rootScopeService.getCustomerId();
        let reqData = [];
        changedPR.map((cp)=>{
            let newReqData = {
                "ProductRecommendations": [cp],
                "LeadId": leadId,
                "NeedId": -2,
                "CustomerId": CustomerID
            }
            reqData.push(newReqData);
        })        
        return savePRservice(reqData);
    }


    const recommendationList = recommendations.map((rec, Index) => (
        <RecommendationCard
            data={rec}
            key={Index}
            User={props.User}
            Suppliers={Suppliers}
            Plans={Plans}
            SavePR={SavePR}
            allLeads={allLeads}
            getProductRecommendations={() => { getProductRecommendations(selectedSubProductId, leadId) }}
        />
    ));
    const recommendationList_Inv = recommendations.map((rec, Index) => {
        if (subProducts[activeTab] && ((subProducts[activeTab].ID === rec.SubProductType) || (subProducts[activeTab].ID === 1 && rec.SubProductType === 6))) {
            return <RecommendationCard
                data={rec}
                key={Index}
                User={props.User}
                Suppliers={Suppliers}
                Plans={Plans}
                SavePR={SavePR}
                allLeads={allLeads}
                getProductRecommendations={() => { getProductRecommendations(selectedSubProductId, leadId) }}
            />
        }
        else {
            return null;
        }

    });

    // setShowAddNew(!showAddNew)
    const fnAddNewRecommendation = (activeTabIndex) => {
        if (!subProductFilter) { getPRBySubProdId(SubProductID); }
        else {
            getPRBySubProdId(subProducts[activeTabIndex].ID)
        }
        setOpenAddNewRecommendation(true);
    }

    const getSubProductTabs = () => {
        const tabs = [];
        for (let i of subProducts) {
            tabs.push({ ID: i.ID, title: i.Name })
        }
        setsubProductTabs(tabs)
    }
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);

    }

    return (
        <>
            <Grid item sm={12} md={12} xs={12}>
                <div name='CreateBooking' className="recomend"> <h3>Recommendations</h3>
                    <div className="expandmoreIcon"><ExpandMore onClick={handleToggle} style={{ transform: ShowRecommendationSection ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
                    <span className="caption">Plans recommended earlier</span>
                    {!subProductFilter && <h3 className="addnew" onClick={fnAddNewRecommendation}> <span><AddCircle /> Add New</span></h3>}
                    {ShowRecommendationSection &&
                        <Tabs value={activeTab} onChange={handleTabChange} className="tabUI">
                            {
                                subProductTabs.map((tab, index) => (

                                    <AntTab className="tabBtn" label={tab.title} key={index} onClick={() => { getPRBySubProdId(tab.ID) }} />
                                ))
                            }

                        </Tabs>
                    }
                    <div className="list-items">
                        {ShowRecommendationSection && subProductFilter && leadId > 0 && <h3 className="addnew" onClick={() => { fnAddNewRecommendation(activeTab) }}> <span><AddCircle /> Add New</span></h3>}
                        {!subProductFilter && ShowRecommendationSection && recommendationList}
                        {subProductFilter && ShowRecommendationSection && recommendationList_Inv}
                    </div>
                </div>
                <AddNewRecommendationPopup open={OpenAddNewRecommendation} handleClose={() => { setOpenAddNewRecommendation(false) }} >
                    <AddNewForm activeLeadCollection={activeLeadCollection} Suppliers={Suppliers}
                        Plans={Plans} newPR={newPR} setNewPR={setNewPR} productId={productId}
                        subProductFilter={subProductFilter} subProductTypeID={subProductTypeID}
                        SavePR={SavePR} UserId={props.User.UserId}
                        getProductRecommendations={() => { getProductRecommendations(selectedSubProductId, leadId) }} />
                </AddNewRecommendationPopup>
            </Grid>
        </>
    )

}
