/* Motor Checklist Form Styles */
.motor-checklist-form-section {
    border-radius: 0px 0px 16px 16px;
    background: #CCDBF4;
    overflow: hidden;
    cursor: pointer;
    height: 36px;

    .motor-checklist-form-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 16px;

        .form-title {
            color: #253859;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            /* 150% */
        }

        .form-arrow {
            color: #0165FF;
            font-size: 16px;
            font-weight: bold;

        }


    }
}

.motorChecklistPopup {
    .MuiDialog-scrollPaper {
        background-color: #253858d9;
    }

    .popupWrapper {
        max-height: 80vh;
        overflow-y: auto;
        padding: 20px 30px;

    }

    .savechangeBtn {
        background: #0065ff;
        border-radius: 8px;
        width: 230px;
        margin-top: 0px;
        letter-spacing: 0.17px;
        padding: 12px 40px;
        border: none;
        color: #ffffff;
        font: normal normal 400 14px/21px Roboto;
        cursor: pointer;
        box-shadow: none;
        outline: none;

    }

    .MuiDialog-paperScrollPaper {
        width: 564px;
        border-radius: 20px;
        background: linear-gradient(141deg, #FFF 0.65%, #F7FAFF 43.24%);
    }

    .checklist-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .custom-input {
        input {
            background-color: white;
            color: #253858 !important;
            font-weight: 500 !important;
        }

        .MuiOutlinedInput-root {
            width: 240px;
        }
    }

    .MuiOutlinedInput-root {
        height: auto !important;
        padding: 0px;
        background: #FFF;
    }

    .MuiDialogContent-root {
        padding: 0px !important;
    }

    .MuiOutlinedInput-input {
        color: #253858 !important;
        font-family: Roboto !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        line-height: 24px !important;
        /* 171.429% */
    }

    .MuiFormLabel-root {
        color: #253858 !important;
        font-weight: 500 !important;
        margin-bottom: 5px;
    }

    .required {
        color: #FF5630;
        margin-left: 4px;
    }

    .MuiFormControlLabel-label {
        color: #253858;
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
    }

    .MuiOutlinedInput-notchedOutline {
        border-radius: 6px !important;
        border: 0.8px solid #CECECE !important;

    }

    .MuiRadio-root {
        svg {
            color: #CECECE;
            font-size: 1.7rem;
        }
    }

    .Mui-checked {
        svg {
            color: #0065FF;
        }
    }

    .MuiIconButton-root {
        background-color: #eaeaea;
        position: absolute;
        padding: 5px;
        right: 17px;
        top: 10px;
        z-index: 999;
    
        svg {
          font-size: 1.2rem;
        }
      }

    &.admin-view {
        .MuiFormControl-root {
            opacity: 0.8;
        }
        
        .MuiRadio-root {
            color: #999 !important;
        }
        
        .Mui-checked {
            .MuiRadio-root {
                color: #999 !important;
            }
        }
        
        .savechangeBtn {
            background: #999 !important;
            cursor: not-allowed;
        }
    }
}

.motor-header {
    background-color: #E7F0FF;
    padding: 12px 20px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.motor-header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}



.motor-title {
    color: #253859;
    font-family: Roboto;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px; /* 150% */
}

.motor-updated {
    background-color: #CADEFF;
    color: #395583;
    font-family: Roboto;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
    border-radius: 40px;
    padding:3px 10px 2px;
}

.motor-close-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    color: #1d3a7b;
    padding: 4px;
    display: flex;
    align-items: center;
}

.form-updated {
    font-size: x-small;
    color: #395583;
    font-family: Roboto;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
}