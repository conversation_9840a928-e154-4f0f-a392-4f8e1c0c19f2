import React, { useState, useCallback } from "react";
import { LinearProgress } from "@mui/material";
import { CALL_API } from '../../../services/api.service';
import rootScopeService from "../../../services/rootScopeService";

let data1 = {
  "customerid": 25943692,
  "isnri": "No",
  "city": "Jhunjhunu",
  "appinstalled": "yes",
  "languagesknown": "unknown",
  "income": 699999,
  "customer_category": "HNI customer",
  "member_summary": "Family of 3 including self, spouse, mother",
  "fos_status": "No FOS visits",
  "insurance_portfolio": {
      "portfolio": "27 booked – 25 issued, 3 rejected, 1 cancelled",
      "rejected_status": {
          "health": "Health insurance lead rejected after 32 calls (2.6 hours)",
          "term": "Term insurance lead rejected after 4 calls (5.3 mins)"
      },
      "active_status": "10 active policies",
      "rider": "supertopup bought (90L) | PA covered (15L)",
      "policies": {
          "health": {
              "renewalstatus": "self ADITYA BIRLA policy expired 11 months ago",
              "status": "NIVA BUPA 10L suminsured policy | 23.2K premium (self), expiring in 43 days (non assisted)",
              "active_status": "3 active health policies"
          },
          "investment": {
              "status": "BAJAJ ALLIANZ 3.6L suminsured policy | 36K premium, expiring in 19.1 yrs (non assisted)",
              "active_status": "2 active investment policies"
          },
          "motor": {
              "renewalstatus": "Renewed twice for TATA ALTROZ from RELIANCE",
              "status": "Owns MARUTI ERTIGA | 10.2L IDV, expiring in 87 days (non assisted)",
              "active_status": "2 active motor policies"
          },
          "twowheeler": {
              "status": "Owns TVS Jupiter | 50K IDV, expiring in 8 months (non assisted)",
              "active_status": "3 active twowheeler policies"
          }
      }
  },
  "communication": {
      "call_preference_time": "weekday 9AM-12PM",
      "preferred_channel": "Prefers phone communication, mostly for sales calls",
      "dnd": "DND opted for Sales (whatsapp)",
      "recent_interaction_chat": "Customer last interacted on whatapp 5 months ago for motor sales",
      "recent_interaction_call": "Customer last interacted on call 8 days ago",
      "pre_sales_talktime": "17 hrs",
      "post_sales_talktime": "6.9 hrs"
  }
}

export default function CustomerProfile() {
    const [customer360Data, setCustomer360Data] = useState(null);
    const [isCustomer360Loading, setIsCustomer360Loading] = useState(false);

    const getCustomer360Profile = useCallback(() => {
        setIsCustomer360Loading(true);
        const customerId = rootScopeService.getCustomerId();
        
        const input = {
            url: `https://qainternalmatrixapi.policybazaar.com/api/SalesView/GetCustomer360Profile/${customerId}`,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'source': 'matrix',
                'clientKey': 'MTQAL6YWNav',
                'authKey': 'MTQALGsaWLYmF6YWNav'
            }
        };

        CALL_API(input).then((result) => {
            if (result) {
                setCustomer360Data(data1);
            }
        })
        .catch((error) => {
            console.error('Error fetching Customer 360 data:', error);
            setCustomer360Data(data1);
        })
        .finally(() => {
            setIsCustomer360Loading(false);
        });
    }, []);

    // Load data when component mounts
    React.useEffect(() => {
        if (!customer360Data) {
            getCustomer360Profile();
        }
    }, [customer360Data, getCustomer360Profile]);

    if (isCustomer360Loading) {
        return <LinearProgress />;
    }

    if (!customer360Data) {
        return <div style={{ padding: '20px', textAlign: 'center', color: '#808080' }}>No data available</div>;
    }

    const data = customer360Data;

    // Helper function to check if customer has specific policy type
    const hasPolicyType = (policyType) => {
        return data.insurance_portfolio?.policies?.[policyType]?.active_status || 
               data.insurance_portfolio?.policies?.[policyType]?.status;
    };

    // Generate insights based on data
    const getCustomerInsights = () => {
        const insights = [];
        
        if (data.customer_category) {
            insights.push(`${data.customer_category}`);
        }
        
        if (data.member_summary) {
            insights.push(`${data.member_summary}`);
        }
        
        if (data.insurance_portfolio?.portfolio) {
            insights.push(`Portfolio: ${data.insurance_portfolio.portfolio}`);
        }
        
        if (data.communication?.pre_sales_talktime) {
            insights.push(`Pre-sales engagement: ${data.communication.pre_sales_talktime} talk time`);
        }
        
        if (data.fos_status) {
            insights.push(`Field visits: ${data.fos_status}`);
        }
        
        return insights;
    };

    return (
        <div className="know-your-customer">
            {/* Customer Basic Info */}
            <div className="customer-info-section">
                <div className="info-row">
                    <span className="info-value">{data.city}</span>
                </div>
                <div className="info-row">
                    <span className="info-value">{data.age ? data.age : 'N/A'}</span>
                </div>
                <div className="info-row">
                    <span className="info-value">{data.employment}</span>
                </div>
                <div className="info-row">
                    <span className="info-value">{data.income ? `${(data.income / 100000).toFixed(1)}L` : 'N/A'}</span>
                </div>
            </div>

            {/* Customer Insights */}
            <div className="customer-insights-section">
                <h4>Customer Insights</h4>
                <div className="insights-list">
                    {getCustomerInsights().map((insight, index) => (
                        <div key={index} className="insight-item">
                            <span className="insight-icon">💡</span>
                            <span className="insight-text">{insight}</span>
                        </div>
                    ))}
                </div>
            </div>

            {/* Insurance Portfolio */}
            <div className="insurance-portfolio-section">
                <h4>Insurance portfolio</h4>
                <div className="portfolio-icons">
                    {hasPolicyType('health') && (
                        <div className="portfolio-item">
                            <div className="icon health-icon">❤️</div>
                            <span className="policy-type">Health</span>
                        </div>
                    )}
                    {hasPolicyType('motor') && (
                        <div className="portfolio-item">
                            <div className="icon motor-icon">🚗</div>
                            <span className="policy-type">Motor</span>
                        </div>
                    )}
                    {hasPolicyType('twowheeler') && (
                        <div className="portfolio-item">
                            <div className="icon bike-icon">🏍️</div>
                            <span className="policy-type">Two Wheeler</span>
                        </div>
                    )}
                    {hasPolicyType('investment') && (
                        <div className="portfolio-item">
                            <div className="icon investment-icon">💰</div>
                            <span className="policy-type">Investment</span>
                        </div>
                    )}
                </div>
                
                {/* Dynamic Policy Status Messages */}
                {data.insurance_portfolio?.policies?.health?.status && (
                    <div className="portfolio-status health-status">
                        <span className="status-icon">✅</span>
                        <span>{data.insurance_portfolio.policies.health.status}</span>
                    </div>
                )}
                
                {data.insurance_portfolio?.policies?.motor?.renewalstatus && (
                    <div className="portfolio-status motor-status">
                        <span className="status-icon">🔄</span>
                        <span>{data.insurance_portfolio.policies.motor.renewalstatus}</span>
                    </div>
                )}

                {data.insurance_portfolio?.policies?.twowheeler?.status && (
                    <div className="portfolio-status bike-status">
                        <span className="status-icon">🏍️</span>
                        <span>{data.insurance_portfolio.policies.twowheeler.status}</span>
                    </div>
                )}

                {/* {data.insurance_portfolio?.rejected_status && (
                    <div className="rejected-policies">
                        <h5>Recent Rejections:</h5>
                        {data.insurance_portfolio.rejected_status.health && (
                            <div className="rejected-item">
                                <span className="reject-icon">❌</span>
                                <span>{data.insurance_portfolio.rejected_status.health}</span>
                            </div>
                        )}
                        {data.insurance_portfolio.rejected_status.term && (
                            <div className="rejected-item">
                                <span className="reject-icon">❌</span>
                                <span>{data.insurance_portfolio.rejected_status.term}</span>
                            </div>
                        )}
                    </div>
                )} */}

                <div className="total-policies">
                    <span>Total policies active: {data.insurance_portfolio?.active_status?.match(/\d+/)?.[0] || '0'}</span>
                </div>
            </div>

            {/* Communication Preference */}
            <div className="communication-section">
                <h4>Communication preference</h4>
                <div className="comm-item">
                    <span className="comm-icon">📞</span>
                    <span>{data.communication?.call_preference_time || 'data not coming'}</span>
                </div>
                <div className="comm-item">
                    <span className="comm-icon">📶</span>
                    <span>{data.communication?.preferred_channel || 'data not coming'}</span>
                </div>
                <div className="comm-item">
                    <span className="comm-icon">🕐</span>
                    <span>{data.communication?.recent_interaction_call || 'data not coming'}</span>
                </div>
                <div className="know-more-btn">
                    <button>Know more</button>
                </div>
            </div>
        </div>
    );
}
