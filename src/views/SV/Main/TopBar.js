/// Topbar for <Main />,
/// Find header.js if looking for dialer and progressive bar

import { <PERSON>rid, Tooltip, Switch, ToggleButton } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { ErrorBoundary } from "../../../hoc";
import Search from "../Header/Search";
import CallBackDetails from "../RightBlock/CallBackDetails";
import CallTransfer from "../RightBlock/CallTransfer";
import VideoCallBtn from "./VideoCallBtn";
// import Vip from "./Vip";
import rootScopeService from "../../../services/rootScopeService";
import UpsellFund from "./UpsellFund";
import ActionButtons from "../Header/ActionButtons";
import AddToQueue from "./actions/AddToQueue";
import AddCar from "../RightBlock/AddCar";
import MarkImportant from "../RightBlock/MarkImportant";
import LastFiveLeads from "../RightBlock/LastFiveLeads";
import User from "../../../services/user.service";
import { hideCallButton, isFOSChurnAgent } from "../../../helpers";
import CallButton from "../RightBlock/CallButton";
import FOSAssignToCallCentre from "./FOSAssignToCallCentre";
import { ShowAddLeadbtn } from "../../../helpers/commonHelper";
import { PotentLeadsPanel } from "./Modals/PotentLeadsPanel";
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import HNICustPoliciesPopup from "./Modals/HNICustPoliciesPopup";
import { IsPBPrimeCustomerExist, UnlockPBPrime } from "../../../layouts/SV/components/Sidebar/helper/sidebarHelper";
import LockIcon from '@mui/icons-material/Lock';
import { enqueueSnackbar } from "notistack";

export default function TopBar() {
  let [ParentLeadId, bookingcancelreason, IsAddCarVisible, FOSLeadChurnReason, TotalCallDuration, IsHotLead, allLeads, parentLead] = useSelector(state => {
    let { parentLeadId, bookingcancelreason, IsAddCarVisible, FOSLeadChurnReason, TotalCallDuration, IsHotLead, allLeads, parentLead } = state.salesview;
    return [parentLeadId, bookingcancelreason, IsAddCarVisible, FOSLeadChurnReason, TotalCallDuration, IsHotLead, allLeads, parentLead]
  });
  const IsPriorityUser = rootScopeService.getPriority();
  const Isshow = !!ParentLeadId;
  const isMobile = useSelector(state => state.common.isMobile);
  const [Message, setMessage] = useState(null);
  const [currentPopup, setCurrentPopup] = useState('');
  const [primeCustomerStatus, setPrimeCustomerStatus] = useState(null); // null: not checked, true: prime, false: not prime
  const [toggleState, setToggleState] = useState(true); // false = OFF, true = ON
  let isValidInitialNoOfLives = useSelector(({ salesview }) => salesview.IsValidInitialNoOfLives);
  let SubstatusID = 0;

  const setSubstatusMessage = () => {
    if (SubstatusID == 2265) {
      setMessage("Reminder: Pitch Deductible to the customer ");
    }
    if (SubstatusID == 2273 || SubstatusID == 2262 || SubstatusID == 2284) {
      setMessage("Reminder: Pitch Deductible / port to the customer ");
    }
    if (SubstatusID == 2266) {
      setMessage("Reminder: Pitch Deductible to the customer ");
    }
  }

  const getSubStatus = () => {
    SubstatusID = allLeads.length > 0 ? allLeads[0].SubStatusId : 0;
    var R_num = 0;
    allLeads.forEach((vdata, key) => {
      if (vdata.LeadSourceId === 6 && [2, 3, 4, 11].includes(vdata.StatusId) && (R_num > vdata.R_Number || R_num === 0)) {
        SubstatusID = vdata.SubStatusId;
        R_num = vdata.R_Number;
      }
    });
    setSubstatusMessage();
  }

  const MultiyearAgentAssist = () => {
    var flag = 0
    allLeads.forEach((vdata, key) => {
      if (vdata.LeadSourceId === 6 && vdata.RenewalYear > 1 && [2, 3].includes(vdata.PreviousTerm)) {
        flag = 1
      }
    });
    if (flag == 1) {
      setMessage("Alert: Multiyear customer - Ensure customer continues with PB");
    }
  }

  const CheckHealthHNIGrp = () => {
    let usergrp = User.UserGroupList || [];
    if (usergrp.length > 0) {
      let IsHealthHNI = Array.isArray(usergrp) &&
        usergrp.some((item) =>
          typeof item.GroupName === "string" && item.GroupName.toLowerCase().includes("-hni-")
        );
      if (IsHealthHNI) { return true; }
      else { return false; }
    }
    return false;
  }

  const checkPrimeCustomerStatus = async () => {
    try {
      const customerId = rootScopeService.getCustomerId();
      if (!customerId) {
        setPrimeCustomerStatus(null);
        return;
      }

      IsPBPrimeCustomerExist(customerId).then((response) => {
        if (response && response.Status === true) {
          setPrimeCustomerStatus(response.Data);

          let isDisable = true;
          if(response.Data == false && (User && User.RoleId && User.RoleId == 13) && parentLead && parentLead.LeadAssignedUser && parentLead.LeadAssignedUser == User.UserId)
            isDisable = false;
          setToggleState(isDisable);
        } else {
          setPrimeCustomerStatus(null);
        }
      }).catch((error) => {
        setPrimeCustomerStatus(null);
      })
    }
    catch (error) {
      setPrimeCustomerStatus(null);
    }
  }

  const getCustomerIdFromUrl = () => {
    try {
      // Find object in allLeads where EnquiryID > 0
      if (allLeads && allLeads.length > 0) {
        const leadWithEnquiry = allLeads.find(lead => lead.EnquiryID && lead.EnquiryID > 0);

        if (!leadWithEnquiry || !leadWithEnquiry.ContinueJourneyURL) {
          return null;
        }

        // Parse the URL to extract custId parameter
        const url = new URL(leadWithEnquiry.ContinueJourneyURL);
        if (url) {
          const custId = url.searchParams.get('custId');

          if (!custId) {
            return null;
          }
          return custId;
        }
        else {
          return null;
        }
      }
      else {
        return null;
      }
    }
    catch (error) {
      return null;
    }
  }

  const handleUnlockPBPrime = async () => {
    try {
      setToggleState(true);

      const customerId = getCustomerIdFromUrl();
      if (!customerId) {
        return;
      }

      UnlockPBPrime(customerId, rootScopeService.getLeadId()).then((response) => {
        if (response && response.Status === true && response.Data === true) {
          // If unlock successful, change status to true (green background)
          setPrimeCustomerStatus(true);
          enqueueSnackbar("PB Prime Unlock - Sucess", { variant: 'success', autoHideDuration: 2000, });
        }
        else if(response && (response.Status == true || response.Status == false) && response.Message){
           enqueueSnackbar("PB Prime Unlock - " + response.Message, { variant: 'error', autoHideDuration: 2000, });
        }
      }).catch((error) => {
      })
    } catch (error) {
    }
  }

  useEffect(() => {
    getSubStatus()
    MultiyearAgentAssist()

    // Check prime customer status when leads change and we have PB Prime UTM content
    if (rootScopeService.getProductId() === 7) {
      checkPrimeCustomerStatus();
    } else {
      setPrimeCustomerStatus(null);
    }
  }, [allLeads]);
  return (
    <>
      <Grid item sm={6} md={4} xs={12}>
        <ErrorBoundary name="CallBackDetails ">
          <CallBackDetails />
        </ErrorBoundary>
      </Grid>
      {isMobile &&
        <ErrorBoundary name="ActionButtons">
          <ActionButtons isLeadOpen={Isshow} isMobile={isMobile} />
        </ErrorBoundary>
      }
      <Grid item sm={6} md={4} xs={12} lg={4}>
        {/* <Vip /> */}
        <div className={isMobile ? 'topbar-scroll-mobile' : 'dflex'}>
          <ErrorBoundary name="VideoCallBtn">
            <VideoCallBtn />
          </ErrorBoundary>
          {/* <ErrorBoundary name="PbLms">
            <PbLms />
          </ErrorBoundary> */}
          {SV_CONFIG && Array.isArray(SV_CONFIG.FOSProductConfig) && SV_CONFIG.FOSProductConfig.includes(rootScopeService.getProductId()) && isFOSChurnAgent(User)
            ? <>
              <ErrorBoundary name="FOSAssignToCallCentre">
                <FOSAssignToCallCentre />
              </ErrorBoundary>
            </>
            : <>

              {!IsPriorityUser ?
                <ErrorBoundary name="CallTransfer">
                  <CallTransfer isVisible={true} />
                </ErrorBoundary>
                : null
              }
              {/* <ErrorBoundary name="VideoCallBtn">
                <PbLms />
              </ErrorBoundary> */}
              <ErrorBoundary name="UpsellFund">
                <UpsellFund />
              </ErrorBoundary>
              {isMobile &&
                <>
                  {IsPriorityUser &&
                    <ErrorBoundary name="AddToQueue">
                      {Isshow && ShowAddLeadbtn() ? <AddToQueue hideText={IsAddCarVisible} /> : null}
                    </ErrorBoundary>
                  }
                  {
                    (!User.IsProgressive && <ErrorBoundary name="CallButton">
                      {Isshow && !hideCallButton(User) ? <CallButton />
                        : null}
                    </ErrorBoundary>)
                  }
                  <ErrorBoundary name="Addcar">
                    {Isshow && IsAddCarVisible ? <AddCar /> : null}
                  </ErrorBoundary>
                  <ErrorBoundary name="MarkImportant">
                    {Isshow ? <MarkImportant ParentLeadId={ParentLeadId} /> : null}
                  </ErrorBoundary>
                  <ErrorBoundary name="LastFiveLeads">
                    <LastFiveLeads />
                  </ErrorBoundary>
                </>
              }
              {(User.RoleId === 13 && rootScopeService.getProductId() === 131 && isValidInitialNoOfLives) &&
                <>
                  <ErrorBoundary name="SmeAnnualOpenLeadsPopupIcon">

                    <Tooltip title="Annual Open leads exist">
                      <NotificationsActiveIcon onClick={() => setCurrentPopup("SmeAnnualOpenLeadsPopup")} className="alartMsg" />
                    </Tooltip>

                  </ErrorBoundary>
                  <ErrorBoundary name="SmeAnnualOpenLeadsPopup">
                    <PotentLeadsPanel open={currentPopup === "SmeAnnualOpenLeadsPopup"} handleClose={() => { setCurrentPopup(null) }} />
                  </ErrorBoundary>
                </>
              }
            </>
          }



          {CheckHealthHNIGrp() === true
            && User?.RoleId === 13 && <>
              <ErrorBoundary name="HNICustPoliciesPopup">
                <HNICustPoliciesPopup />
              </ErrorBoundary>
            </>}

          {rootScopeService.getProductId() ===7 && (primeCustomerStatus == true || primeCustomerStatus == false) && 
            <div className="prime-toggle-container">
            <div className="prime-logo-section">
              <img
                src={CONFIG.PUBLIC_URL + "/images/Prime_logo.svg"}
                alt="Prime Logo"

              />
              <span className="available-text">
                Available
              </span>
            </div>
            <div className="vertical-divider"></div>
            <span className="off-text">
              OFF
            </span>
            <Switch
              checked={primeCustomerStatus}
              onChange={handleUnlockPBPrime}
              size="small"
              className="prime-toggle-switch"
              disabled={toggleState}
            />
            <span className="on-text">
              ON
            </span>
          </div>}


        </div>

      </Grid>
      {!isMobile && <Grid item sm={12} md={4} xs={12}>
        {(IsPriorityUser && ParentLeadId) ? <Search /> : null}
      </Grid>}

      {([7, 1000].includes(rootScopeService.getProductId()) && bookingcancelreason) &&
        <Grid item sm={12} md={12} xs={12} className="bookingCancelMsg"><h3>{bookingcancelreason.map((row, i) => (
          <h3>
            {row.LeadID} {" - "}
            {row.Reason}
          </h3>)
        )}</h3></Grid>

      }

      {
        SV_CONFIG && Array.isArray(SV_CONFIG.FOSProductConfig) && SV_CONFIG.FOSProductConfig.includes(rootScopeService.getProductId()) ?
          <>
            {FOSLeadChurnReason && FOSLeadChurnReason.status &&
              <Grid item sm={12} md={12} xs={12} className="FOSLeadChurnReason">
                <h3>
                  {FOSLeadChurnReason.message}
                </h3>
              </Grid>
            }
          </>
          : null
      }

      {TotalCallDuration >= 900 && IsHotLead && [2, 106, 130, 118].includes(rootScopeService.getProductId()) &&
        <Grid item sm={12} md={12} xs={12} className="HotLeadsTagging">
          <h3>
            {"High chance of renewal, check call records and comments to engage and renew"}
          </h3>
        </Grid>
      }
      {Message && [2, 106, 130, 118].includes(rootScopeService.getProductId()) &&
        <Grid item sm={12} md={12} xs={12} className="FOSLeadChurnReason">
          <h3>
            {Message}
          </h3>
        </Grid>
      }
    </>
  )
}