// Original MotorLeadSummaryCard styles
* {
  font-family: roboto;
}

.motor-lead-summary-card {
  border-radius: 16px;
  box-shadow: 0px 3px 12px 0px #0000001F;
  overflow: hidden;
  background: #F7FBFF;
  margin-top: 10px;

  .MuiCardContent-root {
    padding: 0;
  }
}

.lead-summary-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;

  .lead-summary-modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0;
  }

  .lead-summary-modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .lead-summary-modal-icon {
    width: 20px;
    height: 20px;
    color: #6b7280;
    cursor: pointer;

    &:hover {
      color: #374151;
    }
  }
}

.lead-summary-content {
  padding: 0px 5px;
  overflow-y: auto;
  height: 550px;
}

.lead-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 9px;
  padding-bottom: 12px;
  border-bottom: 1px dashed #0000001F;

  .lead-summary-title {
    font-size: 14px;
    font-weight: 600;
    color: #253858E3;
    margin-bottom: 8px;
  }

  .lead-summary-chips {
    display: flex;
    gap: 6px;
  }

  .lead-summary-chip {
    color: #253858E3;
    background-color: transparent;
    font-size: 11px;
    height: 18px;
    line-height: 16px;
    font-weight: 500;
    border-radius: 4px;
    border: 1px solid #253858E3;

    .MuiChip-label {
      padding: 0px 5px;
    }
  }
}

.lead-summary-section {
  background-color: #E5F0FF;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .lead-summary-section-title {
    color: #253858E3;
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 14px;
    text-align: center;
    font-family: roboto;
  }


}

.customer-requirement-section {
  background-color: #f0fdf4;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  margin-bottom: 12px;

  .customer-requirement-title {
    font-size: 14px;
    font-weight: 600;
    color: #253858E3;
    font-family: roboto;
    margin-bottom: 8px;
  }


}

.customer-concerns-section {
  background-color: #FEF3F0;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  margin-bottom: 12px;

  .customer-requirement-title {
    font-size: 14px;
    font-weight: 600;
    color: #253858E3;
    font-family: roboto;
    margin-bottom: 8px;
  }


}

.lead-summary-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.4;

  &:last-child {
    margin-bottom: 0;
  }

  .lead-summary-label {
    color: #253858E3;
    font-size: 12px;
    font-weight: 600;
    flex: 1;
    text-align: left;
    line-height: 18px;
    font-family: roboto;
  }

  .lead-summary-value {
    color: #253858E3;
    font-weight: 400;
    font-size: 12px;
    line-height: 19px;
    text-align: right;
    font-family: roboto;
    flex: 1;
  }
}

// Modal specific styles for CallSummaryModal
.call-summary-modal {
  .MuiDialog-paperWidthSm {
    max-height: calc(100% - 30px);
    width: 941px;
    margin: 0px;
    height: 100vh;

    .MuiDialogContent-root {
      padding-top: 0px !important;

    }

    h6 {
      text-align: center;
      font-family: Roboto;
      font-weight: 600;
      font-size: 24px;
      line-height: 100%;
      color: #253858E3;
    }
  }


  .lead-summary-content {
    padding: 0;
    height: auto;
    overflow-y: visible;
  }

  // MUI Accordion overrides for exact image layout
  .MuiAccordion-root {
    border: none;
    margin-bottom: 12px;
    box-shadow: none;
    overflow: hidden;

    &:before {
      display: none;
    }

    &.Mui-expanded {
      margin-bottom: 12px;
    }
  }

  .MuiAccordionSummary-root {
    background: #FFFFFF;
    padding: 0px 0px;
    min-height: 48px;
    border-bottom: 1px solid #E0E7FF;

    &.Mui-expanded {
      min-height: 48px;
      border-bottom: 1px solid #E0E7FF;
    }

    .MuiAccordionSummary-content {
      align-items: center;
      margin: 0;

      &.Mui-expanded {
        margin: 0;
      }
    }

    .MuiAccordionSummary-expandIconWrapper {
      color: #4F46E5;

      &.Mui-expanded {
        transform: rotate(180deg);
      }
    }
  }

  .MuiAccordionDetails-root {
    padding: 16px 0px;
    background: #FFFFFF;
  }

  .accordion-header-content {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 10px;

    .accordion-date-text {
      font-size: 24px;
      font-weight: 500;
      color: #253858E3;
      line-height: 1.2;
      font-family: roboto;
    }

    .accordion-time-text {
      font-size: 24px;
      color: #25385899;
      font-weight: 400;
      line-height: 1.2;
      font-family: roboto;
    }
  }

  // Ensure sections have proper spacing
  .lead-summary-section,
  .customer-requirement-section,
  .customer-concerns-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Private Car Section - Horizontal Layout
  .private-car-section-horizontal {
    background-color: #E5F0FF;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .private-car-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px dashed #0000001F;

      .private-car-title-section {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .private-car-title {
        font-size: 14px;
        font-weight: 600;
        color: #253858E3;
        font-family: roboto;
      }

      .private-car-chips {
        display: flex;
        gap: 6px;
      }

      .private-car-chip {
        color: #253858E3;
        background-color: transparent;
        font-size: 11px;
        height: 18px;
        line-height: 16px;
        font-weight: 500;
        border-radius: 4px;
        border: 1px solid #253858E3;

        .MuiChip-label {
          padding: 0px 5px;
        }
      }
    }

    .private-car-details-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px 45px;

      .private-car-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;


        .private-car-label {
          color: #253858E3;
          font-size: 12px;
          font-weight: 600;
          font-family: roboto;
          flex: 1;
          text-align: left;
        }

        .private-car-value {
          color: #253858E3;
          font-weight: 400;
          font-size: 12px;
          font-family: roboto;

          flex: 1;
          text-align: right;
        }
      }
    }
  }

  // Grid Layout Styles
  .grid-layout-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    margin-top: 16px;
  }

  .grid-section {
    border-radius: 8px;
    padding: 12px;

    &.policy-details-grid {
      background-color: #E5F0FF;
    }

    &.customer-requirement-grid {
      background-color: #f0fdf4;
    }

    &.customer-concerns-grid {
      background-color: #FEF3F0;
    }

    .grid-section-title {
      font-size: 14px;
      font-weight: 600;
      color: #253858E3;
      font-family: roboto;
      margin-bottom: 12px;
      text-align: center;
    }



    .addon-list {
      font-size: 12px;
      color: #253858E3;
      font-weight: 400;
      text-align: right;
      font-family: roboto;
      flex: 1;

      div {
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .communication-preference {
      display: flex;
      align-items: center;
      gap: 4px;
      justify-content: flex-end;
      flex: 1;
      background-color: #F0F6FE;
      padding: 4px 8px;
      border-radius: 4px;

      .communication-preference-icon {
        font-size: 12px !important;
        color: #007BFF;
      }

      .communication-preference-text {
        font-size: 12px !important;
        color: #007BFF !important;
        font-weight: 500 !important;
        font-family: roboto !important;
      }
    }
  }
}


.communication-preference {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: flex-end;
  flex: 1;
  background-color: #F0F6FE;
  padding: 4px 8px;
  border-radius: 4px;

  .communication-preference-icon {
    font-size: 12px !important;
    color: #007BFF;
  }

  .communication-preference-text {
    font-size: 12px !important;
    color: #007BFF !important;
    font-weight: 500 !important;
    font-family: roboto !important;
  }
}


.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;

}
.addon-list {
  font-size: 12px;
  color: #253858E3;
  font-weight: 400;
  text-align: right;
  flex: 1;
  font-family: roboto;

  div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.communication-preference {
  display: flex;
  align-items: center;
  gap: 4px;
}