import { FormControlLabel, Grid, Radio, RadioGroup, Switch } from "@mui/material";
import { ExpandMore } from "@mui/icons-material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { SV_CONFIG } from "../../../appconfig";
import { TextInput } from "../../../components";
import SelectDropdown from "../../../components/SelectDropdown";
import { CALL_API } from "../../../services/api.service";
import { SetCustomerComment, SetLeadAudit } from "../../../services/Common";
import rootScopeService from "../../../services/rootScopeService";
import User from "../../../services/user.service";
import { setRefreshLead, updateStateInRedux } from "../../../store/actions/SalesView/SalesView";

let ProductInfo = {
    SmartCollect: false,
    IsDiabetic: false,
    IsABSLIApptCreated: false,
    NoCostEMI: false,
    IsPotentialBuyer: false
};

let MotorInfo = {
    PreviousClaimed: true,
    PrivateVehicle: true,
    SelfOwn: true,
    PolicyType: true,
    NoCostEMI: false,
    AddOn: "",
    SmartCollect: false
};
let HealthInfo = {
    IsPreExistsDiseas: false,
    PreExistsDiseas: "",
    ReqSumInsured: "",
    IsOldPolicy: false,
    OldPolicy: "",
    EMIReq: true,
    IsIHOPackoffered: 0,
    Maternity: false,
    NoCostEMI: false,
    MonthlyModeEMI: false,
    ClaimAssistance: false,
    SmartCollect: false
};
let HomeInfo = {
    Loan: false,
    homeType: "",
    PreferredInsurer: "",
    Other: "",
    SumInsured: "",
    LPremium: "",
    PolicyTerm: "",
    SmartCollect: false,
    NoCostEMI: false
};

const AddInfo = (props) => {
    const { enqueueSnackbar } = useSnackbar();
    const [show, setShow] = useState(false);
    const [IsNoCostEMI, setIsNoCostEMI] = useState(false);
    const [IsMonthlyMode, setIsMonthlyMode] = useState(true);
    const [IsClaimAssistance, setIsClaimAssistance] = useState();
    const [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const [ParentLeadId, PrimaryLeadId] = useSelector(state => {
        let { parentLeadId, primaryLeadId, leadIds } = state.salesview;
        return [parentLeadId, primaryLeadId, leadIds];
    });
    const [allLeads, IsBrandNewCar] = useSelector(state => {
        let { allLeads, IsBrandNewCar } = state.salesview;
        return [allLeads, IsBrandNewCar]
    });
    const [NewMotorInfo, setNewMotorInfo] = useState(MotorInfo);
    const [originalInfo, setOriginalInfo] = useState({});
    const [NewHealthInfo, setNewHealthInfo] = useState(HealthInfo);
    const [NewHomeInfo, setNewHomeInfo] = useState(HomeInfo);
    const [NewProductInfo, setNewProductInfo] = useState(ProductInfo);
    const [IsMotorGroup, setIsMotorGroup] = useState(false);
    const [IsSTUGroup, setIsSTUGroup] = useState(false);
    const [IsConfirmationPopupShow, setIsConfirmationPopupShow] = useState(false);
    const [IsDiabeticSwitchDisable, setIsDiabeticSwitchDisable] = useState(false);
    const [IsEnableHomeOther, setIsEnableHomeOther] = useState(false);
    const [IsPotentialCustomerType, setIsPotentialCustomerType] = useState(false);
    const ShowMotorAddInfo = false;

    const productId = rootScopeService.getProductId();
    let AddOns = ["Zero Depreciation", "RSA", "Engine Protector", "Consumable", "NCB Protector"];
    const HomeTypes = ["Structure", "Content", "Structure and Content"];
    const PreferredInsurerList = ["HDFC", "Bajaj", "SBI", "Sriram", " ICICI", "Others"];
    const reduxDispatch = useDispatch();
    let ShowSmeCustomerType = useSelector(state => state.salesview.ShowSmeCustomerType);
    let SmePotentialBuyerSubProducts = SV_CONFIG["SmePotentialBuyerSubProducts"] ? SV_CONFIG["SmePotentialBuyerSubProducts"] : [];
    let SmePotentialBuyerLabel = SV_CONFIG["SmePotentialBuyerLabel"] ? SV_CONFIG["SmePotentialBuyerLabel"] : '';
    let ShowMotorNeedAnalysis = SV_CONFIG["ShowMotorNeedAnalysis"];

    const disabled = props.disabled;

    const handleChange = (e) => {
        const { name, value, type } = e.target;
        if (disabled) {
            enqueueSnackbar("You are not authorized to perform this action!", { variant: "warning", autoHideDuration: 3000 });
            return;
        }
        if (productId === 117) {
            switch (name) {
                case "AddOn":
                    setNewMotorInfo({ ...NewMotorInfo, [name]: value });
                    break;
                default:
                    // Handle binary inputs
                    setNewMotorInfo({ ...NewMotorInfo, [name]: !NewMotorInfo[name] });
                    break;
            }
        }
        else if (productId === 139) {
            switch (name) {
                default:
                    // Handle binary inputs
                    setNewMotorInfo({ ...NewMotorInfo, [name]: !NewMotorInfo[name] });
                    break;
            }
        }
        else if (productId === 2) {
            // switch (name) {
            //     case "ReqSumInsured":
            //     case "PreExistsDiseas":
            //     case "OldPolicy":
            //         setNewHealthInfo({ ...NewHealthInfo, [name]: value });
            //         break;
            //     default:
            //         // Handle binary inputs
            //         setNewHealthInfo({ ...NewHealthInfo, [name]: !NewHealthInfo[name] });
            //         break;
            // }
            if (['checkbox'].includes(type)) {
                setNewHealthInfo({ ...NewHealthInfo, [name]: !NewHealthInfo[name] });
            }
            else {
                setNewHealthInfo({ ...NewHealthInfo, [name]: value });
            }
        }
        else if (productId === 101) {
            if (['checkbox'].includes(type)) {
                setNewHomeInfo({ ...NewHomeInfo, [name]: !NewHomeInfo[name] });
            }
            else {
                setNewHomeInfo({ ...NewHomeInfo, [name]: value });
            }
        }
        else {
            setNewProductInfo({ ...NewProductInfo, [name]: !NewProductInfo[name] });
        }
    }

    const SaveComment = (Comment) => {
        let UserId = User.UserId;

        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            ParentLeadId,
            PrimaryLeadId,
            UserId,
            "Comment": Comment,
            "EventType": 12
        };
        SetCustomerComment(requestData);
    }
    const SetLeadAudits = (NewInfo, oldInfo) => {
        let FieldsData = require("../../../assets/json/FieldsData").default;
        let reqDataAudit = [];
        let invalid = [undefined, null, 0, "", "0"];
        if (rootScopeService.getProductId() !== 115) {
            for (const [key, vdata] of Object.entries(NewInfo)) {

                var fieldVal = FieldsData.Field[key];
                if (vdata != oldInfo[key] && fieldVal !== undefined && !(invalid.indexOf(vdata) >= 0 && invalid.indexOf(oldInfo[key]) >= 0)) {
                    reqDataAudit.push({
                        LeadId: ParentLeadId,
                        AgentId: User.UserId || 19414,
                        SectionName: "AddInfo",
                        Field: String(fieldVal),
                        OldValue: String(oldInfo[key]),
                        NewValue: String(vdata),
                        ProductId: rootScopeService.getProductId()
                    });
                }
            }
        }
        // else {
        //     var Section = $scope.SubProdType == 1 ? "GrowthAddInfo" : $scope.SubProdType == 2 ? "RetireAddInfo" : "ChildAddInfo";
        //     angular.forEach(NewInfo, function (vdata, key) {
        //         var fieldVal = FieldsData.Field[key];
        //         if (vdata.AnswerValue.Answer != oldInfo[key].AnswerValue.Answer && fieldVal != undefined && !(invalid.indexOf(vdata.AnswerValue.Answer) >= 0 && invalid.indexOf(oldInfo[key].AnswerValue.Answer) >= 0)) {
        //             reqDataAudit.detail.push({
        //                 LeadId: rootScopeService.getParentLeadId(),
        //                 AgentId: rootScopeService.getUserId(),
        //                 SectionName: Section,
        //                 Field: fieldVal,
        //                 OldValue: oldInfo[key].AnswerValue.Answer,
        //                 NewValue: vdata.AnswerValue.Answer,
        //                 ProductId: rootScopeService.getProductId()
        //             });
        //         }
        //     })
        // }
        if (reqDataAudit.length > 0) {
            SetLeadAudit(reqDataAudit).then((result) => {
                if (result && result.IsSaved) {
                    enqueueSnackbar("Lead Audit Details Saved", { variant: 'success', autoHideDuration: 3000, });
                }
            }, function () {
                console.log("Lead Audit not saved");
            })

        }
    }
    /************  Motor, 117 ***************/
    const getMotorInfo = () => {
        const CustomerId = rootScopeService.getCustomerId();
        const ParentId = ParentLeadId;

        const input = {
            url: `coremrs/api/LeadDetails/GetMotorNeedAnalysis/${CustomerId}/${ParentId}`,
            method: 'GET', service: 'MatrixCoreAPI',
        };

        CALL_API(input).then((response) => {
            if (response) {
                setNewMotorInfo({
                    PreviousClaimed: response.PreviousClaimed,
                    PrivateVehicle: response.PrivateVehicle,
                    SelfOwn: response.SelfOwn,
                    PolicyType: response.PolicyType === "Third Party" ? true : false,
                    NoCostEMI: response.NoCostEMI,
                    AddOn: response.AddOn,
                    SmartCollect: Boolean(response.SmartCollect)
                });
                setOriginalInfo({
                    PreviousClaimed: response.PreviousClaimed,
                    PrivateVehicle: response.PrivateVehicle,
                    SelfOwn: response.SelfOwn,
                    PolicyType: response.PolicyType === "Third Party" ? true : false,
                    NoCostEMI: response.NoCostEMI,
                    AddOn: response.AddOn,
                    SmartCollect: Boolean(response.SmartCollect)
                });
            }
            else setNewMotorInfo(MotorInfo);
        }, function () {
            // setNewMotorInfo(MotorInfo)
        });
    };
    const UpdateMotorInfo = () => {
        const reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            // PreviousClaimed: NewMotorInfo.PreviousClaimed,
            PrivateVehicle: NewMotorInfo.PrivateVehicle,
            SelfOwn: NewMotorInfo.SelfOwn,
            // PolicyType: NewMotorInfo.PolicyType ? "Third Party" : "Comprehensive",
            NoCostEMI: NewMotorInfo.NoCostEMI,
            // AddOn: NewMotorInfo.AddOn,
            CreatedBy: User.UserId,
            ParentId: ParentLeadId,
            // SmartCollect: NewMotorInfo.SmartCollect === true ? 1 : 0
        };
        const input = {
            url: `coremrs/api/LeadDetails/SetMotorNeedAnalysis`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        CALL_API(input).then((resultData) => {
            if (resultData) {
                SetLeadAudits(NewMotorInfo, originalInfo);
                getMotorInfo();
                SaveComment("Customer Additional info Updated.");
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 2000,
                });
            }
        });
    }
    /************  Motor ends ***************/
    /************  Commercial starts ***************/
    const UpdateCommercialInfo = () => {
        const reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            // PreviousClaimed: NewMotorInfo.PreviousClaimed,
            // PrivateVehicle: NewMotorInfo.PrivateVehicle,
            // SelfOwn: NewMotorInfo.SelfOwn,
            // PolicyType: NewMotorInfo.PolicyType ? "Third Party" : "Comprehensive",
            NoCostEMI: NewMotorInfo.NoCostEMI,
            // AddOn: NewMotorInfo.AddOn,
            CreatedBy: User.UserId,
            ParentId: ParentLeadId,
            SmartCollect: NewMotorInfo.SmartCollect === true ? 1 : 0
        };
        const input = {
            url: `coremrs/api/LeadDetails/SetMotorNeedAnalysis`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        CALL_API(input).then((resultData) => {
            if (resultData) {
                SetLeadAudits(NewMotorInfo, originalInfo);
                getMotorInfo();
                SaveComment("Customer Additional info Updated.");
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 2000,
                });
            }
        });
    }
    /************  Commercial ends ***************/
    /************  health, 2 ***************/

    const ValidateforEditRenewal = () => {
        var isRenewalAgent = false;
        var UserBUMappingList = User.UserBUMapping;
        if (UserBUMappingList !== undefined && UserBUMappingList !== "") {
            UserBUMappingList.forEach(function (val, key) {
                if (val.IsRenewal && ([106, 118, 130, 2].indexOf(val.ProductId) !== -1)) {
                    isRenewalAgent = true;
                }
            });
        }
        return isRenewalAgent;
    }

    const setHealthNeedAnalysisService = (reqData) => {
        const input = {
            url: `coremrs/api/LeadDetails/SetHealthNeedAnalysis/`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        };
        return CALL_API(input);
    }
    const getHealthInfo = () => {
        setIsNoCostEMI(false);
        setIsClaimAssistance("Additional-details");
        var filterArray = allLeads.filter((item) => {
            return item.LeadSource === "Renewal";
        });
        if (filterArray.length > 0) {
            setIsNoCostEMI(true);
            setIsClaimAssistance("Additional-details claim");
        }
        if (SV_CONFIG["NoCostEnableUsers"][SV_CONFIG["environment"]].indexOf(User.UserId) !== -1) {
            setIsNoCostEMI(false);
        }
        else if (ValidateforEditRenewal()) {
            setIsNoCostEMI(false);
        }

        if (User.GroupProcessId === "7") {
            setIsMonthlyMode(false);
        }

        const input = {
            url: `coremrs/api/LeadDetails/GetHealthNeedAnalysis/` + rootScopeService.getCustomerId() + '/' + ParentLeadId + '/1',
            method: 'GET', service: 'MatrixCoreAPI'
        };
        CALL_API(input).then((response) => {
            response = response.Data;
            if (response != null) {
                setNewHealthInfo({
                    ReqSumInsured: response.ReqSumInsured,
                    IsPreExistsDiseas: response.PreExistsDiseas !== "",
                    PreExistsDiseas: response.PreExistsDiseas,
                    IsOldPolicy: response.OldPolicy !== "",
                    OldPolicy: response.OldPolicy,
                    EMIReq: response.EMIReq,
                    Maternity: response.Maternity,
                    NoCostEMI: response.NoCostEMI == 1,
                    MonthlyModeEMI: response.MonthlyModeEMI,
                    IsIHOPackoffered: response.IsIHOPackoffered,
                    ClaimAssistance: response.ClaimAssistance,
                    SmartCollect: Boolean(response.SmartCollect)
                });
                setOriginalInfo({
                    ReqSumInsured: response.ReqSumInsured,
                    IsPreExistsDiseas: response.PreExistsDiseas !== "",
                    PreExistsDiseas: response.PreExistsDiseas,
                    IsOldPolicy: response.OldPolicy !== "",
                    OldPolicy: response.OldPolicy,
                    EMIReq: response.EMIReq,
                    Maternity: response.Maternity,
                    NoCostEMI: response.NoCostEMI == 1,
                    MonthlyModeEMI: response.MonthlyModeEMI,
                    IsIHOPackoffered: response.IsIHOPackoffered,
                    ClaimAssistance: response.ClaimAssistance,
                    SmartCollect: Boolean(response.SmartCollect)
                });
            }
            else setNewHealthInfo(HealthInfo);
        }, function () {
            setNewHealthInfo(HealthInfo);
        });
    };
    const UpdateHealthInfo = () => {
        var errormsg = "";

        if (['', undefined].indexOf(NewHealthInfo.ReqSumInsured) === -1 && (isNaN(parseFloat(NewHealthInfo.ReqSumInsured)))) {
            errormsg = errormsg + "Enter Sum Insured in Valid Format. \n";
        }
        if (NewHealthInfo.PreExistsDiseas === "" && NewHealthInfo.IsPreExistsDiseas) {
            errormsg = errormsg + "Enter Pre existing diseases. \n";
        }
        if (NewHealthInfo.OldPolicy === "" && NewHealthInfo.IsOldPolicy) {
            errormsg = errormsg + "Enter Policy Details. \n";
        }
        if (errormsg !== "") {
            enqueueSnackbar(errormsg, {
                variant: 'error',
                autoHideDuration: 4000,
                style: { whiteSpace: 'pre-line' },
            });
            return;
        }
        const reqData = {

            CustomerId: rootScopeService.getCustomerId(),
            ReqSumInsured: !NewHealthInfo.ReqSumInsured ? 0 : NewHealthInfo.ReqSumInsured,
            PreExistsDiseas: NewHealthInfo.IsPreExistsDiseas ? NewHealthInfo.PreExistsDiseas : "",
            OldPolicy: NewHealthInfo.IsOldPolicy ? NewHealthInfo.OldPolicy : "",
            EMIReq: NewHealthInfo.EMIReq,
            Maternity: NewHealthInfo.Maternity,
            NoCostEMI: NewHealthInfo.NoCostEMI ? 1 : 0,
            MonthlyModeEMI: NewHealthInfo.MonthlyModeEMI,
            ClaimAssistance: NewHealthInfo.ClaimAssistance,
            CreatedBy: User.UserId,
            IsIHOPackoffered: NewHealthInfo.IsIHOPackoffered,
            ParentId: ParentLeadId,
            SmartCollect: NewHealthInfo.SmartCollect === true ? 1 : 0,
            RenewalFlag: 1

        };
        setHealthNeedAnalysisService(reqData).then(function (resultData) {
            if (resultData) {
                SetLeadAudits(NewHealthInfo, originalInfo);
                getHealthInfo();
                SaveComment("Customer Additional info Updated.");
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 3000,
                });
            }
        }, function () {
            enqueueSnackbar('Something went wrong, Please Connect the Support team.', {
                variant: 'error',
                autoHideDuration: 3000,
            });
        });
    }
    /************  health end ***************/

    /************  home, 101 ***************/
    const GetHomeNeedAnalysisService = (ParentID) => {
        const input = {
            url: `coremrs/api/LeadDetails/GetHomeNeedAnalysis?ParentId=` + ParentID,
            method: 'GET', service: 'MatrixCoreAPI'
        };
        return CALL_API(input);
    }
    const GetHomeNeedAnalysis = function () {

        var CustomerId = rootScopeService.getCustomerId();
        var ParentId = ParentLeadId;

        GetHomeNeedAnalysisService(ParentId).then(function (response) {
            if (response != null) {
                if (response != null && response.Others != '' && response.PreferredInsurer === 'Others') {
                    setIsEnableHomeOther(true);
                }
                else {
                    setIsEnableHomeOther(false);
                }

                setNewHomeInfo({
                    Loan: response.Loan,
                    HomeType: response.HomeType,
                    PreferredInsurer: response.PreferredInsurer,
                    Other: response.Others,
                    SumInsured: response.SumInsured,
                    LPremium: response.LPremium,
                    PolicyTerm: response.PolicyTerm,
                    SmartCollect: Boolean(response.SmartCollect),
                    NoCostEMI: Boolean(response.NoCostEMI)

                });
                setOriginalInfo({
                    Loan: response.Loan,
                    HomeType: response.HomeType,
                    PreferredInsurer: response.PreferredInsurer,
                    Other: response.Others,
                    SumInsured: response.SumInsured,
                    LPremium: response.LPremium,
                    PolicyTerm: response.PolicyTerm,
                    SmartCollect: Boolean(response.SmartCollect),
                    NoCostEMI: Boolean(response.NoCostEMI)
                })
            };

        }, function () {
            // $scope.MotorInfo = [];
        });
    }
    const OnSlctHomeInsurer = (PreferredInsurer) => {
        if (PreferredInsurer === "Others") {
            setIsEnableHomeOther(true);
        }
        else {
            setIsEnableHomeOther(false);
        }
    }
    const checkIsMotorGroup = () => {
        var usergrp = User.UserGroupList;
        usergrp.forEach(function (item, key) {
            if (SV_CONFIG["MotorGroupNOCostEMI"][SV_CONFIG["environment"]].indexOf(item.GroupId) > -1) {
                setIsMotorGroup(true);
                return;
            }
        });

    }

    const checkSTUGroup = () => {
        var usergrp = User.UserGroupList;
        usergrp.forEach(function (item, key) {
            if ([2903].indexOf(item.GroupId) > -1) {
                setIsSTUGroup(true);
                return;
            }
        });
    }

    useEffect(() => {
        OnSlctHomeInsurer(NewHomeInfo.PreferredInsurer);
    }, [NewHomeInfo.PreferredInsurer]);

    const SetHomeNeedAnalysisService = (requestData) => {
        const input = {
            url: `coremrs/api/LeadDetails/SetHomeNeedAnalysis`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData
        };
        return CALL_API(input);
    }

    const SetHomeAnalysis = () => {
        const reqData = {
            CustomerId: rootScopeService.getCustomerId(),
            Loan: NewHomeInfo.Loan,
            HomeType: NewHomeInfo.HomeType,
            PreferredInsurer: NewHomeInfo.PreferredInsurer,
            Others: NewHomeInfo.PreferredInsurer === "Others" ? NewHomeInfo.Other : "",
            //Others: NewHomeInfo.Other,
            SumInsured: NewHomeInfo.SumInsured,
            LPremium: NewHomeInfo.LPremium !== "" ? NewHomeInfo.LPremium : 0,
            PolicyTerm: NewHomeInfo.PolicyTerm !== "" ? NewHomeInfo.PolicyTerm : 0,
            CreatedBy: User.UserId,
            ParentId: ParentLeadId,
            SmartCollect: NewHomeInfo.SmartCollect === true ? 1 : 0,
            NoCostEMI: NewHomeInfo.NoCostEMI === true ? 1 : 0
        };
        SetHomeNeedAnalysisService(reqData).then((resultData) => {
            if (resultData) {
                SetLeadAudits(NewHomeInfo, originalInfo);
                GetHomeNeedAnalysis();
                SaveComment("Customer Additional info Updated.");
                enqueueSnackbar("Details Updated Successfully.", {
                    autoHideDuration: 3000,
                    variant: "success"
                });
            }
        }, function () {
            enqueueSnackbar("Something went wrong, Please Connect to Support team.", {
                autoHideDuration: 3000,
                variant: "error"
            });
        });
    };
    /************  home end ***************/


    /***********other products **********/

    const GetConfirmationResponse = () => {
        // if (NewProductInfo.IsDiabetic === true && NewProductInfo.IsDiabetic != originalInfo.IsDiabetic) {
        //     setIsConfirmationPopupShow(true);
        // }
        // else {
        if (NewProductInfo.IsABSLIApptCreated != originalInfo.IsABSLIApptCreated && productId == 115) {
            SaveFeildAuditHistory("ABSLI Appointment Created", 60);
        }
        if (NewProductInfo.IsPotentialBuyer && !window.confirm('Are you sure you want to mark the customer as a Potential Repeat Buyer?')) {
            return;
        }

        UpdateProductInfo();
        //}
    }
    const getProductInfo = () => {
        const input = {
            url: 'coremrs/api/MRSCore/GetProductNeedAnalysis?ParentId=' + ParentLeadId,
            method: 'GET',
            service: 'MatrixCoreAPI',
        }
        CALL_API(input).then((response) => {
            if (response) {
                setNewProductInfo({
                    SmartCollect: Boolean(response.SmartCollect),
                    IsDiabetic: Boolean(response.IsDiabetic),
                    IsABSLIApptCreated: Boolean(response.IsABSLIApptCreated),
                    NoCostEMI: Boolean(response.NoCostEMI),
                    IsPotentialBuyer: Boolean(ShowSmeCustomerType)
                });
                setOriginalInfo({
                    SmartCollect: Boolean(response.SmartCollect),
                    IsDiabetic: Boolean(response.IsDiabetic),
                    IsABSLIApptCreated: Boolean(response.IsABSLIApptCreated),
                    NoCostEMI: Boolean(response.NoCostEMI),
                    IsPotentialBuyer: Boolean(ShowSmeCustomerType)
                });
                Boolean(response.IsDiabetic) === true ? setIsDiabeticSwitchDisable(true) : setIsDiabeticSwitchDisable(false);
            }
            else setNewProductInfo(ProductInfo);
        }, function () {
            // setNewProductInfo(ProductInfo)
        });
    };
    const UpdateProductInfo = () => {
        setIsConfirmationPopupShow(false);
        NewProductInfo.IsDiabetic === true ? setIsDiabeticSwitchDisable(true) : setIsDiabeticSwitchDisable(false);

        const reqData = {
            LeadID: ParentLeadId,
            SmartCollect: NewProductInfo.SmartCollect === true ? 1 : 0,
            IsDiabetic: NewProductInfo.IsDiabetic === true ? 1 : 0,
            IsABSLIApptCreated: NewProductInfo.IsABSLIApptCreated === true ? 1 : 0,
            CustomerId: rootScopeService.getCustomerId(),
            ProductId: rootScopeService.getProductId(),
            CreatedBy: User.UserId,
            NoCostEMI: NewProductInfo.NoCostEMI === true ? 1 : 0,
            CustomerType: NewProductInfo.IsPotentialBuyer ? "PotentialBuyer" : "NonPotentialBuyer"
        };
        const input = {
            url: `coremrs/api/MRSCore/SetProductNeedAnalysis`,
            method: 'POST', service: 'MatrixCoreAPI',
            requestData: reqData
        }
        CALL_API(input).then((resultData) => {
            if (resultData) {
                SetLeadAudits(NewProductInfo, originalInfo);
                getProductInfo();
                SaveComment("Customer Additional info Updated.");
                enqueueSnackbar("Details Updated Successfully.", {
                    variant: 'success',
                    autoHideDuration: 2000,
                });

                if (NewProductInfo.IsPotentialBuyer) {
                    reduxDispatch(updateStateInRedux({ key: "ShowSmeCustomerType", value: true }));
                    reduxDispatch(setRefreshLead({ RefreshLead: true }));
                }
                else if (!NewProductInfo.IsPotentialBuyer) {
                    reduxDispatch(updateStateInRedux({ key: "ShowSmeCustomerType", value: false }));
                    reduxDispatch(setRefreshLead({ RefreshLead: true }));
                }
            }
        });
    }

    /************ other products end *************/

    const handleToggle = (e) => {
        setShow(!show);
        if (productId === 117) {
            checkIsMotorGroup();
            getMotorInfo();

        }
        else if (productId === 139) {
            getMotorInfo();
        }
        else if (productId === 2) {
            getHealthInfo();
        }
        else if (productId === 101)
            GetHomeNeedAnalysis();
        else {
            getProductInfo();
            checkSTUGroup();
        }
    }
    useEffect(() => {
        if (RefreshLead) {
            setShow(false);
        }
    }, [RefreshLead]);
    const handleClose = () => {
        setIsConfirmationPopupShow(false);
    }
    const CheckIsDiabeticGrpEligibleCust = () => {
        let ParentLeadInfo = Array.isArray(allLeads) ? allLeads.filter((item) => item.LeadID == item.ParentID) : [];
        if (ParentLeadInfo.length > 0) {
            if (ParentLeadInfo[0].Age && parseInt(ParentLeadInfo[0].Age.match(/\(([^)]+)\)/)[1]) >= 30 && parseInt(ParentLeadInfo[0].Age.match(/\(([^)]+)\)/)[1]) <= 55
                && ParentLeadInfo[0].ProfessionType && ['salaried', 'self employed'].indexOf(ParentLeadInfo[0].ProfessionType.toLowerCase()) > -1
                && ParentLeadInfo[0].QualificationId && [1, 2].indexOf(ParentLeadInfo[0].QualificationId) > -1) {
                return true;
            }
            else { return false; }
        }
        return false;
    }
    const SaveFeildAuditHistory = (comment, EventType) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            ParentLeadId,
            PrimaryLeadId,
            UserId,
            "Comment": comment,
            "EventType": EventType
        };
        SetCustomerComment(requestData);
    }

    const NoCostEmiString = (productId === 117 && User?.UserGroupList?.some(item => item.GroupId === 2703)) ? "NoCostEMI (>=12,000)" : "NoCostEMI (>=15,000)";
    //For Term leads , if call duration exceeds 200 secs , and age is between 30 to 55 yrs and 
    // useInterval(function () {
    //     let ConnectCallSF = window.localStorage.getItem('ConnectCallSF');
    //     //var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
    //     if (ConnectCallSF) {
    //       ConnectCallSF = JSON.parse(ConnectCallSF);
    //       var CallinitateTime = ConnectCallSF.CallInitTime;
    //       let durationTime = 0;
    //       if (CallinitateTime) {
    //         durationTime = (new Date() - new Date(CallinitateTime)) / 1000;
    //       }

    //       //-----------Show Is Diabetic toggle to agent -----------------//
    //       if (durationTime >= 200 && CheckIsDiabeticGrpEligibleCust() == true && [7].indexOf(rootScopeService.getProductId()) > -1) {
    //         setShowIsDiabeticToggle(true);
    //       }
    //       else
    //       {
    //         setShowIsDiabeticToggle(false);
    //       }
    //   }}, 2000);

    return <>
        <div className="addInfo">
            <h3>Additional Info</h3>
            <div className="expandmoreIcon">
                <ExpandMore onClick={handleToggle} style={{ transform: show ? 'rotate(180deg)' : 'rotate(0deg)' }} /></div>
            <p className="caption">Other additional information related to case </p>
            {productId === 117 && show && ShowMotorNeedAnalysis &&
                <>
                    {ShowMotorAddInfo && <div className="Additional-details">
                        <div>Previously Claimed ?</div>
                        <div>
                            {NewMotorInfo.PreviousClaimed ? "Yes" : "No"}
                            <Switch
                                checked={NewMotorInfo.PreviousClaimed}
                                onChange={handleChange}
                                name="PreviousClaimed"
                            />
                        </div>
                    </div>}
                    <div className="Additional-details">
                        <div>Private Vehicle ?</div>
                        <div>
                            {NewMotorInfo.PrivateVehicle ? "Yes" : "No"}
                            <Switch
                                checked={NewMotorInfo.PrivateVehicle}
                                onChange={handleChange}
                                name="PrivateVehicle"
                            />
                        </div>
                    </div>
                    <div className="Additional-details">
                        <div>SelfOwn ?</div>
                        <div>
                            {NewMotorInfo.SelfOwn ? "Yes" : "No"}
                            <Switch
                                checked={NewMotorInfo.SelfOwn}
                                onChange={handleChange}
                                name="SelfOwn"
                            />
                        </div>
                    </div>

                    {ShowMotorAddInfo && <div>
                        <div>Policy Type?</div>
                        <div className="radioDesign">
                            <RadioGroup name="PolicyType" value={NewMotorInfo.PolicyType} onChange={handleChange}>
                                <FormControlLabel value={true} control={<Radio />} label="Third Party" />
                                <FormControlLabel value={false} control={<Radio />} label="Comprehensive" />
                            </RadioGroup>
                        </div>
                    </div>}

                    {((IsBrandNewCar || User?.UserGroupList?.some(item => item.GroupId === 2703)) && (IsMotorGroup == true && (User.RoleId === 13 || User.RoleId === 12))) && <div className="Additional-details">
                        <div>{NoCostEmiString}</div>
                        <div>
                            {NewMotorInfo.NoCostEMI ? "Yes" : "No"}
                            <Switch
                                checked={!!NewMotorInfo.NoCostEMI}
                                onChange={handleChange}
                                name="NoCostEMI"
                            />
                        </div>
                    </div>}
                    {ShowMotorAddInfo && <div className="Additional-details">
                        <div>Smart Collect</div>
                        <div>
                            {NewMotorInfo.SmartCollect ? "Yes" : "No"}
                            <Switch
                                checked={!!NewMotorInfo.SmartCollect}
                                onChange={handleChange}
                                name="SmartCollect"
                            />
                        </div>
                    </div>}

                    {ShowMotorAddInfo && <SelectDropdown
                        name="AddOn"
                        label="AddOn ?"
                        value={NewMotorInfo.AddOn}
                        options={AddOns}
                        labelKeyInOptions='_all'
                        valueKeyInOptions="_all"
                        handleChange={handleChange}
                        sm={12} md={12} xs={12}
                    />}
                    <button
                        disabled={disabled}
                        onClick={UpdateMotorInfo}
                        className={disabled ? "disabledBtn" : "submitBtn"}
                    >
                        Submit
                    </button>
                </>
            }

            {productId === 2 && show &&
                <>

                    <TextInput
                        name="ReqSumInsured"
                        label="Sum Insured Required ?"
                        maxLength={10}
                        handleChange={handleChange}
                        value={NewHealthInfo.ReqSumInsured}
                        sm={12} md={12} xs={12}
                    />
                    <div className="Additional-details">
                        <div>Pre existing diseases ?</div>
                        <div>
                            {NewHealthInfo.IsPreExistsDiseas ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.IsPreExistsDiseas}
                                onChange={handleChange}
                                name="IsPreExistsDiseas"
                            />
                        </div>
                    </div>
                    <TextInput
                        name="PreExistsDiseas"
                        label="Diseases"
                        show={NewHealthInfo.IsPreExistsDiseas}
                        maxLength={15}
                        handleChange={handleChange}
                        value={NewHealthInfo.PreExistsDiseas}
                        sm={12} md={12} xs={12}
                    />
                    <div className="Additional-details">
                        <div>Any existing health policy ?</div>
                        <div>
                            {NewHealthInfo.IsOldPolicy ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.IsOldPolicy}
                                onChange={handleChange}
                                name="IsOldPolicy"
                            />
                        </div>
                    </div>
                    <TextInput
                        name="OldPolicy"
                        label="Details"
                        show={NewHealthInfo.IsOldPolicy}
                        maxLength={10}
                        handleChange={handleChange}
                        value={NewHealthInfo.OldPolicy}
                        sm={12} md={12} xs={12}
                    />
                    <div className="Additional-details">
                        <div>EMI payment benefit Required ?</div>
                        <div>
                            {NewHealthInfo.EMIReq ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.EMIReq}
                                onChange={handleChange}
                                name="EMIReq"
                            />
                        </div>
                    </div>
                    <div className="Additional-details">
                        <div>Any maternity event ?</div>
                        <div>
                            {NewHealthInfo.Maternity ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.Maternity}
                                onChange={handleChange}
                                name="Maternity"
                            />
                        </div>
                    </div>
                    <div className="Additional-details">
                        <div>No Cost EMI{'(>=15K)'}</div>
                        <div>
                            {NewHealthInfo.NoCostEMI == 1 ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.NoCostEMI == 1}
                                onChange={handleChange}
                                name="NoCostEMI"
                                disabled={IsNoCostEMI}
                            />
                        </div>
                    </div>
                    {/* { IsMonthlyMode && 
                        <> 
                            <div className="Additional-details">
                        <div>MonthlyMode EMI</div>
                        <div>
                            {NewHealthInfo.MonthlyModeEMI ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.MonthlyModeEMI}
                                onChange={handleChange}
                                name="MonthlyModeEMI"
                            />
                        </div>
                    </div>
                    </>
                    }                     */}
                    {/* <div className={IsClaimAssistance}>
                        <div>Claim Assistance</div>
                        <div>
                            {NewHealthInfo.ClaimAssistance ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.ClaimAssistance}
                                onChange={handleChange}
                                name="ClaimAssistance"
                            />
                        </div>
                    </div> */}
                    <div className="Additional-details">
                        <div>Smart Collect</div>
                        <div>
                            {NewHealthInfo.SmartCollect ? "Yes" : "No"}
                            <Switch
                                checked={!!NewHealthInfo.SmartCollect}
                                onChange={handleChange}
                                name="SmartCollect"
                            />
                        </div>
                    </div>


                    <button
                        disabled={disabled}
                        onClick={UpdateHealthInfo}
                        className={disabled ? "disabledBtn" : "submitBtn"}
                    >
                        Submit
                    </button>
                </>
            }

            {productId === 101 && show &&
                <>
                    <Grid container spacing={2}>
                        <Grid item xs={12}>
                            <div className="Additional-details">
                                <div>Loan</div>
                                <div>
                                    {NewHomeInfo.Loan ? "Yes" : "No"}
                                    <Switch
                                        checked={!!NewHomeInfo.Loan}
                                        onChange={handleChange}
                                        name="Loan"
                                    />
                                </div>
                            </div>
                            <div className="Additional-details">
                                <div>Smart Collect</div>
                                <div>
                                    {NewHomeInfo.SmartCollect ? "Yes" : "No"}
                                    <Switch
                                        checked={!!NewHomeInfo.SmartCollect}
                                        onChange={handleChange}
                                        name="SmartCollect"
                                    />
                                </div>
                            </div>
                            <div className="Additional-details">
                                <div>No Cost EMI{'(>=20K)'}</div>
                                <div>
                                    {NewHomeInfo.NoCostEMI ? "Yes" : "No"}
                                    <Switch
                                        checked={!!NewHomeInfo.NoCostEMI}
                                        onChange={handleChange}
                                        name="NoCostEMI"
                                    />
                                </div>
                            </div>
                        </Grid>
                        <SelectDropdown
                            name="HomeType"
                            label="Type"
                            value={NewHomeInfo.HomeType}
                            options={HomeTypes}
                            labelKeyInOptions='_all'
                            valueKeyInOptions="_all"
                            handleChange={handleChange}
                            sm={12} md={12} xs={12}
                        />

                        <SelectDropdown
                            name="PreferredInsurer"
                            label="Preferred Insurer"
                            value={NewHomeInfo.PreferredInsurer}
                            options={PreferredInsurerList}
                            labelKeyInOptions='_all'
                            valueKeyInOptions="_all"
                            handleChange={handleChange}
                            sm={12} md={12} xs={12}
                        />
                        <TextInput
                            name="Other"
                            label="Other"
                            handleChange={handleChange}
                            value={NewHomeInfo.Other}
                            show={IsEnableHomeOther}
                            sm={12} md={12} xs={12}
                        />
                        <TextInput
                            name="SumInsured"
                            label="Sum Insured"
                            maxLength={10}
                            handleChange={handleChange}
                            value={NewHomeInfo.SumInsured}
                            sm={12} md={12} xs={12}
                        />
                        <TextInput
                            name="LPremium"
                            label="L1 Premium"
                            maxLength={10}
                            handleChange={handleChange}
                            value={NewHomeInfo.LPremium}
                            sm={12} md={12} xs={12}
                        />
                        <TextInput
                            name="PolicyTerm"
                            label="Policy Term(Years)"
                            maxLength={2}
                            handleChange={handleChange}
                            value={NewHomeInfo.PolicyTerm}
                            sm={12} md={12} xs={12}
                        />
                    </Grid>
                    <button
                        disabled={disabled}
                        onClick={SetHomeAnalysis}
                        className={disabled ? "disabledBtn" : "submitBtn"}
                    >
                        Submit
                    </button>
                </>
            }
            {productId == 115 && show &&
                <>
                    <div className="Additional-details">
                        <div>IsABSLIApptCreated</div>
                        <div>
                            {NewProductInfo.IsABSLIApptCreated ? "Yes" : "No"}
                            <Switch
                                checked={!!NewProductInfo.IsABSLIApptCreated}
                                onChange={handleChange}
                                name="IsABSLIApptCreated"
                            />
                        </div>
                    </div>
                </>
            }
            {productId == 130 && IsSTUGroup && show &&
                <>
                    <div className="Additional-details">
                        <div>No Cost EMI{'(>=15K)'}</div>
                        <div>
                            {NewProductInfo.NoCostEMI == 1 ? "Yes" : "No"}
                            <Switch
                                checked={!!NewProductInfo.NoCostEMI == 1}
                                onChange={handleChange}
                                name="NoCostEMI"
                            />
                        </div>
                    </div>
                </>
            }

            {productId === 139 && show &&
                <>
                    <div className="Additional-details">
                        <div>{NoCostEmiString}</div>
                        <div>
                            {NewMotorInfo.NoCostEMI ? "Yes" : "No"}
                            <Switch
                                checked={!!NewMotorInfo.NoCostEMI}
                                onChange={handleChange}
                                name="NoCostEMI"
                            />
                        </div>
                    </div>
                    <div className="Additional-details">
                        <div>Smart Collect</div>
                        <div>
                            {NewMotorInfo.SmartCollect ? "Yes" : "No"}
                            <Switch
                                checked={!!NewMotorInfo.SmartCollect}
                                onChange={handleChange}
                                name="SmartCollect"
                            />
                        </div>
                    </div>
                    <button
                        disabled={disabled}
                        onClick={UpdateCommercialInfo}
                        className={disabled ? "disabledBtn" : "submitBtn"}
                    >
                        Submit
                    </button>
                </>
            }

            {productId != 101 && productId != 117 && productId != 2 && productId != 139 && show &&
                <>

                    {productId !== 131 &&
                        <div className="Additional-details">
                            <div>Smart Collect</div>
                            <div>
                                {NewProductInfo.SmartCollect ? "Yes" : "No"}
                                <Switch
                                    checked={!!NewProductInfo.SmartCollect}
                                    onChange={handleChange}
                                    name="SmartCollect"
                                />
                            </div>
                        </div>
                    }
                    {User && User.RoleId == 13 && (ShowSmeCustomerType || IsPotentialCustomerType || ((rootScopeService.getProductId() == 131 && Array.isArray(allLeads) && allLeads.length > 0 && allLeads[0].SubProductTypeId && allLeads[0].SubProductTypeId==13) &&
                     User.IsEnableChat)) &&
                        <div className="Additional-details">
                            <div className="PotentialBuyer">{SmePotentialBuyerLabel}</div>
                            <div className="PotentialBuyerToggle">
                                {(!!NewProductInfo.IsPotentialBuyer || !!ShowSmeCustomerType) ? "Yes" : "No"}
                                <Switch
                                    checked={(!!NewProductInfo.IsPotentialBuyer || !!ShowSmeCustomerType)}
                                    onChange={handleChange}
                                    name="IsPotentialBuyer"
                                    disabled={ShowSmeCustomerType}
                                />
                            </div>
                        </div>
                    }
                     {User && User.RoleId != 13 && (ShowSmeCustomerType || IsPotentialCustomerType || ((rootScopeService.getProductId() == 131 && Array.isArray(allLeads) && allLeads.length > 0 && allLeads[0].SubProductTypeId && allLeads[0].SubProductTypeId==13) &&
                      User.IsEnableChat)) &&
                        <div className="Additional-details">
                            <div className="PotentialBuyer">{SmePotentialBuyerLabel}</div>
                            <div className="PotentialBuyerToggle">
                                {(!!NewProductInfo.IsPotentialBuyer) ? "Yes" : "No"}
                                <Switch
                                    checked={(!!NewProductInfo.IsPotentialBuyer)}
                                    onChange={handleChange}
                                    name="IsPotentialBuyer"
                                />
                            </div>
                        </div>
                    }
                    {/* {productId == 7 && (IsDiabeticSwitchDisable || ShowIsDiabeticToggle) &&
                    <>
                    <div className="Additional-details">
                        <div>IsDiabetic</div>
                        <div>
                            {NewProductInfo.IsDiabetic ? "Yes" : "No"}
                            <Switch
                                checked={!!NewProductInfo.IsDiabetic}
                                onChange={handleChange}
                                name="IsDiabetic"
                                disabled={IsDiabeticSwitchDisable}
                            />
                        </div>
                    </div>
                    </>
                    } */}
                    <button
                        disabled={disabled}
                        onClick={GetConfirmationResponse}
                        className={disabled ? "disabledBtn" : "submitBtn"}
                    >
                        Submit
                    </button>
                </>
            }
        </div>
        {/* {IsConfirmationPopupShow &&
            <ModalPopup open={IsConfirmationPopupShow} className="EMIPendingPopup" handleClose={handleClose}>
                <div className="comboPolicyPopup">
                    <h4>Are you Sure the customer is Diabetic?</h4>
                    <div>
                        <button onClick={() => { UpdateProductInfo() }} >Yes, Proceed</button>
                        <button onClick={() => { setIsConfirmationPopupShow(false) }}>No</button>
                    </div>
                </div>
            </ModalPopup>
        } */}

    </>

}
export default AddInfo;
