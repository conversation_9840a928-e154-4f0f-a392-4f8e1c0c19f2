import React, { useEffect, useState } from "react";
import { CALL_API } from "../../../services";
import rootScopeService from "../../../services/rootScopeService";
import { useMediaQuery, useTheme } from '@mui/material';
import { connect, useSelector } from "react-redux";
import { setOpenRightBarMenu, setRefreshCallBackDetails, setActiveCallBackDetails } from "../../../store/actions/SalesView/SalesView";
import dayjs from "dayjs";
import {  IsLeadContent } from "../../../services/Common";
import User from "../../../services/user.service";

function CallBackDetails(props) {
    const [callback, setcallback] = useState(null);
    const [RefreshCallBackDetails] = useSelector(({ salesview }) => [salesview.RefreshCallBackDetails]);
    const parentLeadId = useSelector(state => state.salesview.parentLeadId) || 0;
    const ShowAssignCriticalComponents = useSelector(state => state.salesview.ShowAssignCriticalComponents);
    let [RefreshLead] = useSelector(({ salesview }) => [salesview.RefreshLead]);
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'), {
        defaultMatches: true
    });
    let IsChat = User.IsChat;
    const [IsChatView, setIsChatView] = useState(true);
    const parentLead = useSelector(state => {
        let { parentLead } = state.salesview;
        return [parentLead]
    });

    const getcallback = () => {
        if (!parentLeadId) return;

        let CustomerId = rootScopeService.getCustomerId();

        const input =
        {
            url: `coremrs/api/MRSCore/GetCustomerCallback/${CustomerId}/${parentLeadId}`,
            method: "GET",
            service: "MatrixCoreAPI",
        }
        CALL_API(input).then((result) => {
            if (result && result.EventDate) {
                setcallback(result.EventDate);
                if(result.IsGoogleInvite){
                    props.setActiveCallBackToRedux(result);
                }
            }
            else {
                setcallback(null);
            }
        });

    }

    const openCallBackCalender = () => {
        props.setOpenRightBarMenuToRedux("SetCallback");
    }

    useEffect(() => {
        if(parentLead && Array.isArray(parentLead) && parentLead[0])
        {
            if(rootScopeService.getProductId() == 2 && User.RoleId == 13 && IsChat == 1 && parentLead[0].LeadAssignedUser != User.UserId){
                setIsChatView(false);
            }
        }
        if (parentLeadId !== 0 || RefreshLead) {
            getcallback();
        }
    }, [parentLeadId, RefreshLead]);

    useEffect(() => {
        if (parentLeadId !== 0 && RefreshCallBackDetails) {
            getcallback();
            props.setRefreshCallBackDetailsToRedux(false);
        }
    }, [RefreshCallBackDetails]);

    // if (!callback) return null;
    if (IsLeadContent() && !isDesktop)
        return null;
    return <>
        <div className="callBackSection">
            <span className="icon-calender"></span>
            {callback ? dayjs(callback).format("DD-MM-YYYY hh:mm a") : "None Set"}
            {ShowAssignCriticalComponents && IsChatView &&  <p href="#" onClick={openCallBackCalender}>Edit</p>}
        </div>
    </>
}


const mapStateToProps = state => {
    return {

    };
};

const mapDispatchToProps = dispatch => {
    return {
        setOpenRightBarMenuToRedux: (value) => dispatch(setOpenRightBarMenu({ OpenRightBarMenu: value })),
        setRefreshCallBackDetailsToRedux: (value) => dispatch(setRefreshCallBackDetails({ RefreshCallBackDetails: value })),
        setActiveCallBackToRedux: (value) => dispatch(setActiveCallBackDetails({ ActiveCallBackDetails: value })),
    };
};
export default connect(mapStateToProps, mapDispatchToProps)(CallBackDetails);
