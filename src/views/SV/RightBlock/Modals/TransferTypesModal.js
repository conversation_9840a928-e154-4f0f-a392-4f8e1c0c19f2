import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SV_CONFIG } from "../../../../appconfig";
import { SelectDropdown } from "../../../../components";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { CALL_API, GetIframeURL } from "../../../../services";
import { GetTLCallingNo, createCallTranserUrl, getCustomersRelationshipManager, GetClaimDetailsInbound, ScheduleCTCConnectCallService, getBookingIdsFromCustomerId, getSalesToClaimCallTransferDetails, getBookingDetailsByLeadIdCRT, getCustomerOpenTicketCount, getAssignedAgentDetailsByProductIdAndCustomerId, getBrandNewCarTLTransferDetails } from "../../../../services/Common";
import rootScopeService from "../../../../services/rootScopeService";
import User from "../../../../services/user.service";
import Common from "../../Main/helpers/Common";
import { localStorageCache } from "../../../../../src/utils/utility";

import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import Tickets from "../../RightBar/Tickets";
import { ScheduleCTCSuccessPopup } from "./ScheduleCTCSuccessPopup/ScheduleCTCSuccessPopup";

export default function TransferTypesModal(props) {

    let TransferTypes = require("../../../../assets/json/TransferTypes").default;
    let [TransferTypelist, setTransferTypelist] = useState([]);
    let [PEDTypelist, setPEDTypelist] = useState([]);
    let [NonPEDTypelist, setNonPEDTypelistset] = useState([]);
    let [RenewalRegionalTypeList, setRenewalRegionalTypeList] = useState([]);
    let [SubProductlist, setSubProductlist] = useState([]);
    let [TermRegionalTypeList, setTermRegionalTypeList] = useState([]);
    let [SavingsRegionalTypeList, setSavingsRegionalTypeList] = useState([]);
    let [RenewalTransferTypesSmeList, setRenewalTransferTypesSmeList] = useState([]);
    let [SMECrossBUTransferQueues, setSMECrossBUTransferQueues] = useState([]);
    const [crossBUTransferTypeList, setCrossBUTransferTypeList] = useState([]);
    const [ regionalTransferLanguageList, setRegionalTransferLanguageList ] = useState([])
    const [underwritersTransferList, setUnderwriterTransferList] = useState([])
    const [claimTypeList, setClaimTypeList] = useState([]);
    const [bookingIdList, setBookingIdList] = useState([]);
    const [serviceTransferList, setServiceTransferList] = useState([]);

    const [SelectedSubProductType, setSelectedSubProductType] = useState("");
    const [ClaimQueue, setClaimQueue] = useState("");
    const [serviceQueue, setServiceQueue] = useState("");
    const [SelectedTransferType, setSelectedTransferType] = useState("");
    const [SelectedRegionalTransferType, setSelectedRegionalTransferType] = useState("");
    const [SelectedRegionalPEDTransferType, setSelectedRegionalPEDTransferType] = useState("");
    const [SelectedRegionalNPEDTransferType, setSelectedRegionalNPEDTransferType] = useState("");
    const [CallTransferUrl, setCallTransferUrl] = useState("");
    const [SelectedTermRegionalTransferType, setSelectedTermRegionalTransferType] = useState("");
    const [SelectedSavingsRegionalTransferType, setSelectedSavingsRegionalTransferType] = useState("");
    const [SelectedSmeRenewalGroup, setSelectedSmeRenewalGroup] = useState("");
    const [SelectedRenewalRegional, setSelectedRenewalRegional] = useState("");
    const [SelectedCarInboundType, setSelectedCarInboundType] = useState("");
    const [selectedCrossBUType, setSelectedCrossBUType] = useState("");
    const [selectedHealthRegionalLanguage, setSelectedHealthRegionalLanguage] = useState("");
    const [selectedUnderwriterType, setSelectedUnderwriterType] = useState("");
    const [selectedClaimType, setSelectedClaimType] = useState("");
    const [selectedBookingId, setSelectedBookingId] = useState("");
    const [selectedServiceTransferType, setSelectedServiceTransferType] = useState("");

    const [IsInterTransfer, setIsInterTransfer] = useState(false);
    const [IsRegional, setIsRegional] = useState(false);
    const [IsRegionalPED, setIsRegionalPED] = useState(false);
    const [IsRegionalNPED, setIsRegionalNPED] = useState(false);
    const [IsRegionalNRIMalyalam, setRegionalNRIMalyalam] = useState(false);
    const [IsRegionalBengali, setIsRegionalBengali] = useState(false);
    const [IsTermRegional, setIsTermRegional] = useState(false);
    const [IsSavingsRegional, setIsSavingsRegional] = useState(false);
    const [IsSmeRenewalLeadTransfer, setIsSmeRenewalLeadTransfer] = useState(false);
    const [IsRenewalRegional, setIsRenewalRegional] = useState(false);
    const [IsSmeOtherBUTransfer, setIsSmeOtherBUTransfer] = useState(false);
    const [ isClaimTransfer, setIsClaimTransfer ] = useState(false);
    const [isCrossBUTransfer, setIsCrossBUTransfer] = useState(false);
    const [isUnderwriterTransfer, setIsUnderWriterTransfer] = useState(false);
    const [ isServiceTransfer, setIsServiceTransfer ] = useState(false);
    const [isBrandNewCarTLTransfer, setIsBrandNewCarTLTransfer] = useState(false);
    const [brandNewCarTLInfo, setBrandNewCarTLInfo] = useState("");

    let [InterTransferList, setInterTransferList] = useState([16]);
    const [isRMTransfer, setIsRMTransfer] = useState(false);
    const [isSmeInterTeamTransfer, setIsSmeInterTeamTransfer] = useState(false);
    const [RMdetails, setRMdetails] = useState(null);
    const [currentPopup, setCurrentPopup] = useState("");
    const [showTicketSection, setShowTicketSection] = useState(false);

    const [ ticketCount, setTicketCount ] = useState(0);

    const [showOverlapDiv, setShowOverlapDiv] = useState(false);

    const [bookingLeadId, setBookingLeadId] = useState();
    const [crossProductLeadId, setCrossProductLeadId] = useState();
    const [claimTransferProduct, setClaimTransferProduct] = useState(0);
    const [bookingsData, setBookingsData] = useState([]);

    const [isScheduleCTCInProgress, setIsScheduleCTCInProgress] = useState(false);
    const [showScheduleCTCSuccessPopup, setShowScheduleCTCSuccessPopup] = useState(false);

    const [claimAssignedAgentDetail, setClaimAssignedAgentDetail] = useState();
    const [serviceAssignedAgentDetail, setServiceAssignedAgentDetail] = useState();
    const [crossBUAssignedAgentDetail, setCrossBUAssignedAgentDetail] = useState();
    const [claimId, setClaimId] = useState();
    
    let [parentLeadId] = useSelector(({ salesview }) => [salesview.parentLeadId]);
    const { enqueueSnackbar } = useSnackbar();
    let VerificationAccessGroups = SV_CONFIG["VerificationProcessGroupIds"][SV_CONFIG["environment"]];
    let allLeads = useSelector(state => state.salesview.allLeads);
    let CarTypes = [
        {
            "Id": "Commercial",
            "Name": "Commercial"
        },
        {
            "Id": "Car",
            "Name": "Car"
        }
    ]

    const GetGroups = () => {
        let groupIds = [];
        let groupList = User.UserGroupList;
        if (Array.isArray(groupList)) {
            for (var i = 0, len = groupList.length; i < len; i++) {
                groupIds.push(groupList[i].GroupId);
            }
        }
        return groupIds;
    }

    const bindTransferVal = () => {
        let usergrp = User.UserGroupList;

        setTransferTypelist(TransferTypes.TransferType.filter(plan => {
            let data = true;
            // if IDs belongs to 18,19,20,21 then please check for not allowed products
            if (Array.isArray(plan.NotAllowedProductId) && plan.NotAllowedProductId.length > 0) {
                if (plan.NotAllowedProductId.indexOf(rootScopeService.getProductId()) > -1) {
                    data = false;
                    return data;
                }
            }
            if (Array.isArray(plan.NotAllowedGroupId) && plan.NotAllowedGroupId.length > 0) {
                let UserGroups = GetGroups();
                const checkUserNotAllowed = plan.NotAllowedGroupId.every(value => !UserGroups.includes(value));

                if (checkUserNotAllowed) {
                    data = false;
                    return data;
                }
            }
            // if plan.ProductId is undefined or empty array => show for all products
            if (Array.isArray(plan.ProductId) && plan.ProductId.length > 0) {
                data = data && (plan.ProductId.indexOf(rootScopeService.getProductId()) > -1);
            }
            if (Array.isArray(plan.GroupId) && plan.GroupId.length > 0) {
                data = data && (Array.isArray(usergrp) ? (usergrp.filter(grp => plan.GroupId.indexOf(grp.GroupId) > -1).length > 0) : false);
            }
            //IsRenewable
            if (plan.IsRenewable) {
                let usergrpList = User.UserGroupList || [];
                let flag = false;
                Array.isArray(usergrpList) && usergrpList.forEach(function (val, key) {
                    if (val && val.GroupName && val.GroupName.toLowerCase().includes("renewals")) { flag = true; }
                });
                data = data && flag;
            }
            // Check id IsRenewalLead
            if (Array.isArray(allLeads)) {
                if (plan.IsRenewalLead) {
                    allLeads.forEach((vdata) => {
                        data = data && vdata.StatusMode === 'P' && parseInt(vdata.LeadSourceId) === 6;
                    });
                } else if (plan.IsRenewalLead === false) {
                    let filteredData = allLeads.filter(vdata => !(vdata.StatusMode === 'P' && parseInt(vdata.LeadSourceId) === 6));
                    data = data && (filteredData.length === allLeads.length); // true if no elements were removed, false otherwise
                }
            }

            // Check if brand new car
            if (Array.isArray(allLeads)) {
                if (plan.IsBrandNewCar) {
                    const hasBrandNewCar = allLeads.some(vdata => vdata.IsBrandNewCar);
                    data = data && hasBrandNewCar;
                }
            }

            // Check any one RenewalLead
            if (plan.AnyRenewal && Array.isArray(allLeads)) {
                let flag = false;
                allLeads.forEach((vdata) => {
                    if (vdata.StatusMode === 'P' && vdata.LeadSourceId == "6")
                        flag = true;
                });
                data = data && flag;
            }

            // Check all leads to be booked and rejected
            if (plan.Allbooked && Array.isArray(allLeads)) {
                let renewalflag = false;
                let bookedflag = true;
                let rejectedflag = true;
                allLeads.forEach((vdata) => {
                    if (vdata.LeadSourceId == "6")
                        renewalflag = true;

                    if (vdata.StatusMode === 'P' && bookedflag == true) {
                        if (vdata.StatusId >= 13)
                            bookedflag = true;
                        else
                            bookedflag = false;
                    }
                    if (vdata.StatusMode === 'N' && rejectedflag == true) {
                        rejectedflag = true;
                    }
                    else {
                        rejectedflag = false;
                    }

                });
                data = data && renewalflag && bookedflag && !rejectedflag;
            }

            return data;
        }));

        setTermRegionalTypeList(TransferTypes.RegionalTransferTerm.filter(plan => {
            let data = true;
            if (Array.isArray(plan.GroupId) && plan.GroupId.length > 0) {
                data = data && (Array.isArray(usergrp) ? (usergrp.filter(grp => plan.GroupId.indexOf(grp.GroupId) > -1).length > 0) : false);
            }
            return data;
        }))

        setPEDTypelist(TransferTypes.RegionalTransferPED);

        setRegionalTransferLanguageList(TransferTypes.RegionalTransferLanguage);

        setNonPEDTypelistset(TransferTypes.RegionalTransferNPED);

        // setTermRegionalTypeList(TransferTypes.RegionalTransferTerm);

        setSavingsRegionalTypeList(TransferTypes.RegionalTransferSavings);

        setRenewalTransferTypesSmeList(TransferTypes.RenewalTransferTypesSme);

        setRenewalRegionalTypeList(TransferTypes.RenewalRegionalType);

        setSMECrossBUTransferQueues(TransferTypes.SMECrossBUTransferQueues);

        setCrossBUTransferTypeList(TransferTypes.CrossBUTransfer.filter((plan) => {
            let isValid = true;
            const productId = rootScopeService.getProductId();
            const userGroups = GetGroups();

            if (Array.isArray(plan.NotAllowedProductId) && plan.NotAllowedProductId.includes(productId)) {
                return false;
            }

            if (Array.isArray(plan.NotAllowedGroupId) && plan.NotAllowedGroupId.some(id => userGroups.includes(id))) {
                return false;
            }

            if (Array.isArray(plan.ProductId) && plan.ProductId.length > 0) {
                isValid = isValid && plan.ProductId.includes(productId);
            }

            if (Array.isArray(plan.GroupId) && plan.GroupId.length > 0) {
                isValid = isValid && Array.isArray(usergrp) && usergrp.some(grp => plan.GroupId.includes(grp.GroupId));
            }

            return isValid;
        }));

        setUnderwriterTransferList(TransferTypes.UnderWritersTransfer);

        setClaimTypeList(TransferTypes.ClaimTransfer);

        setServiceTransferList(TransferTypes.CRTTransfer);
    }

    useEffect(() => {
        getCustomerOpenTicketCount().then((response) => {
            setTicketCount(response.Data.openTicketCount)
        })
    }, [])

    useEffect(() => {
        getSubProductMaster();
    }, [])

    useEffect(() => {
        bindTransferVal();
    }, [allLeads])

    const getInterTranferQueue = async (SelectedSubProductType) => {
        if (SubProductlist && Array.isArray(SubProductlist) && SubProductlist.length > 0) {
            let _list = SubProductlist.filter(x => {
                let data = true;
                data = x.SubProductId === SelectedSubProductType;
                return data;
            });
            return _list[0];
        }
    }

    const getPEDQueue = (SelectedRegionalPEDTransferType) => {
        let _list = PEDTypelist.filter(x => {
            let data = true;
            data = x.Id === SelectedRegionalPEDTransferType;
            return data;
        });
        return _list[0];
    }

    const getHealthRegionalQueue = (languageType) => {
        return regionalTransferLanguageList.find(langObj => {
            return langObj.Id === languageType
        });
    }

    const getSmeRenewalGroups = (SelectedSmeRenewalGroup) => {
        let _list = RenewalTransferTypesSmeList.filter(x => {
            let data = true;
            data = x.Id === SelectedSmeRenewalGroup;
            return data;
        });
        return _list[0];
    }

    const getNPEDQueue = (SelectedRegionalNPEDTransferType) => {
        let _list = NonPEDTypelist.filter(x => {
            let data = true;
            data = x.Id === SelectedRegionalNPEDTransferType;
            return data;
        });
        return _list[0];
    }

    const getTermRegionalQueue = (SelectedTermRegionalTransferType) => {
        let _list = TermRegionalTypeList.filter(x => {
            let data = true;
            data = x.Id === SelectedTermRegionalTransferType;
            return data;
        });
        return _list[0];
    }

    const getSavingsRegionalQueue = (SelectedSavingsRegionalTransferType) => {
        let _list = SavingsRegionalTypeList.filter(x => {
            let data = true;
            data = x.Id === SelectedSavingsRegionalTransferType;
            return data;
        });
        return _list[0];
    }

    const getClaimQueue = (requestData) => {
        GetClaimDetailsInbound(requestData).then((result) => {
            if (result) {
                setClaimQueue(result);
            };
        }).catch((e) => {
            console.log(e);
        });
    }

    const getRenewalRegionalQueue = (SelectedRenewalRegional) => {
        let _list = RenewalRegionalTypeList.filter(x => {
            let data = true;
            data = x.Id === SelectedRenewalRegional;
            return data;
        });
        return _list[0];
    }

    const getSMECrossBUTransferQueues = (SelectedTransferType) => {
        let _list = SMECrossBUTransferQueues.filter(x => {
            let data = true;
            data = x.Id === SelectedTransferType;
            return data;
        });
        return _list[0];
    }

    const checkValidations = () => {
        if (SelectedTransferType === '') {
            enqueueSnackbar("Please fill the Transfer Type", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (IsRegional && selectedHealthRegionalLanguage === "") {
            enqueueSnackbar("Please select the Regional language", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (IsSavingsRegional && SelectedSavingsRegionalTransferType === '') {
            enqueueSnackbar("Please select the  Regional name", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (IsTermRegional && SelectedTermRegionalTransferType === '') {
            enqueueSnackbar("Please select the  Regional name", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isCrossBUTransfer && selectedCrossBUType === '') {
            enqueueSnackbar("Please select BU Product", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isUnderwriterTransfer && selectedUnderwriterType === '') {
            enqueueSnackbar("Please select Underwriter Type", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isClaimTransfer && selectedClaimType === '') {
            enqueueSnackbar("Please select Claim Type", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isClaimTransfer && selectedClaimType !== "" && selectedBookingId === "") {
            enqueueSnackbar("Please select BookingId", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isServiceTransfer && selectedServiceTransferType === '') {
            enqueueSnackbar("Please select Service Transfer Type", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        if (isServiceTransfer && selectedServiceTransferType !== '' && selectedBookingId === "") {
            enqueueSnackbar("Please select BookingId", { variant: 'error', autoHideDuration: 3000, });
            return false;
        }

        return true;
    }

    const OpenTransferModel = async () => {

        if (!checkValidations()) {
            return;
        }

        setIsRMTransfer(false);
        setIsSmeInterTeamTransfer(false);
        let campaign = '';
        const connectCallSF = Common.getConnectCallSFFromLS();
        const countryCode = connectCallSF?.CountryCode || "";
        
        let list = TransferTypes.TransferType.filter(plan => {
            let data = true;
            data = plan.Id === SelectedTransferType;
            return data;
        });

        if (selectedCrossBUType) {
            list = TransferTypes.CrossBUTransfer.filter(plan => {
                let data = true;
                data = plan.Id === selectedCrossBUType;
                return data;
            })
        }

        if (selectedUnderwriterType) {
            list = TransferTypes.UnderWritersTransfer.filter(plan => {
                let data = true;
                data = plan.Id === selectedUnderwriterType;
                return data;
            })
        }

        if (selectedClaimType) {
            list = TransferTypes.ClaimTransfer.filter(plan => {
                let data = true;
                data = plan.Id === selectedClaimType;
                return data;
            })
        }

        if (selectedServiceTransferType) {
            list = TransferTypes.CRTTransfer.filter(plan => {
                let data = true;
                data = plan.Id === selectedServiceTransferType;
                return data;
            })
        }

        if (InterTransferList.indexOf(SelectedTransferType) > -1) {
            if (SelectedSubProductType == "") {
                enqueueSnackbar("You have to fill the Sub Product first", { variant: 'error', autoHideDuration: 3000, });
                return;
            }

            let InsuranceType = '';
            let subProductTypeID = '';
            allLeads.forEach((vdata, key) => {
                if (vdata.StatusMode === 'P' && vdata.LeadID === parentLeadId) {
                    InsuranceType = vdata.InsuranceType;
                    subProductTypeID = vdata.SubProductTypeId;
                }
            });

            let queueName = await getInterTranferQueue(SelectedSubProductType);
            if (queueName != "" && queueName.QueueName != "") {
                campaign = queueName.QueueName;
            }

            if (subProductTypeID && (subProductTypeID > 0)) {
                // Do nothing
            }
            else {
                enqueueSnackbar("Sub-Product cannot be blank for the current Lead during call transfer", { variant: 'error', autoHideDuration: 3000, });
                return;
            }

            if (rootScopeService.getProductId() === 131) {
                if (window.confirm('Are you sure you want to transfer the call to the selected SubProduct?')) {
                    setIsSmeInterTeamTransfer(true);
                }
                else {
                    return;
                }
            }
        }
        else if (IsRegional) {
            let queueName = getHealthRegionalQueue(selectedHealthRegionalLanguage);
            campaign = queueName.Queue;
        }
        else if (IsRegionalPED) {
            let queueName = getPEDQueue(SelectedRegionalPEDTransferType);
            campaign = queueName.Queue;
        }
        else if (IsRegionalNPED) {
            let queueName = getNPEDQueue(SelectedRegionalNPEDTransferType);
            campaign = queueName.Queue;
        }
        else if (IsTermRegional) {
            let queueName = getTermRegionalQueue(SelectedTermRegionalTransferType);
            campaign = queueName.Queue;
        }
        else if (IsSavingsRegional) {
            let queueName = getSavingsRegionalQueue(SelectedSavingsRegionalTransferType);
            campaign = queueName.Queue;
        }
        else if (isClaimTransfer || list[0].Transfertype === 'sales_to_claims') {
            campaign = (ClaimQueue !== undefined && ClaimQueue !== '') ? ClaimQueue : list[0].Queue;
        }
        else if (isServiceTransfer || list[0].Transfertype === 'sales_to_service') {
            campaign = (serviceQueue !== undefined && serviceQueue !== '') ? serviceQueue : list[0].Queue;
        }
        else if (IsSmeRenewalLeadTransfer) {
            let queueName = getSmeRenewalGroups(SelectedSmeRenewalGroup);
            campaign = queueName.Queue;
        }
        else if (IsRenewalRegional) {
            let queueName = getRenewalRegionalQueue(SelectedRenewalRegional);
            campaign = queueName.Queue;
        }
        else if (list && list.length > 0 && list[0].Transfertype === 'regional_transfer' && IsRegionalNRIMalyalam) {
            let Regionallist = TransferTypes.RegionalTransfer.filter(plan => {
                let data = true;
                data = plan.Id === SelectedRegionalTransferType;
                return data;
            });
            campaign = (Regionallist && Regionallist.length > 0) ? Regionallist[0].Queue : '';
        }
        else if (list && list.length > 0 && list[0].Transfertype === 'regional_transfer' && IsRegionalBengali) {
            let Regionallist = TransferTypes.RegionalTransfer.filter(plan => {
                let data = true;
                data = plan.Id === SelectedRegionalTransferType;
                return data;
            });
            campaign = (Regionallist && Regionallist.length > 0) ? Regionallist[0].Queue : '';
        }
        else if (IsSmeOtherBUTransfer) {
            campaign = list[0].Queue;

            if (SelectedTransferType && SelectedTransferType === 7 && [null, undefined, '', 0].indexOf(SelectedCarInboundType) !== -1) {
                enqueueSnackbar("Please select the Car Type ", { variant: 'error', autoHideDuration: 3000, });
                return;
            }
            else {
                if ((SelectedTransferType && SelectedTransferType === 7 && SelectedCarInboundType === "Car") ||
                    (SelectedTransferType && SelectedTransferType === 8)) {
                    let smeCrossBUTransferQueues = getSMECrossBUTransferQueues(SelectedTransferType);
                    campaign = smeCrossBUTransferQueues ? smeCrossBUTransferQueues.Queue : campaign;
                }
            }
        }
        else {
            campaign = list[0].Queue;
        }

        if (campaign === ""  || campaign === null || campaign === undefined) {
            enqueueSnackbar("Some Error Occured", { variant: 'error', autoHideDuration: 3000, });
            return
        };

        let product = '';
        let productId = 0;
        let UserGroupId = 0;

        let Transfertype = list[0].Transfertype;
        let action = list[0].Action;
        let isCreateLead = list[0].isCreateLead ? 'yes' : 'no';

        let highlightAdvisor = "";
        let crossProduct = false;
        let crossBULeadId = "";

        if (isClaimTransfer) {
            highlightAdvisor = claimAssignedAgentDetail;
        }

        if (isServiceTransfer) {
            highlightAdvisor = serviceAssignedAgentDetail;
        }

        if (isCrossBUTransfer) {
            highlightAdvisor = crossBUAssignedAgentDetail;
            crossProduct = true
            crossBULeadId = crossProductLeadId;
        }

        const customerId = getCustomerIdForTransfer(parentLeadId);

        if (list && list[0].Action == "blindtransfer") {
            //Blind transfer
            let RMEmployeeID = null;
            switch (campaign) {
                case "claim_Transfer":
                    if (rootScopeService.getProductId() == 117 || rootScopeService.getProductId() == 114) {
                        product = 'NewCar';
                        productId = 117;
                    }
                    else if ([106, 118, 130, 2].indexOf(rootScopeService.getProductId()) != -1) {
                        product = 'health';
                        productId = 2;
                    }
                    else if (rootScopeService.getProductId() == 3) {
                        product = 'Travel'
                        productId = 3;
                    }
                    else if (isCreateLead) {
                        product = rootScopeService.getProductId();
                        productId = rootScopeService.getProductId();
                    }

                    break;
                case "fostoivr":
                    product = 'CustomerAssistance';
                    productId = 219;
                    let agent = User.EmployeeId
                    let response = await transferCallToIVR(parentLeadId, Transfertype, agent, 'fostoivr', 'blindtransfer');
                    if (Object.keys(response).length > 0) {
                        if (response.status == 200) {
                            enqueueSnackbar(response.message, { variant: 'success', autoHideDuration: 3000, });
                        } else {
                            enqueueSnackbar(response.message, { variant: 'error', autoHideDuration: 3000, });
                        }
                    }
                    break;
                default: break;
            }
            if (campaign == "fostoivr") return;

            if (Transfertype === "claimBlindTransfer") {
                if (rootScopeService.getProductId() == 117 || rootScopeService.getProductId() == 114) {
                    product = 'NewCar';
                    productId = 117;
                }
                else if ([106, 118, 130, 2].indexOf(rootScopeService.getProductId()) != -1) {
                    product = 'health';
                    productId = 2;
                }
                else if (rootScopeService.getProductId() == 3) {
                    product = 'Travel'
                    productId = 3;
                }
                else if (isCreateLead) {
                    product = rootScopeService.getProductId();
                    productId = rootScopeService.getProductId();
                }

            }
            // let URL = SV_CONFIG["ClaimTransfer"][SV_CONFIG["environment"]]
            // if (isCreateLead === 'yes') {
            //     URL += User.EmployeeId + "&transfer_agents=&campaign=" + campaign + "&bookingid=" + parentLeadId + "&action=" + action + "&transfer_type=" + Transfertype + "&product=" + rootScopeService.getProductId() + "&isCreateLead=" + isCreateLead;
            // }
            // else {
            //     URL += User.EmployeeId + "&transfer_agents=&campaign=" + campaign + "&bookingid=" + parentLeadId + "&action=" + action + "&transfer_type=" + Transfertype + "&product=" + product + "&productid=" + productId;
            // }

            // CALL_API({ service: "custom", url: URL }).then(resultData => {
            //     if (resultData && resultData.status == 200) {
            //         enqueueSnackbar("Call initiated successfully", { variant: 'success', autoHideDuration: 3000, });
            //     }
            //     else {
            //         try {
            //             enqueueSnackbar(resultData.message, { variant: 'error', autoHideDuration: 3000, });
            //         }
            //         catch { }
            //         enqueueSnackbar("Something Went Wrong", { variant: 'error', autoHideDuration: 3000, });
            //     }
            // })
            if (rootScopeService.getProductId() == 131) {
                OpenCallTranserModal(campaign, Transfertype, productId, { action }, isCreateLead, null, parentLeadId, null, null, null, customerId, crossProduct, crossBULeadId, countryCode);
            } else {
                OpenCallTranserModal(campaign, Transfertype, productId, { action }, isCreateLead, null, null, null, highlightAdvisor, null, customerId, crossProduct, crossBULeadId, countryCode);
            }
        }
        else {
            let thirdpartynumber;
            let RMEmployeeID = null;
            switch (campaign) {
                case "TLCallTransfer":
                    //get tl number
                    try {
                        let res = await GetTLCallingNo(User.UserId);
                        if (res != null && res > 0) {
                            thirdpartynumber = res;
                        }
                        else {
                            enqueueSnackbar("TL Calling No not found", { variant: 'error', autoHideDuration: 3000 });
                            props.handleClose();
                            return;
                        }
                    }
                    catch (err) {
                        console.log("something went wrong- GetTLCallingNo")
                    }
                    break;
                case "claimtransfer":
                    if ([2, 106, 118, 130].includes(rootScopeService.getProductId())) {
                        productId = 2;
                    }
                    break;
                case "mmsupervisor":
                    productId = 106;
                    break;
                case "health":
                    let usergrp = User.UserGroupList;
                    let isVerificationAccessGroups = false;
                    if (Array.isArray(VerificationAccessGroups) && VerificationAccessGroups.length > 0) {
                        isVerificationAccessGroups = usergrp.filter(grp => VerificationAccessGroups.indexOf(grp.GroupId) > -1).length > 0;
                    }
                    if (!isVerificationAccessGroups && [106, 118, 130, 2].includes(rootScopeService.getProductId()) && Transfertype != "renewaltofreshreferrals") {
                        setCurrentPopup("SelectInsurerForCallTranfer");
                        props.handleClose();
                        return;
                    }

                    break;
                case 'Sales_to_RM':
                    setIsRMTransfer(true);
                    try {
                        let customerId = getCustomerIdForTransfer(parentLeadId);
                        if (!customerId) {
                            enqueueSnackbar('Unable to transfer call.', { autoHideDuration: 3000, variant: 'error' });
                            return;
                        }

                        let customersRMDetails = await getCustomersRelationshipManager(customerId);
                        if (!(customersRMDetails && customersRMDetails.RMEmployeeID)) {
                            enqueueSnackbar('No Relationship Manager found', { autoHideDuration: 3000, variant: 'error' });
                            return;
                        }
                        else {
                            setRMdetails(customersRMDetails)
                            RMEmployeeID = customersRMDetails.RMEmployeeID;
                        }
                    } catch {
                        enqueueSnackbar('Unable to fetch Relationship Manager', { autoHideDuration: 3000, variant: 'error' });
                        return;
                    }
                    break;
                case "engageinsurerrm":
                    UserGroupId = User.GroupId;
                    break;
                default:
                    break;
            }

            if (isServiceTransfer) {
                productId = list[0].TransferProduct
                const leadId = bookingLeadId || parentLeadId;
                OpenCallTranserModal(campaign, Transfertype, productId, { RMEmployeeID, action }, isCreateLead, thirdpartynumber, leadId, UserGroupId, highlightAdvisor, null, customerId, crossProduct, crossBULeadId, countryCode);

                return;
            }

            if (isClaimTransfer) {
                productId = list[0].TransferProduct
                const title = "Claim Agent Transfer"
                const leadId = claimId || bookingLeadId || parentLeadId;
                OpenCallTranserModal(campaign, Transfertype, productId, { action }, isCreateLead, null, leadId, null, highlightAdvisor, title, customerId, crossProduct, crossBULeadId, countryCode);

                return;
            }

            if (isBrandNewCarTLTransfer) {
                const response = await getBrandNewCarTLTransferDetails(parentLeadId);
                if (response.Success) {
                    thirdpartynumber = response.MobileNo;

                    setBrandNewCarTLInfo(`${response.UserName}-${response.EmployeeId}`);
                }
            }

            OpenCallTranserModal(campaign, Transfertype, productId, { RMEmployeeID, action }, isCreateLead, thirdpartynumber, parentLeadId, UserGroupId, highlightAdvisor, null, customerId, crossProduct, crossBULeadId, countryCode);
        }

        // props.handleClose();
    }

    const handleChange = (e) => {

        setShowOverlapDiv(false);

        switch (e.target.name) {

            case "TransferName":
                setSelectedTransferType(e.target.value);
                //fo inter transfer
                setIsInterTransfer(false);
                setIsRegional(false);
                setIsRegionalPED(false);
                setIsRegionalNPED(false);
                setIsTermRegional(false);
                setIsSavingsRegional(false);
                setIsSmeRenewalLeadTransfer(false);
                setIsRenewalRegional(false);
                setIsSmeOtherBUTransfer(false);
                setIsClaimTransfer(false);
                setSelectedCrossBUType("");
                setIsCrossBUTransfer(false);
                setSelectedRenewalRegional("")
                setSelectedRegionalTransferType("");
                setSelectedSavingsRegionalTransferType("")
                setIsUnderWriterTransfer(false);
                setSelectedUnderwriterType("");
                setSelectedHealthRegionalLanguage("");
                setIsServiceTransfer(false);
                setSelectedServiceTransferType("")
                setSelectedClaimType("")
                setSelectedBookingId("");
                setClaimQueue("");
                setServiceQueue("");
                setIsBrandNewCarTLTransfer(false);

                if (InterTransferList.indexOf(e.target.value) > -1) { setIsInterTransfer(true); }
                if (e.target.value === 23) { setIsRegional(true); }  /// 23 is id for regional transfers of Health Fresh
                if (e.target.value === 25) { setIsTermRegional(true); }// 25 is id for regional transfers of Term
                if (e.target.value === 26) { setIsSavingsRegional(true); }// 26 is id for regional transfers of Savings
                if (e.target.value === 32) { setIsRenewalRegional(true); }// 32 is id for regional transfers of renewal
                if (e.target.value === 2 || e.target.value === 15 || e.target.value === 34) {    
                    // Claim Transfer   
                    getBookingIdsFromCustomerId({
                        CustomerId: getCustomerIdForTransfer(parentLeadId),
                        ProductId: 0
                    }).then((bookings) => {
                        setBookingsData(bookings.Bookings);

                        const uniqueProductIds = [...new Set(bookings.Bookings.map(product => product.ProductId))];

                        const claimList = TransferTypes.ClaimTransfer.filter((type) => uniqueProductIds.includes(type.TransferProduct))

                        if(claimList.length === 0) {
                            let requestData = {
                                "ProductID": rootScopeService.getProductId(),
                                "LeadID": rootScopeService.getLeadId(),
                                "TransferAgentEmpId": User.EmployeeId
                            }
                            getClaimQueue(requestData);
                            setIsClaimTransfer(false); // actually claim transfer is true but this is for managing state
                        } else {
                            setIsClaimTransfer(true);
                            setClaimTypeList(claimList);
                        }
                    })
                }

                if (e.target.value === 40) { setIsCrossBUTransfer(true); }

                if (e.target.value === 41) { setIsUnderWriterTransfer(true); }

                if (e.target.value === 42) {
                    // Service Transfer
                    getBookingIdsFromCustomerId({
                        CustomerId: getCustomerIdForTransfer(parentLeadId),
                        ProductId: 0
                    }).then((bookings) => {
                        setBookingsData(bookings.Bookings);

                        const uniqueProductIds = [...new Set(bookings.Bookings.map(product => product.ProductId))];

                        const CRTList = TransferTypes.CRTTransfer.filter((type) => uniqueProductIds.includes(type.TransferProduct))

                        if(CRTList.length === 0) {
                            setServiceQueue("unregistered")
                            setIsServiceTransfer(false); // actually service transfer is true but this is for managing state
                        } else {
                            setIsServiceTransfer(true);
                            setServiceTransferList(CRTList);
                        }
                    })
                }

                if (e.target.value === 44) {
                    setIsBrandNewCarTLTransfer(true);
                }

                if ([2, 15, 34].indexOf(e.target.value) > -1) {
                    // let requestData = {
                    //     "ProductID": rootScopeService.getProductId(),
                    //     "LeadID": rootScopeService.getLeadId(),
                    //     "TransferAgentEmpId": User.EmployeeId
                    // }
                    // getClaimQueue(requestData);
                }//fetch claim queue from claims API
                if (e.target.value === 30) { setIsSmeRenewalLeadTransfer(true); }// 30 is id for SmeRenewalLeadTransfer

                if ((rootScopeService.getProductId() === 131) && ([7, 8].indexOf(e.target.value) !== -1)) {
                    setIsSmeOtherBUTransfer(true);
                }

                break;
            case "SubProductName":
                setSelectedSubProductType(e.target.value);
                break;
            // case "RegionalName":
            //     setSelectedRegionalTransferType(e.target.value);
            //     setIsRegionalPED(false);
            //     setIsRegionalNPED(false);
            //     setRegionalNRIMalyalam(false);
            //     setIsRegionalBengali(false);
            //     if (e.target.value === 1) { setIsRegionalPED(true); }
            //     if (e.target.value === 2) { setIsRegionalNPED(true); }
            //     if (e.target.value === 3) { setRegionalNRIMalyalam(true); }
            //     if (e.target.value === 4) { setIsRegionalBengali(true); }
            //     break;
            case "RegionalPEDName":
                setSelectedRegionalPEDTransferType(e.target.value);
                break;
            case "RegionalNPEDName":
                setSelectedRegionalNPEDTransferType(e.target.value);
                break;
            case "TermRegionalName":
                setSelectedTermRegionalTransferType(e.target.value);
                break;
            case "SavingsRegionalName":
                setSelectedSavingsRegionalTransferType(e.target.value);
                break;
            case "SmeRenewalGroups":
                setSelectedSmeRenewalGroup(e.target.value);
                break;
            case "RenewalRegional":
                setSelectedRenewalRegional(e.target.value);
                break;
            case "CarInboundType":
                setSelectedCarInboundType(e.target.value);
                break;
            case "CrossBUTransfer":
                setSelectedCrossBUType(e.target.value);

                const type = crossBUTransferTypeList.find(type => type.Id === e.target.value);
                const prod = type.TransferProduct;

                getAssignedAgentDetailsByProductIdAndCustomerId({
                    customerId: getCustomerIdForTransfer(parentLeadId),
                    productId: prod
                }).then((data) => {
                    setCrossBUAssignedAgentDetail(data.EmployeeId);
                    setCrossProductLeadId(data.LeadId);
                })
                break;
            case "RegionalLanguage":
                setSelectedHealthRegionalLanguage(e.target.value);
                break;
            case "UnderwritersTransfer":
                setSelectedUnderwriterType(e.target.value);
                break;
            case "ClaimType":
                setSelectedBookingId("")
                setSelectedClaimType(e.target.value);
                setClaimQueue("");
                setServiceQueue("");

                const selectedType = claimTypeList.find(type => type.Id === e.target.value);
                const productId = selectedType.TransferProduct;

                setClaimTransferProduct(productId);

                setBookingIdList(bookingsData.filter(booking => booking.ProductId === productId).map(booking => {
                    return { 
                        value: `${booking.LeadId} - ${booking.SupplierShortName}`,
                        productId: booking.ProductId
                    }
                }));
                break;
            case "BookingId":
                setSelectedBookingId(e.target.value);

                const selectedBooking = e.target.value;
                const leadId = selectedBooking.split('-')[0].trim();
                setBookingLeadId(leadId);
                setClaimQueue("");
                setServiceQueue("");

                if (isClaimTransfer) {
                    getSalesToClaimCallTransferDetails({
                        LeadId: leadId,
                        ProductId: claimTransferProduct,
                        Source: "matrix",
                        Action: "getClaimDetails"
                    }).then((data) => {
                        setClaimAssignedAgentDetail(data.Data.EmployeeId);
                        setClaimId(data.Data.ClaimId);
                        setClaimQueue(data.Data.QueueName);
                    })
                } else if (isServiceTransfer) {
                    getBookingDetailsByLeadIdCRT({
                        LeadId: leadId,
                        CallType: "IB",
                        Source: "matrix"
                    }).then((data) => {
                        setServiceAssignedAgentDetail(data.Data.AssignedEmployeeID);
                        setServiceQueue(data.Data.QueueName);
                    })
                }
                
                break;
            case "CRTTransfer":
                setSelectedBookingId("")
                setSelectedServiceTransferType(e.target.value);

                const selectType = serviceTransferList.find(type => type.Id === e.target.value);
                const prodId = selectType.TransferProduct;

                setBookingIdList(bookingsData.filter(booking => booking.ProductId === prodId).map(booking => {
                    return { 
                        value: `${booking.LeadId} - ${booking.SupplierShortName}`,
                        productId: booking.ProductId
                    }
                }));
                break;
            default:
                break;
        }
    }

    const OpenCallTranserModal = function (Campaign, transfer_type, productId, data = {}, isCreateLead, thirdpartynumber = null, parentLeadId = null, UserGroupId, highlightAdvisor="", title="", customerId = "", crossProduct=false, crossBULeadId = null, countryCode = "") {
        // let url = "";
        // let RMEmployeeID, action;
        // try {
        //     ({ RMEmployeeID, action } = data);
        // } catch { }


        // url = "u=" + User.UserId + "&agent=" + User.EmployeeId;
        // url = url + "&transfer_agents=&campaign=" + Campaign + "&bookingid=" + parentLeadId + "&transfer_type=" + transfer_type;
        // if (thirdpartynumber) url += "&thirdpartynumber=" + thirdpartynumber;
        // if (productId) url += "&productid=" + productId;
        // if (isCreateLead === 'yes') {
        //     url = url + "&iscreatelead=" + isCreateLead;
        // }

        // if (RMEmployeeID) {
        //     url += "&assignedagent=" + RMEmployeeID;

        // }
        // if (action) {
        //     url += "&action=" + action
        // }

        let subProductId = 0;
        let initialLeadSubProductId = 0;
        let isSmeRenewal = false;
        if (transfer_type === "SmeInterTeamTransfer") {
            subProductId = SelectedSubProductType;

            if (Array.isArray(allLeads) && allLeads.length > 0) {
                allLeads.forEach((e, index) => {
                    if (e.LeadID === parentLeadId) {
                        initialLeadSubProductId = e.SubProductTypeId;
                        isSmeRenewal = (e.LeadSource === "Renewal") && e.Utm_source && (e.Utm_source === "Core Renewal");
                    }
                })
            }
        }

        let url = createCallTranserUrl(Campaign, transfer_type, productId, data, isCreateLead, thirdpartynumber, parentLeadId, UserGroupId, subProductId, initialLeadSubProductId, isSmeRenewal, highlightAdvisor, title, customerId, crossProduct, crossBULeadId, countryCode)

        console.log("call transfer url is", url)
        setCallTransferUrl(url);

        setCurrentPopup("CallTransfer");
    
        setShowOverlapDiv(true);

    }

    const getSubProductMaster = () => {
        if (rootScopeService.getProductId() == 131) {
            let TransferTypeSubProductlist = localStorageCache.readFromCache('TransferTypeSubProductlist');
            if (TransferTypeSubProductlist && Array.isArray(TransferTypeSubProductlist) && TransferTypeSubProductlist.length > 0) {
                setSubProductlist(TransferTypeSubProductlist);
            }
            else {
                const input = {
                    url: 'api/SME/GetSMETransferSubProducts',
                    method: "GET",
                    service: "MatrixCoreAPI",
                    timeout: 2000
                }
                return CALL_API(input).then((response) => {
                    if (!!response && Array.isArray(response) && response.length > 0) {
                        localStorageCache.writeToCache('TransferTypeSubProductlist', response, 24 * 60 * 60 * 1000);//24 hours
                        setSubProductlist(response);
                    }
                });
            }
        }
    }

    const transferCallToIVR = async (parentLeadId, Transfertype, agent, campaign, action) => {
        if (rootScopeService.getProductId() == 219) {

            let reqData = {
                transfer_type: Transfertype,
                campaign: campaign,
                bookingid: parentLeadId,
                action: action,
                agent: agent
            }
            const input = {
                url: 'api/LeadDetails/CallTransferSalesIVR',
                method: "POST",
                service: "MatrixCoreAPI",
                requestData: reqData,
                timeout: 2000
            }
            return CALL_API(input).then((response) => {
                if (response) {
                    return response;
                }
            });

        }
    }

    const handleScheduleCTC = (comment) => {
            const connectCallSF = Common.getConnectCallSFFromLS();
            const ProductId = connectCallSF && connectCallSF.lead && connectCallSF.lead.ProductId;
            // const LeadID = connectCallSF && connectCallSF.LeadID; // use leadid on which rm is assigned
            const LeadID = RMdetails && RMdetails.LeadId;
            if (!(LeadID || ProductId)) {
                enqueueSnackbar('Unable to set Followup- Data is Missing!', { variant: 'error', autoHideDuration: 3000 });
                return;
            }
            const CallNow = true;
    
            const data = { ProductId, LeadID, comment, CallNow };
            const followupTS = new Date().getTime() / 1000;
            setIsScheduleCTCInProgress(true);
            ScheduleCTCConnectCallService(data, parseInt(followupTS)).then((res) => {
                if (SV_CONFIG && SV_CONFIG.UseSchCTCConnectCallApi ? res && res.SchCTCConnectCallResult !== false : res) {
                    setShowScheduleCTCSuccessPopup(true);
                }
                else {
                    enqueueSnackbar('Unable to set Followup, Please try again.', { variant: 'error', autoHideDuration: 3000 });
                }
    
            }).catch((e) => {
                console.log("error in handleScheduleCTC", e)
                enqueueSnackbar('Some Error Occured', { variant: 'error', autoHideDuration: 3000 });
            }).finally(() => {
                setIsScheduleCTCInProgress(false);
            })
        }

    // if (!props.open) return null
    return (
        <>
            <ModalPopup
                open={props.open}
                title={showTicketSection ? null : "Call Transfer"}
                handleClose={props.handleClose}
                className="transfer-types-modal"
                showCloseButton={!showTicketSection}
                disableBackdropClick={true}
            >
                {!showTicketSection && (
                    <button className="open-tickets-btn" onClick={() => setShowTicketSection(true)}>
                        Open tickets<span className="ticket-count">{ticketCount}</span>
                    </button>
                )}

                {showTicketSection && (
                    <div className="tickets-section">
                        <div className="tickets-header">
                            <div className="header-left">
                                <ArrowBackIcon
                                    className="back-icon"
                                    onClick={() => setShowTicketSection(false)}
                                />
                            </div>
                            <CloseIcon
                                className="close-icon"
                                onClick={props.handleClose}
                            />
                        </div>
                        <Tickets open={true} />
                    </div>
                )}  

                {!showTicketSection && (
                    <>
                        <div className="form-container">
                            <div className="form-row">
                                <div className="form-group">
                                    <label className="form-label">Select Call Transfer Category</label>
                                    <SelectDropdown
                                        name="TransferName"
                                        label="TrasferName"
                                        placeholder="TransferName"
                                        value={SelectedTransferType}
                                        options={TransferTypelist}
                                        labelKeyInOptions='TransferName'
                                        valueKeyInOptions="Id"
                                        handleChange={handleChange}
                                        sm={12} md={12} xs={12}
                                        InputLabelProps={{ shrink: false }}
                                        shrink={false}
                                    />
                                </div>
                                <div className="form-group">
                                    {isClaimTransfer && 
                                        <>
                                            <label className="form-label">Claim Type</label>
                                            <SelectDropdown
                                                name="ClaimType"
                                                label="Claim Type"
                                                value={selectedClaimType}
                                                options={claimTypeList}
                                                labelKeyInOptions='TransferName'
                                                valueKeyInOptions="Id"
                                                handleChange={handleChange}
                                                sm={12} md={12} xs={12}
                                            />
                                        </>
                                    }
                                    {IsInterTransfer && 
                                    <>  
                                        <label className="form-label">SubProduct Name</label>
                                        <SelectDropdown
                                            name="SubProductName"
                                            label="SubProductName"
                                            value={SelectedSubProductType}
                                            options={SubProductlist}
                                            labelKeyInOptions='SubProductName'
                                            valueKeyInOptions="SubProductId"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }
                                    {IsRenewalRegional &&
                                    <>
                                        <label className="form-label">Regional Name</label>
                                        <SelectDropdown
                                            name="RenewalRegional"
                                            label="Regional Name"
                                            value={SelectedRenewalRegional}
                                            options={RenewalRegionalTypeList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }

                                    {IsRegional && // health
                                        <>
                                            <label className="form-label">Regional Language</label>
                                            <SelectDropdown
                                                name="RegionalLanguage"
                                                label="Regional Language"
                                                value={selectedHealthRegionalLanguage}
                                                options={regionalTransferLanguageList}
                                                labelKeyInOptions='TransferName'
                                                valueKeyInOptions="Id"
                                                handleChange={handleChange}
                                                sm={12} md={12} xs={12}
                                            />
                                        </>
                                    }
                                    
                                    {IsTermRegional && 
                                    <>
                                        <label className="form-label">Regional Name</label>
                                        <SelectDropdown
                                            name="TermRegionalName"
                                            label="Regional Name"
                                            value={SelectedTermRegionalTransferType}
                                            options={TermRegionalTypeList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    
                                    }
                                    {IsSavingsRegional && 
                                    <>
                                        <label className="form-label">Regional Name</label>
                                        <SelectDropdown
                                            name="SavingsRegionalName"
                                            label="Regional Name"
                                            value={SelectedSavingsRegionalTransferType}
                                            options={SavingsRegionalTypeList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }
                                    {IsSmeRenewalLeadTransfer && 
                                    <>
                                        <label className="form-label">Renewal Groups</label>
                                        <SelectDropdown
                                            name="SmeRenewalGroups"
                                            label="Renewal Groups"
                                            value={SelectedSmeRenewalGroup}
                                            options={RenewalTransferTypesSmeList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }
                                    {(IsSmeOtherBUTransfer && SelectedTransferType && SelectedTransferType === "7") && 
                                    <>
                                        <label className="form-label">Renewal Groups</label>
                                        <SelectDropdown
                                            name="CarInboundType"
                                            label="Car Type"
                                            value={SelectedCarInboundType}
                                            options={CarTypes}
                                            labelKeyInOptions='Name'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }
                                    {isCrossBUTransfer && 
                                    <>
                                        <label className="form-label">Cross Product Transfer</label>
                                        <SelectDropdown
                                            name="CrossBUTransfer"
                                            label="Cross Product Transfers"
                                            value={selectedCrossBUType}
                                            options={crossBUTransferTypeList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }

                                    {isUnderwriterTransfer && 
                                    <>
                                        <label className="form-label">Select Underwriters</label>
                                        <SelectDropdown
                                            name="UnderwritersTransfer"
                                            label="Select Underwriters"
                                            value={selectedUnderwriterType}
                                            options={underwritersTransferList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }

                                    {isServiceTransfer && 
                                    <>
                                        <label className="form-label">Select CRT Transfers</label>
                                        <SelectDropdown
                                            name="CRTTransfer"
                                            label="Select CRT Transfers"
                                            value={selectedServiceTransferType}
                                            options={serviceTransferList}
                                            labelKeyInOptions='TransferName'
                                            valueKeyInOptions="Id"
                                            handleChange={handleChange}
                                            sm={12} md={12} xs={12}
                                        />
                                    </>
                                    }

                                </div>
                            </div>
                            <div className="form-row">
                                <div className="form-group">
                                    {isClaimTransfer && selectedClaimType !== '' &&
                                        <>
                                            <label className="form-label">Select Booking ID</label>
                                            <SelectDropdown
                                                name="BookingId"
                                                label="Booking Id"
                                                value={selectedBookingId}
                                                options={bookingIdList}
                                                labelKeyInOptions='value'
                                                valueKeyInOptions="value"
                                                handleChange={handleChange}
                                                sm={12} md={12} xs={12}
                                            />
                                        </>
                                    }

                                    {isServiceTransfer && selectedServiceTransferType !== '' &&
                                        <>
                                            <label className="form-label">Select Booking ID</label>
                                            <SelectDropdown
                                                name="BookingId"
                                                label="Booking Id"
                                                value={selectedBookingId}
                                                options={bookingIdList}
                                                labelKeyInOptions='value'
                                                valueKeyInOptions="value"
                                                handleChange={handleChange}
                                                sm={12} md={12} xs={12}
                                            />
                                        </>
                                    }
                                    
                                </div>
                                <div className="form-group">

                                </div>
                            </div>
                        </div>
                        

                        {showOverlapDiv ? (
                            <div className="overlap-div">
                                <div className="overlap-content">
                                    {isRMTransfer &&
                                        <div className="rmtransfer-ctc">
                                            <h3>You can either transfer from below panel, or schedule CTC on behalf of customer</h3>
                                            <button className="rm-schedule-ctc" onClick={() => handleScheduleCTC()}>
                                                {isScheduleCTCInProgress ? "Scheduling..." : "Schedule CTC"}
                                            </button>
                                        </div>
                                    }
                                    {isBrandNewCarTLTransfer &&
                                        <div className="brand-newcar-message">
                                            <h3>Your call will be transfered to {brandNewCarTLInfo} on clicking below Transfer Button</h3>
                                        </div>
                                    }
                                    <div className="iframe-container">
                                        <iframe
                                            src={GetIframeURL("CallTranfer", null, { url: CallTransferUrl, productId: rootScopeService.getProductId() })}
                                            title="Call Transfer"
                                            width="100%"
                                            height="400px"
                                            style={{ border: 'none' }}
                                        ></iframe>
                                    </div>

                                    <div className="iframe-actions">
                                        <button className="back-button" onClick={() => setShowOverlapDiv(false)}>
                                            Back
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <>
                                <div className="warning-section">
                                    {/* <div className="warning-box">
                                        <div className="warning-icon">
                                            <WarningAmberIcon />
                                        </div>
                                        <div className="warning-content">
                                            <div className="warning-title">Confirm before proceeding</div>
                                            <div className="warning-text">Please make sure you have informed the customer about merging this call with another advisor</div>
                                        </div>
                                    </div> */}
                                    <div className="action-buttons">
                                        <button className="submit-button" onClick={() => { OpenTransferModel()}}>Transfer</button>
                                    </div>
                                </div>
                            </>
                        )}

                    </>
                )}
            </ModalPopup>

            {/* GetIframeURL("CallTranfer", null, { url: CallTransferUrl }); */}
            {/* <CallTransferModal
                url={CallTransferUrl}
                open={currentPopup === "CallTransfer"}
                handleClose={() => { setCurrentPopup(null) }}
                isRMTransfer={isRMTransfer}
                isSmeInterTeamTransfer={isSmeInterTeamTransfer}
                RMdetails={RMdetails}
                type="CallTransfer"
            /> */}
            {/* <SelectInsurerForCallTranferModal
                url={CallTransferUrl}
                open={currentPopup === "SelectInsurerForCallTranfer"}
                handleClose={() => { setCurrentPopup(null) }}
                OpenCallTranserModal={OpenCallTranserModal}
            /> */}

            <ScheduleCTCSuccessPopup
                open={showScheduleCTCSuccessPopup}
                handleClose={() => setShowScheduleCTCSuccessPopup(false)}
            />

        </>
    )

}

const getCustomerIdForTransfer = (parentLeadId) => {
    let customerId = null;
    try {

        let ConnectCallSF = Common.getConnectCallSFFromLS()
        if (ConnectCallSF && ConnectCallSF.lead && ConnectCallSF.lead.CustomerId) {
            customerId = ConnectCallSF.lead.CustomerId;
        }
        else if (ConnectCallSF && parentLeadId === parseInt(ConnectCallSF.LeadID)) {
            customerId = rootScopeService.getCustomerId();
        }
    }
    catch { }
    return customerId;
}