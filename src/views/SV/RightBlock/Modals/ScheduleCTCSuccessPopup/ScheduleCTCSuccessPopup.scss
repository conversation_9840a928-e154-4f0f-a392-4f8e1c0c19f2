.schedule-ctc-success-popup {
    .MuiDialog-paper {
        border-radius: 12px;
        max-width: 420px;
        width: 90%;
        margin: 16px;
    }

    .success-popup-content {
        padding: 32px 24px 24px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24px;

        .success-icon-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);

            .success-icon {
                font-size: 48px;
                color: white;
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            }
        }

        .success-message {
            text-align: center;
            max-width: 320px;

            h3 {
                margin: 0 0 12px 0;
                font-size: 24px;
                font-weight: 600;
                color: #1a1a1a;
                line-height: 1.3;
            }

            p {
                margin: 0;
                font-size: 16px;
                color: #666;
                line-height: 1.5;
                font-weight: 400;
            }
        }

        .success-actions {
            width: 100%;
            display: flex;
            justify-content: center;

            .success-close-btn {
                background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 32px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
                min-width: 120px;

                &:hover {
                    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
                    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
                    transform: translateY(-1px);
                }

                &:active {
                    transform: translateY(0);
                    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
                }

                &:focus {
                    outline: none;
                    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
                }
            }
        }
    }

    // Animation for the popup
    .MuiDialog-root {
        .MuiBackdrop-root {
            background-color: rgba(0, 0, 0, 0.5);
        }
    }

    // Responsive design
    @media (max-width: 480px) {
        .MuiDialog-paper {
            margin: 8px;
            max-width: calc(100% - 16px);
        }

        .success-popup-content {
            padding: 24px 16px 16px;
            gap: 20px;

            .success-icon-container {
                width: 64px;
                height: 64px;

                .success-icon {
                    font-size: 36px;
                }
            }

            .success-message {
                h3 {
                    font-size: 20px;
                }

                p {
                    font-size: 14px;
                }
            }

            .success-actions {
                .success-close-btn {
                    padding: 10px 24px;
                    font-size: 14px;
                }
            }
        }
    }
}
