import React from "react";
import ModalPopup from '../../../../../components/Dialogs/ModalPopup';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import './ScheduleCTCSuccessPopup.scss';

export const ScheduleCTCSuccessPopup = (props) => {
    return (
        <ModalPopup 
            open={props.open} 
            handleClose={props.handleClose} 
            className="schedule-ctc-success-popup"
            disableBackdropClick={false}
        >
            <div className="success-popup-content">
                <div className="success-icon-container">
                    <CheckCircleRoundedIcon className="success-icon" />
                </div>
                <div className="success-message">
                    <h3>CTC Scheduled Successfully!</h3>
                    <p>The callback has been scheduled successfully. The customer will receive a call back as requested.</p>
                </div>
                <div className="success-actions">
                    <button className="success-close-btn" onClick={props.handleClose}>
                        Got it
                    </button>
                </div>
            </div>
        </ModalPopup>
    )
}
