import { Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { useSnackbar } from "notistack";
import React, { useEffect, useState } from "react";
import { connect, useDispatch, useSelector } from "react-redux";
import { SV_CONFIG } from "../../../../appconfig";
import ModalPopup from "../../../../components/Dialogs/ModalPopup";
import { CALL_API } from "../../../../services";
import rootScopeService from "../../../../services/rootScopeService";
import User from "../../../../services/user.service";
import { setCustomerOTPStatus, updateStateInRedux } from "../../../../store/actions/SalesView/SalesView";
import { ConvertToJSONDate } from "../../../../utils/utility"
import CommonService, { AddLeadToPriorityQueueService, SetCustomerComment } from "../../../../services/Common";
import CallIcon from '@mui/icons-material/Call';
import masterService from "../../../../services/masterService";
import { isNonProgressiveWfhCalling } from "../../../../helpers";

let ConnectCallNewApi = SV_CONFIG["ConnectCallNewApi"];
let ReasonId = 71; 
let callableVirtualNumber={};

const ConnectCallApi = function (CallingNumbers, parentLeadId, index, IsJavascriptActiveForEasyDialer,VirtualNumber) {
    var input = {};
    if(ConnectCallNewApi)
    {
        return ConnectCallSFservice(parentLeadId, 
                                    ReasonId, 
                                    CallingNumbers[index].EncryptedMobileNo, 
                                    CallingNumbers[index].CountryCode, 
                                    CallingNumbers[index].CountryId,
                                    VirtualNumber);
    }
    else
    {
        input =
        {
            LeadId: parentLeadId,
            MobileNo: CallingNumbers[index].EncryptedMobileNo,
            ProductId: rootScopeService.getProductId(),
            RoleId: User.RoleId,
            EmpId: User.EmployeeId,
            GroupId: User.GroupId,
            IsAsterickDialer: User.IsAsterickDialer,
            UserId: User.UserId,
            CustomerId: rootScopeService.getCustomerId(),
            Prefix: 1,
            IsJavascriptActiveForEasyDialer,
            UserName: User.UserName,
            CountryCode: CallingNumbers[index].CountryCode
        };
        var reqData = { objCallDTO: input };
        return ConnectCallService(reqData);
    }
};

const ConnectCallSFservice = (parentLeadId, reasonId, encryptedMobileNo, countryCode, countryId,VirtualNumber) => {
    let source = '';
    if (isNonProgressiveWfhCalling()) {
        source = 'fosapp'
    }

    let apiCallTime = new Date().toJSON();
    if (localStorage.getItem('serverdate')) 
    {
        apiCallTime = localStorage.getItem('serverdate');
    }

    let baseUrl = 'onelead/api/communication/ConnectCallSF/'
    let service = 'MatrixCoreAPI'

    let url = baseUrl
        + parentLeadId + '/' + User.UserId + '/' + rootScopeService.getCustomerId()
        + '?attempt=' + false
        + '&roleId=' + User.RoleId
        + '&apiCallTime=' + apiCallTime
        + '&PriorityReasonId=' + reasonId
        + '&EncryptedMobileNo=' + encryptedMobileNo
        + '&CountryCode=' + countryCode
        + '&CountryId=' + countryId
        

    if (source) {
        url += '&source=' + source;
    }
    if(VirtualNumber)
    {
       url += '&VirtualNumber='+VirtualNumber;
    }
    if (User.Asterisk_IP) { url += '&AsteriskIP=' + User.Asterisk_IP }
    
    const input = {
        url, method: 'GET', service
    };

    return CALL_API(input);
}

const ConnectCallService = function (reqData) {

    let input = {
        method: "POST",
        url: window.location.origin + SV_CONFIG["internalMatrixRef"].serviceUrl + "ConnectCalling",
        service: "custom",
        requestData: reqData,
        timeout: "l"
    }
    var promise = CALL_API(input);

    return promise.then(function (response) {
        if (response) {
            if (response.d && response.d.IsComDial == false)
                window.call(response.d._mobileNo, reqData.objCallDTO.EmpId, reqData.objCallDTO.LeadId, response.d._agentType, reqData.objCallDTO.CountryCode);
            return response;
        }
    }, function (response) {
        // Error                   
        return ({ Error: "Network issue!" });
    });
};

//for non-onelead/non-progressive users
const SelectCallingNumberPopup = (props) => {
    const parentLeadId = useSelector(state => state.salesview.parentLeadId)
    callableVirtualNumber = useSelector(state => state.salesview.callableVirtualNumber)
    const [CallingNumbers, setCallingNumbers] = useState([])
    const [IsCallClicked, setIsCallClicked] = useState(false);
    const [objRestrictCall, setObjRestrictCall] = useState({
        CallAttempetID: 0,
        Credit: 0,
        LastCallDate: "",
        _errorMsg: "",
        _makeCall: false
    });
    const dispatch = useDispatch();
    const CautionNo = ["OTgxOTg1MDE2NQ==", "OTg2OTc0OTczOQ==", "OTkzMDM2MTczOA==", "OTcxNzEwMDgwNw=="];
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();
    // const CheckCustomerOTPStatus = function (CallingNumbers) {
    //     var reqData = {
    //         transactionID: rootScopeService.getCustomerId(),
    //         productId: rootScopeService.getProductId(),
    //         encryptMobileNo: CallingNumbers[0].EncryptedMobileNo,
    //         CountryCode: CallingNumbers[0].CountryCode
    //     };
    //     CommonService.CheckCustomerOTPVerified(reqData).then(function (resultData) {
    //         if (resultData) {
    //             props.setCustomerOTPStatusToRedux(true);
    //         }
    //     }, function () { });
    // };



    const Call = (index) => {
        if(callableVirtualNumber)
        {
            //do nothing
        }
        else
        {
            props.setShowCallBtn(false);

            setTimeout(function () {
                props.setShowCallBtn(true);
            }, 30000);
        }
        
        if (IsCallClicked == false) {
            var Ispriority = rootScopeService.getPriority();
            if (Ispriority) {
                var reqData = { "LeadID": parentLeadId, "WorkDoneTs": ConvertToJSONDate(new Date()), "ActionId": 2, "IsPriorityLead": rootScopeService.getPriorityGrid(), "UserID": User.UserId };
                var priorityLead = {
                    "LeadId": parentLeadId,
                    // "Name": "",
                    // "CustomerId": rootScopeService.getCustomerId(),
                    // "UserID": User.UserId,
                    // "Priority": 1,
                    // "ProductId": rootScopeService.getProductId(),
                    // "Reason": 'Manual added',
                    // "ReasonId": 32,
                    // "CallStatus": "",
                    "IsAddLeadtoQueue":0,
                    "IsNeedToValidate":1
                }
    
                AddLeadToPriorityQueueService(priorityLead).then(function (resultData) {
                    if (resultData != null) {
                        if (resultData && resultData.status > 0) {
                            ConnectUserCall(index);
                        } 
                        else {
                            window.alert(resultData.message);
                        }
                    }
                }, function () {
                        ConnectUserCall(index);
                    });
            } else {
                    ConnectUserCall(index);
                }
            }
    }

    const ConnectUserCall = function (index) 
    {
        let IsJavascriptActiveForEasyDialer;
        try {
            IsJavascriptActiveForEasyDialer
                = User.settings.Key.IsJavascriptActiveForEasyDialer
        } catch (e) { }
        
        setIsCallClicked(true);
        setTimeout(function () {
            setIsCallClicked(false);
            props.handleClose();
        }, 5000);
        var VirtualNumber;
        if(callableVirtualNumber && callableVirtualNumber.VirtualNumber)
        {
            VirtualNumber= callableVirtualNumber.VirtualNumber
        }
        ConnectCallApi(CallingNumbers, parentLeadId, index, IsJavascriptActiveForEasyDialer,VirtualNumber).then(function (response) {
            let message = response.Status ? response.Status : response.Message;
            if(!SV_CONFIG.ManualCallError){
                message = response.Message ? response.Message : response.Status;
            }
            if (response && (response.Message === "Success" || response.Status === "CallInitiated")) 
            {
                if(SV_CONFIG["SmeVNFix"] && SV_CONFIG["SmeVNFix"]==true)
                {
                    //vn fix
                    response.CallInitTime = JSON.stringify(new Date()).replace(/"/g, '');
                    response.CallId = Math.random();
                    response.lead = parentLeadId;
                    response.EmployeeId = User.EmployeeId;
                    window.localStorage.setItem('ConnectCallSF', JSON.stringify(response));
                }
                console.log("Status : " + message);
                if(VirtualNumber)
                {
                    var comment = "Calling By Virtual Number"
                    SaveComment(comment,parentLeadId);
                }
            }
            else
            {
                setObjRestrictCall({
                    _errorMsg: message
                });
                dispatch(updateStateInRedux({ key: 'ObjRestrictCall_errorMsg', value: message }));
            }
        }, function (response) {
            console.error("Error");
        });
    }
    const SaveComment = (Comment, parentLeadId) => {
        let UserId = User.UserId;
        var requestData = {
            "CustomerId": rootScopeService.getCustomerId(),
            "ProductId": rootScopeService.getProductId(),
            parentLeadId,
            UserId,
            "Comment": Comment,
            "EventType": 70
        }
        SetCustomerComment(requestData);
    };

    const GetCallingNumber = () => {
        masterService.GetCallingDetails(rootScopeService.getCustomerId(), parentLeadId).then((resultData) => {
            if (resultData) {
                let productId = Number(rootScopeService.getProductId());
                let data = Array.isArray(resultData) ? resultData : [];
                if (productId !== 117) {
                    data = data.filter(v => v && v.Source !== 'MTX-MOT');
                }
                setCallingNumbers(data);
                data.forEach(function (vdata, key) {
                    if (CautionNo.indexOf(vdata.EncryptedMobileNo) != -1) {
                        enqueueSnackbar("The number you are calling, belongs to an LIC employee, please handle with care.", { variant: 'warning', autoHideDuration: 3000, });
                    }
                    if (vdata.IsPrimary == 1) {
                        // todo :
                        // rootScopeService.setCallingNo(vdata.EncryptedMobileNo, vdata.CountryCode);
                    }
                });
                // CheckCustomerOTPStatus(resultData);
            }
            else {
                setCallingNumbers([]);
            }
        }).catch((e) => {
            setCallingNumbers([]);
        });
    }
    useEffect(() => {
        GetCallingNumber()
    }, []);

    useEffect(() => {
        if (!objRestrictCall._errorMsg) return;
        const snackbarKey = enqueueSnackbar(objRestrictCall._errorMsg, {
            variant: 'error',
            persist: true,
            // className: "multipleCallPopup",
            preventDuplicate: true,
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
            }
        });
        return () => { closeSnackbar(snackbarKey) };
    }, [closeSnackbar, enqueueSnackbar, objRestrictCall._errorMsg]);

    return (
        <>
            <ModalPopup open={props.open} title='Select Contact Number' handleClose={props.handleClose} className="SelectContactNumberPopup">
            {callableVirtualNumber && callableVirtualNumber.VirtualNumber && <h4 className="subTitile">(Calling with Virtual No)</h4>}
                {(Array.isArray(CallingNumbers) && CallingNumbers.length === 0) ? <p>No Data Found</p> :
                    <Grid item sm={12} md={12} xs={12}>
                        <TableContainer component={Paper}>
                            <Table stickyHeader aria-label="sticky table">
                                <TableHead>
                                    <TableRow>
                                        <TableCell>Number</TableCell>
                                        <TableCell>Details</TableCell>
                                        <TableCell align="right">Call</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {CallingNumbers.map((row, index) => (
                                        <TableRow key={index} className="row">
                                            <TableCell >{row.MobileNo}{row.IsPrimary === 1 ? "(Primary)" : ""} </TableCell>
                                            <TableCell >{`(${row.Name} ${row.Country})${row.IsPrimary == 1 ? " - Primary" : ""}`}</TableCell>
                                            <TableCell align="right">
                                                <span
                                                    onClick={() => { return IsCallClicked ? null : Call(index) }}
                                                    disabled={IsCallClicked}
                                                    className={IsCallClicked ? "disabled" : "CallBtn"}
                                                >
                                                    <CallIcon />
                                                </span>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Grid>
                }
            </ModalPopup>
        </>
    )
}

const mapDispatchToProps = dispatch => {
    return {
        setCustomerOTPStatusToRedux: (value) => dispatch(setCustomerOTPStatus({ IsCustomerVerified: value }))
    };
};

export default connect(() => ({}), mapDispatchToProps)(SelectCallingNumberPopup);

