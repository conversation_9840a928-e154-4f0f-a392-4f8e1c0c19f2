import React, { useEffect, useState } from "react";
import { CONFIG, SV_CONFIG } from "../../../appconfig";
import rootScopeService from "../../../services/rootScopeService";
import AlarmIcon from '@mui/icons-material/Alarm';
import { useInterval } from "../Main/helpers/useInterval";
import { CALL_API } from "../../../services";
import User from "../../../services/user.service";
import { ImagePopup } from "../Main/Modals/ImagePopup";
import { useSelector } from "react-redux";

const SLIDE_TIME = 7000;
const INTERACTION_SLIDE_TIME = 20000;
export default function SuggestionBox(props) {

    let ProductId = rootScopeService.getProductId();
    const [IsNRI, setIsNRI] = useState(false);
    let DaysToGo = 0;
    const [isJAGUser, setIsJAGUser] = useState(false);
    const [OpenImgPopup, setOpenImgPopup] = useState(false);
    const [isAppInstalled, setisAppInstalled] = useState(false);
    const [ImgUrl, setImgUrl] = useState("");
    var d = new Date(); // for now
    var date1 = new Date('February 4, 2022 00:00:00');
    let dates = [11, 13, 16, 18, 20, 23, 25, 27];
    let JAGdatecondtition = ((d.getHours() === 8 && d.getMinutes() >= 45) || d.getHours() === 9) && dates.includes(d.getDate());
    const IsRenewal = useSelector(state => state.salesview.IsRenewal);
    let [slideTime, setSlideTime] = useState(SLIDE_TIME)

    useEffect(() => {
        if (props.data && Array.isArray(props.data)) {
            setIsNRI(false);
            if (rootScopeService.getProductId() == 2) {
                props.data.forEach(lead => {
                    if (lead.Country && (['392', '91', '999', 'INDIA', '0', 'NULL'].indexOf(lead.Country) == -1)) {
                        setIsNRI(true);
                    }
                });
            }
        }
    }, [props.data]);

    const checkZeroTermInsuranceGroup = (GroupIds) => {
        var usergrp = User.UserGroupList;
        let result = false;
        if (usergrp && usergrp.length > 0) {
            usergrp.forEach(function (item, key) {
                if (GroupIds.indexOf(item.GroupId) > -1) {
                    result = true;
                }
            });
        }
        return result;
    };

    let data = [
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/Breaking_news.jpg",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/Breaking_News_banner.jpg",
            OpenImageInPopup: true,
            showForproducts: [117, 139]
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/GSTRefundProcess.png",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/DetailedGSTRefundProcess1.jpg",
            OpenImageInPopup: true,
            showForproducts: [2],
            showConditions: (IsNRI)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/GSTRefundGuidelines.png",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/DetailsGSTRefundGuidelines.jpg",
            OpenImageInPopup: true,
            showForproducts: [2],
            showConditions: (IsNRI)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/IsAppInstalled.png",
            showConditions: (isAppInstalled)
        },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/agentvoicesurvey.png",
        //     link: "https://www.surveymonkey.com/r/BJGD7XJ",
        //     showForproducts: [2]
        // },

        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/jag-training.png",
        //     link: JAGdatecondtition ? "https://meet.google.com/xie-vesr-nau" : "https://docs.google.com/forms/d/164M5a0ghwjDWa44243r85i7kEhy2kFrdsSJvW042XrQ/edit?ts=618cc3a4",
        //     showConditions: (isJAGUser)
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/helpdesk.jpeg",
        //     link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/helpdesk.jpeg",
        //     showForproducts: [2],
        //     showConditions: (!IsRenewal),
        //     OpenImageInPopup : true
        // },
        
        // agent assist for health renewal
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/agentassist.png",
            link: "https://pbw.policybazaar.com/workbook/68472bbbc7a9f95ca3ef1bac",
            showForproducts: [2],
            showConditions: (IsRenewal)

        },
        // agent assist for health fresh 
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/opdagentassist.png",
            link: "https://pbw.policybazaar.com/workbook/6847369a0f4c28d00311d32e",
            showForproducts: [2],
            showConditions: (!IsRenewal)

        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/FeaturesGrid.png",
            // link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/FeaturesGridSuggestionBox.png",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/UpdatedFeaturesGridSuggestionBox.png",
            OpenImageInPopup: true,
            showForproducts: [2],
            showConditions: (IsRenewal)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/PortabilityGrid.png",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/PortabilityGridSuggestionBox.png",
            OpenImageInPopup: true,
            showForproducts: [2],
            showConditions: (IsRenewal)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/AdvisorPlaybook.png",
            link: "https://docs.google.com/presentation/d/137QvxWrzyRtFr1NdOEfmSheWwXWz3Nw2jxdaQsnBjq8/edit?usp=sharing",
            showForproducts: [2],
            showConditions: (IsRenewal)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/Banner_23 Nov.png",
            link: JAGdatecondtition ? "https://meet.google.com/nyu-ueff-ecp" : "https://forms.gle/KJtZudbQDTiuoXvn7 ",
            showConditions: (isJAGUser)
        },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/work_week.png",
        //     link: "https://www.surveymonkey.com/r/Term_Sep2021",
        //     showForproducts: [7, 1000],
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/maxv.jpeg",
        //     showForproducts: [7],
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/pbbuyonline.jpeg",
        //     showForproducts: [7],
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/tatanml.jpeg",
        //     showForproducts: [7],

        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/work_weekUpdated.png",
        //     link: "https://www.surveymonkey.com/r/Health_NOV20",
        //     showForproducts: [2],

        // },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/work_weekUpdated.png",
            link: "https://www.surveymonkey.com/r/Motor_Jan22_Agents",
            showForproducts: [117, 139],
            showConditions: (d < date1)

        },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/term_icci_smart.jpeg",
        //     showForproducts: [7],
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/termSelection.jpeg",
        //     showForproducts: [7],
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/pbakshay.jpeg",
        //     link: "https://www.policybazaar.com/pblife/whats_new/AapKiSideHai_Policybazaar_Unveils_New_Brand_Campaign_On_IPL_22",
        //     showConditions: rootScopeService.getProductId() !== 7 && rootScopeService.getProductId() !== 2
        // },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/CRM_3_July01.jpg",
            link: "https://www.policybazaar.com/pblife/decoding_insurance/Feeling_Inadequately_Insured_3_Ways_To_Extend_Your_Health_Insurance_Cover?utm_source=MyAccount&utm_medium=banner&utm_campaign=Display",
            showForproducts: [2, 106, 118, 130],
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/pbadvantagesuggestionbox.jpeg",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/pbAdvantage.png",
            OpenImageInPopup: true,
            showForproducts: [2],
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/healthbannersuggestionbox.jpeg",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/healthbanner.png",
            OpenImageInPopup: true,
            showForproducts: [2, 117],
        },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/social_media_star.jpg",
        //     link: "https://form.jotform.com/pbquiz/voice-of-the-agent_health",
        // },
        // {
        //     image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/pb_support_logo.jpg",
        // },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/bottom_img.jpg",
            header: <div style={{ background: '#f65d4d', color: "white", padding: '4px', textAlign: 'center' }}><h2><AlarmIcon />{DaysToGo} DAYS TO GO</h2></div>,
            showForproducts: [],
            showConditions: (DaysToGo > 0)
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/claim.jpeg",
            link: "https://ci.policybazaar.com/claimsgarages",
            showForproducts: [117],
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/FrontImgPSU.jpeg",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/BackImgPSU.jpeg",
            OpenImageInPopup: true,
            showForproducts: [117],
        },
        {
            image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/0costterminsurance.png",
            link: CONFIG.PUBLIC_URL + "/images/salesview/Banners/0costterminsurance.png",
            OpenImageInPopup: true,
            showConditions: (User.RoleId == 13) ? checkZeroTermInsuranceGroup([711, 1101]) : false
        }
    ]
    // todo remove comments later
    // const [data, setData] = useState([

    // ]);
    // useEffect(() => {
    //     setData(data.filter((item, index) => {
    //         return ((!item.showForproducts || item.showForproducts.indexOf(ProductId) !== -1) && (item.showConditions === undefined || item.showConditions));
    //     }))
    // }, [RefreshLead, ProductId]);

    //const [url, setUrl] = useState(`https://cctechincentive.policybazaar.com/timer/${cryptoService.encode(User.UserId)}`);
    useEffect(() => {
        let AppInstalltionActive = (window.SV_CONFIG_UNCACHED && window.SV_CONFIG_UNCACHED.AppInstalltionActive) || SV_CONFIG.AppInstalltionActive;
        if (AppInstalltionActive == true) {
            IsAppInstalledforCustomer();
        }
        let apiUrl = `${SV_CONFIG["IncentiveAPIPublic"][SV_CONFIG["environment"]]}Jag/GetVisibleMenus/${User.UserId}`;
        const input = {
            url: apiUrl,
            method: 'GET', service: 'custom',
            Header: {
                'Access-Control-Allow-Credentials': true
            },
            withCredentials: true
        };
        CALL_API(input)
            .then((resultdata) => {
                if (resultdata) {
                    try {
                        let obj = JSON.parse(resultdata.Response)[0];
                        if (obj.JAG) {
                            setIsJAGUser(true);
                            // setData([
                            //     {
                            //         image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/jag-training.png",
                            //         link: "https://forms.gle/Bxmy4K3JKhiR7ssE6",
                            //     },
                            //     {
                            //         image: CONFIG.PUBLIC_URL + "/images/salesview/Banners/jag-training.png",
                            //         link: "https://meet.google.com/isf-pcfg-hct",
                            //     },
                            //    ...data]
                            // );
                        }
                    }
                    catch (e) {
                        console.log(e);
                    }
                }
            })

    }, []);

    const IsAppInstalledforCustomer = () => {
        const input =
        {
            url: 'api/LeadDetails/IsAppInstalled/' + rootScopeService.getCustomerId(),
            method: "GET",
            service: "MatrixCoreAPI"
        }
        CALL_API(input).then((result) => {
            if (result == true) {
                setisAppInstalled(true);
            }
            else {
                setisAppInstalled(false);
            }
        });
    }

    const handleclick = (item) => {
        if (item.link && item.OpenImageInPopup) {
            setImgUrl(item.link);
            setOpenImgPopup(true);
        }
        else if (item.link) {
            window.open(item.link);
        }
        else {
            setImgUrl(item.image);
            setOpenImgPopup(true);
        }
    }

    const [slideIndex, setSlideIndex] = useState(0);
    useInterval(() => {
        setSlideIndex((slideIndex + 1) % (data.length));
        setSlideTime(SLIDE_TIME);
    }, slideTime);
    data = (data.filter((item, index) => {
        return ((!item.showForproducts || item.showForproducts.indexOf(ProductId) !== -1) && (item.showConditions === undefined || item.showConditions));
    }))

    if (data.length === 0) return null;

    return (
        <>
            <div key={1} className="suggestionBox mt-2">
                <ul>
                    <li>
                        <div className="slideshow-container">
                            {(data && data.length > 0) &&
                                data.map((item, index) =>
                                (<div key={index}
                                    className={slideIndex === index ? "mySlides show" : "mySlides fade"}
                                >
                                    {/* target={item.link ? "_blank" : null} */}
                                    {/* <a href={item.link || item.image} target="_blank" >
                                        {item.header && <span>{item.header}</span>}
                                        <img src={item.image} alt="banner" />
                                    </a> */}
                                    <div onClick={() => handleclick(item)} >
                                        {item.header && <span>{item.header}</span>}
                                        <img src={item.image} alt="banner" />
                                    </div>

                                </div>))
                            }
                        </div>
                        <div className="dotBox">
                            {(data && data.length > 1) && (
                                data.map((item, index) => (
                                    <span key={index}
                                        className={slideIndex === index ? "dot active" : "dot"}
                                        onClick={(e) => { setSlideIndex(index); setSlideTime(INTERACTION_SLIDE_TIME) }}
                                    ></span>
                                ))
                            )}

                        </div>
                    </li>

                </ul>
            </div>

            <ImagePopup
                open={OpenImgPopup}
                handleClose={() => { setOpenImgPopup(false) }}
                ImgUrl={ImgUrl}
                alt="banner"
            />

        </>
    );
}
