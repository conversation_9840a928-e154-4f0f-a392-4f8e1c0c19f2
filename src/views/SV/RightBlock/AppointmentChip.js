import React, { useEffect, useState } from "react";
import { CONFIG, SV_CONFIG } from "../../../appconfig/app.config";
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { useSnackbar } from 'notistack';
import { GetAppointmentDataService, GetIsAppointmentEligiblePostChurn } from "../../Features/FosHelpers/fosServices";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { updateStateInRedux } from "../../../store/actions";
import rootScopeService from "../../../services/rootScopeService";
import AlarmIcon from '@mui/icons-material/Alarm';
import DateRangeIcon from '@mui/icons-material/DateRange';
import { FOSpopUp } from "./Modals/FOSpopUp";
import RoomIcon from '@mui/icons-material/Room';
import User from "../../../services/user.service";
import { DateChange, CheckExpectedDeliveryDate, checkPolicyExpiry,GetCurrentCallTime } from "../../Features/FosHelpers/fosCommonHelper";
import { getCurrentTalkTime } from "../../../services/Common";


export default function AppointmentChip(props) {

    const [IsAppointment, setIsAppointment] = useState(false);
    const [AppointmentDetails, setAppointmentDetails] = useState([]);

    const [IsScheduledByCustomer, setIsScheduledByCustomer] = useState(false);
    const [IsScheduledByAdvisor, setIsScheduledByAdvisor] = useState(false);
    const [IsError, setIsError] = useState(false);
    const [FormattedAppointmentDateTime, setFormattedAppointmentDateTime] = useState('');
    const [IsLoading, setIsLoading] = useState(true);
    const [AppointmentSubStatus, setAppointmentSubStatus] = useState("");
    const [AppointmentTime, setAppointmentTime] = useState("");
    const [CurrentStatusId, setCurrentStatusId] = useState("");
    const [CurrentSubStatusId, setCurrentSubStatusId] = useState("");
    const [IsEnableAppointmentBtn, setIsEnableAppointmentBtn] = useState("");
    const [OpenFOSLink, setOpenFOSLink] = useState(false);
    const [PolicyExpiryDate, setPolicyExpiryDate] = useState();
    const [ParentLeadSourceId, setParentLeadSourceId] = useState(0);
    const [LeadAssignedUser, setLeadAssignedUser] = useState(0);
    const [PolicyType, setPolicyType] = useState('');
    const [ExpectedDeliveryDate, setExpectedDeliveryDate] = useState();
    const [RegistrationDate, setRegistrationDate] = useState();

    const { enqueueSnackbar } = useSnackbar();


    //const [AppointmentDateTime, setAppointmentDateTime] = useState("");
    let AppointmentRestrictTalktimeGroups=false;
    let usergrp = User.UserGroupList || [];
    usergrp.forEach(function (item, key) {
       
        if (SV_CONFIG.AppointmentRestrictTalktimeGroupIds && (SV_CONFIG.AppointmentRestrictTalktimeGroupIds.indexOf(item.GroupId) > -1)) {
            AppointmentRestrictTalktimeGroups = true;
        }
    });

    const FOSSubStatus = SV_CONFIG["FOSSubStatus"][SV_CONFIG["environment"]];
    const FOSCustomerSources = SV_CONFIG["FOSCustomerSources"];

    //const [AppointmentDateTime,setAppointmentDateTime]=useState("");
    const [ParentLeadId, AppointmentSummaryRefresh, allLeads, FOSHealthRenewalTTEligibility, PODTodayTalkTime, PODHistoryTalktime,next5leads] = useSelector(state => {
        let { parentLeadId, AppointmentSummaryRefresh, allLeads, FOSHealthRenewalTTEligibility, PODTodayTalkTime, PODHistoryTalktime ,next5leads} = state.salesview;
        return [parentLeadId, AppointmentSummaryRefresh, allLeads, FOSHealthRenewalTTEligibility, PODTodayTalkTime, PODHistoryTalktime,next5leads]
    });

    const dispatch = useDispatch();
    const clearState = () => {
        setIsScheduledByAdvisor(false);
        setIsScheduledByCustomer(false);
        setFormattedAppointmentDateTime('');
        setIsError(false);
        setAppointmentSubStatus("");
        setAppointmentTime("");
        setIsAppointment(false);
    }

    const getAppointmentChipDetails = () => {
        clearState();
        let routeParams = window.location.pathname.split('/');
        if (!routeParams) {
            return 0;
        }

        if (ParentLeadId > 0) {
            let _customerId = rootScopeService.getCustomerId();// parseInt(atob(routeParams[reqIndex]).split('/')[0]);

            setIsLoading(true);
            GetAppointmentDataService(_customerId, ParentLeadId)
                .then(function (response) {
                    if (response && response.CustomerId > 0) {
                        setAppointmentDetails(response);
                        console.log("resposne in appointment Details", response);
                        dispatch(updateStateInRedux({ key: "AppointmentData", value: response }));
                        // dispatch(updateStateInRedux({ key: "AppointmentDateTime", value: response.AppointmentDateTime }));
                        // setAppointmentDateTime(response.AppointmentDateTime);
                        var res = DateChange(response.AppointmentDateTime);
                        if (res != null) {
                            setFormattedAppointmentDateTime(res.FormattedAppointmentDateTime);
                            setAppointmentTime(res.AppointmentTime);

                        }
                        setIsAppointment(true);

                        if (response.Source && FOSCustomerSources.indexOf(response.Source.toLowerCase()) > -1) {
                            setIsScheduledByCustomer(true);
                        }
                        else {
                            setIsScheduledByAdvisor(true);
                            //[2088, 2124].indexOf(response.subStatusId) > -1 ? setAppointmentSubStatus("Appointment Confirmed") : setAppointmentSubStatus("Appointment Booked");
                            //[2005].indexOf(response.subStatusId) > -1 ? setAppointmentSubStatus("Appointment Rescheduled") : setAppointmentSubStatus("Appointment Booked");

                        }
                        if (parseInt(response.subStatusId) === 2002)
                            setAppointmentSubStatus("Appointment Booked")
                        else if (parseInt(response.subStatusId) === 2005)
                            setAppointmentSubStatus("Appointment Rescheduled")
                        else if (parseInt(response.subStatusId) === 2088)
                            setAppointmentSubStatus("Appointment Confirmed")
                        else if (parseInt(response.subStatusId) === 2124)
                            setAppointmentSubStatus("Appointment Start")
                        else if (parseInt(response.subStatusId) === 2193)
                            setAppointmentSubStatus("Start Journey")
                        else if (parseInt(response.subStatusId) === 2194)
                            setAppointmentSubStatus("End Journey")
                        else
                            setAppointmentSubStatus("Appointment Booked")
                    }
                    else {
                        setIsAppointment(false);
                    }
                })
                .catch((error) => {
                    setIsError(true);
                })
                .finally(() => {
                    setIsLoading(false);
                })
        }

    }
    const Appointmentrefresh = () => {
        if (ParentLeadId > 0) {
            getAppointmentChipDetails();
        }
    }
    const IsEnableAppointment = function () {
        setIsEnableAppointmentBtn(false)

        let _subStatusId = 0, _statusId = 0;

        // find substatusId of ParentLead
        allLeads.forEach(function (lead) {
            if (lead.LeadID === ParentLeadId) {
                _subStatusId = lead.SubStatusId
                _statusId = lead.StatusId;
                setCurrentStatusId(_statusId);
                setCurrentSubStatusId(_subStatusId);
                setPolicyExpiryDate(lead.PolicyExpiryDate);
                setParentLeadSourceId(lead.LeadSourceId);
                setLeadAssignedUser(lead.LeadAssignedUser);
                setPolicyType(lead.PolicyType);
                setExpectedDeliveryDate(lead.ExpectedDeliveryDate);
                setRegistrationDate(lead.RegistrationDate);

            }
        })


        if (_statusId >= 2) {
            setIsEnableAppointmentBtn(true);
        }
    }



    const handleEdit = (showNoAppointmentdesign) => {
        var onCall = window.localStorage.getItem("onCall") === "true" ? true : false;
        if (props.ProductId == 2 && props.IsLeadSetRenewal && User.RoleId == 13 && LeadAssignedUser != User.UserId) {
            setOpenFOSLink(false);
            enqueueSnackbar("You are not allowed to take an action on this lead", {
                variant: 'error',
                autoHideDuration: 5000,
            });
            return;
        }
        if (AppointmentRestrictTalktimeGroups && SV_CONFIG.AppCreateProductTalktimeConfig && SV_CONFIG.AppCreateProductTalktimeConfig[props.ProductId]&& showNoAppointmentdesign  && !(ParentLeadSourceId==3 && ['6','7'].indexOf(User.GroupProcessId)>-1)) {
            if (!onCall && PODTodayTalkTime + PODHistoryTalktime <= SV_CONFIG.AppCreateProductTalktimeConfig[props.ProductId]) {
                setOpenFOSLink(false);
                enqueueSnackbar("You don't have enough talk time to schedule an appointment on this lead.", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
                return;
            }

            const talktime = GetCurrentCallTime(next5leads,ParentLeadId) + PODTodayTalkTime + PODHistoryTalktime;
            const duration = getCurrentTalkTime();
            const requiredTalkTime = SV_CONFIG.AppCreateProductTalktimeConfig[props.ProductId];

            if(onCall &&  !(duration > (requiredTalkTime + 40) || talktime > requiredTalkTime))
            {
                setOpenFOSLink(false);
                enqueueSnackbar("You don't have enough talk time to schedule an appointment on this lead.", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
                return;
            }
        }

        if (props.ProductId == 2 && props.IsLeadSetRenewal && showNoAppointmentdesign && !FOSHealthRenewalTTEligibility) {
            setOpenFOSLink(false);
            enqueueSnackbar("You don't have enough talk time to schedule an appointment on this lead.", {
                variant: 'error',
                autoHideDuration: 5000,
            });
            return;
        }
        if ((props.ProductId == 117) && ParentLeadSourceId != 3 && showNoAppointmentdesign) {
            if (PolicyType.toLocaleLowerCase() != 'new' && checkPolicyExpiry(PolicyExpiryDate)) {
                setOpenFOSLink(false);
                enqueueSnackbar("Appointment creation cannot proceed as the policy expiry date criteria is not fulfilled.", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
                return;
            }
            else if (PolicyType.toLocaleLowerCase() == 'new' && !CheckExpectedDeliveryDate(ExpectedDeliveryDate)) {
                setOpenFOSLink(false);
                enqueueSnackbar("Appointment cannot be created as Vehicle Delivery Date criteria not met.", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
                return;
            }
            else if (RegistrationDate>0) {
                const currentDate = dayjs();
                const registrationDate = dayjs(RegistrationDate);
                const fiveYearsFromRegistration = registrationDate.add(5, 'year');
                
                if (currentDate.isAfter(fiveYearsFromRegistration)) {
                    setOpenFOSLink(false);
                    enqueueSnackbar("Appointment cannot be created as the registration date is more than 5 years old.", {
                        variant: 'error',
                        autoHideDuration: 5000,
                    });
                    return;
                }
            }

        }
        if (props.ProductId == 139 && ParentLeadSourceId != 3 && showNoAppointmentdesign && checkPolicyExpiry(PolicyExpiryDate)) {
            setOpenFOSLink(false);
            enqueueSnackbar("Appointment creation cannot proceed as the policy expiry date criteria is not fulfilled.", {
                variant: 'error',
                autoHideDuration: 5000,
            });
            return;
        }
        if (!showNoAppointmentdesign && SV_CONFIG.AppointmentStatusEditConfig && SV_CONFIG.AppointmentStatusEditConfig.indexOf(AppointmentDetails.subStatusId) > -1) {
            setOpenFOSLink(false);
            enqueueSnackbar("Appointment Editing is disabled as Journey or Appointment has already been started.", {
                variant: 'error',
                autoHideDuration: 5000,
            });
            return;
        }

        // Check appointment visit count for health fresh products
        if (props.ProductId === 2 && showNoAppointmentdesign && !props.IsLeadSetRenewal ) {
            // Find the current lead to get AppoitnmentVisitedCount
            let currentLead = allLeads.find(lead => lead.LeadID === ParentLeadId);
            if (currentLead && currentLead.AppointmentVisitedCount && currentLead.AppointmentVisitedCount >=3) {
                setOpenFOSLink(false);
                enqueueSnackbar("Appointment cannot be created as the appointment visit count has exceeded the limit of 3", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
                return;
            }
        }

        GetIsAppointmentEligiblePostChurn(ParentLeadId).then(function (response) {
            if (!!response) {
                setOpenFOSLink(true);
            }
            else {
                setOpenFOSLink(false);
                enqueueSnackbar("You don't have enough talk time to schedule an appointment on this lead", {
                    variant: 'error',
                    autoHideDuration: 5000,
                });
            }

        }).catch((err) => {
            setOpenFOSLink(true);
        })

        //localStorage.setItem("FOSChipObj", JSON.stringify(FOSChipObj));
    }

    useEffect(() => {
        if (ParentLeadId > 0) {
            Appointmentrefresh();
            IsEnableAppointment();
        };
    }, [ParentLeadId])
    useEffect(() => {
        if (AppointmentSummaryRefresh) {
            getAppointmentChipDetails();
            dispatch(updateStateInRedux({ Key: 'AppointmentSummaryRefresh', value: false }))
        }
    }, [AppointmentSummaryRefresh])




    useEffect(() => {

        let AppointmentUpdateEvent = function (event) {
            try {
                if (event.data && event.data.action === 'AppCancel') {
                    setOpenFOSLink(false);
                    Appointmentrefresh();

                }
                else if (event.data && event.data.action === 'AppointmentUpdate') {
                    Appointmentrefresh();
                    DateChange();
                }
            } catch (e) {
                setOpenFOSLink(false);

            }
        }
        window.addEventListener('message', AppointmentUpdateEvent);
        return () => { window.removeEventListener("message", AppointmentUpdateEvent) }

    }, [ParentLeadId])




    let chipClass = IsScheduledByCustomer ? "BookedAppoitmentchip" : "confirmedAppoitmentchip";
    //let headingText = IsScheduledByCustomer ? 'Appointment Booked' : AppointmentSubStatus;
    let headingText = AppointmentSubStatus;
    let scheduledBy = IsScheduledByCustomer ? 'by Customer' : 'by Advisor';
    let scheduledByIcon = IsScheduledByCustomer ? "/images/salesview/bookedIcon.svg" : "/images/salesview/noun-support.svg";

    let matrixGoLogo = "/images/salesview/Stroke_png.gif";
    const showNoAppointmentdesign = !IsAppointment && !IsError && !IsLoading;

    if (showNoAppointmentdesign || IsLoading) {
        headingText = 'No Scheduled Appointment';
        scheduledBy = '';
        chipClass = "noActiveAppoitment";
        scheduledByIcon = "/images/salesview/calendaricon.svg"
        // scheduledByIcon = true;
        // matrixGoLogo = "";
        // return (
        //     <div className="confirmedAppoitmentchip">
        //         <p className="noActiveAppoitment">
        //             No appointment is scheduled
        //         </p>
        //     </div>
        // )
    }
    // if (IsLoading) {
    //     headingText = '';
    //     return null;
    // }

    // if (IsError && !IsLoading) {
    //     return (
    //         <div className="wrongError">
    //             <p>Appointment Details Not Fetched!</p>
    //             <a onClick={Appointmentrefresh}> Click here to retry</a>
    //         </div>
    //     )
    // }





    return (
        <>

            {IsError && !IsLoading
                && <div className="wrongError">
                    <p>Appointment Details Not Fetched!</p>
                    <a onClick={Appointmentrefresh}> Click here to retry</a>
                </div>
            }
            {!IsLoading && (IsScheduledByAdvisor || IsScheduledByCustomer || 1) &&
                <div className={chipClass}>

                    {matrixGoLogo && <img src={CONFIG.PUBLIC_URL + matrixGoLogo} className="matrixGoLogo" alt=" " />}
                    <div className="leftSide">
                        <h4>{headingText}</h4>
                        <p>{scheduledBy}</p>
                    </div>
                    {scheduledByIcon && <img src={CONFIG.PUBLIC_URL + scheduledByIcon} alt=" " />}

                    {AppointmentTime && <div className="addressbox"> <RoomIcon /><p title={AppointmentDetails && AppointmentDetails.Address + " " + AppointmentDetails.Address1 + " " + (AppointmentDetails.Pincode > 0 ? AppointmentDetails.Pincode : "")}>{AppointmentDetails && AppointmentDetails.Address + " " + AppointmentDetails.Address1 + " " + (AppointmentDetails.Pincode > 0 ? AppointmentDetails.Pincode : "")}</p></div>}
                    <div className="bottomBox">
                        {AppointmentTime && FormattedAppointmentDateTime && <span><DateRangeIcon /> {FormattedAppointmentDateTime}</span>}
                        {AppointmentTime && <span><AlarmIcon /> {AppointmentTime}</span>}

                        <p disabled={!IsEnableAppointmentBtn} onClick={() => { handleEdit(showNoAppointmentdesign) }}>
                            {showNoAppointmentdesign ? 'Create New Appointment' : 'Edit'}
                            <ArrowForwardIosIcon />
                        </p>

                    </div>
                </div>
            }
            {
                OpenFOSLink && <FOSpopUp
                    open={OpenFOSLink}
                    // view={isViewOnly}
                    handleClose={() => {
                        setOpenFOSLink(false);
                    }}
                    LeadSource={(props && props.LeadSource) ? props.LeadSource : false}
                    AppCreatedSource={AppointmentDetails && AppointmentDetails.AppCreatedSource}
                    parentId={ParentLeadId}
                // selectedSubStatus={NewSMELeadStatus.SubStatusID}
                />
            }
        </>
    );
}
