import React, { useState, useEffect, useRef } from "react";
//import { useLocation, useSearchParams } from "react-router-dom";
import { CALL_API } from "../../../../services/api.service";
import User from "../../../../services/user.service";
import "./NewSVCalendar.scss";
import { array } from "prop-types";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import ArrowCircleRightOutlinedIcon from "@mui/icons-material/ArrowCircleRightOutlined";
import rootScopeService from "../../../../services/rootScopeService";
import { SV_CONFIG } from "../../../../appconfig";

export const NewSVCalendar = (props) => {
  const sanitizeInteger = (value) => {
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? null : parsed;
  };
  const sanitizeString = (value) => {
    return typeof value === "string" ? value.trim() : null;
  };
  // Get parameters from query string or fallback to props
  const url = new URL(window.location.href);
  const params = new URLSearchParams(url.search);
  const agentId = sanitizeInteger(params?.get("AgentID")) ?? sanitizeInteger(props?.data?.AgentID);
  const parentId = sanitizeInteger(params?.get("ParentID")) ?? sanitizeInteger(props?.data?.ParentID);
  const ProductID = sanitizeInteger(params?.get("ProductID")) ?? sanitizeInteger(props?.data?.ProductID);
  const IsRenewal = sanitizeInteger(params?.get("IsRenewal")) ?? sanitizeInteger(props?.data?.IsRenewal);
  const ServiceLead = sanitizeInteger(params?.get("ServiceLead")) ?? sanitizeInteger(props?.data?.ServiceLead);
  const source = sanitizeString(params?.get("source")) ?? sanitizeString(props?.data?.source);
  const pageOpenedForProcess = sanitizeString(params?.get("process")) ?? sanitizeString(props?.data?.process);

  const [isProcessingComplete, setIsProcessingComplete] = useState(false);
  const [week] = useState(["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]);
  const [monthNames] = useState([
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ]);
  const [DefaultDuration, setDefaultDuration] = useState(3);
  const [CellsInRow, setCellsInRow] = useState(20);
  const [hours, sethours] = useState([]);
  const [WeekDatesData, setWeekDatesData] = useState(
    Array(7).fill({ CR: 0, CA: 0, AA: 0, S: 0, TT: 0 })
  );
  const [ActiveDateEvents, setActiveDateEvents] = useState([]);
  const [activeDate, setActiveDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState("");
  const [activeDay, setActiveDay] = useState(new Date().getDay() - 1);
  const [activeMonthYear, setActiveMonthYear] = useState("");
  const [weekDates, setWeekDates] = useState([]);
  const [TimeArray, setTimeArray] = useState([]);
  const [callbackPopupVisible, setCallbackPopupVisible] = useState(false);
  const [ActiveLeadIdEvent, setActiveLeadIdEvent] = useState({});
  const [ActiveLeadIdEventDate, setActiveLeadIdEventDate] = useState("");
  const [LeaveDates, setLeaveDates] = useState([]);
  const [blocked, setblocked] = useState([]);
  const [leaveblocked, setleaveblocked] = useState([]);
  const [isShowCallBKDiv, setIsShowCallBKDiv] = useState(false);
  const [activeCell, setActiveCell] = useState(-1);
  const [callBackTypeId, setCallBackTypeId] = useState("");
  const [subject, setSubject] = useState("");
  const [paymentCall, setPaymentCall] = useState(false);
  const [SetCBStartTime, setSetCBStartTime] = useState("");
  const [NRITime, setNRITime] = useState("");
  const [StartTime, setStartTime] = useState("");
  const [cbHover, setCbHover] = useState(-1); // State to track hovered cell
  const hoverTimer = useRef(null); // Ref to hold the timer ID
  const [toggleNumber, setToggleNumber] = useState(undefined);
  const toggleContainerRef = useRef(null);
  const [CalenderData, setCalenderData] = useState([]);
  const [CRcallBackTime, setCRcallBackTime] = useState(new Date());
  const [CallbackDuration, setCallbackDuration] = useState(0);
  const [ShiftedEvents, setShiftedEvents] = useState([]);
  const [nriTimingElement, setnriTimingElement] = useState(false);
  const [RecommendedCallTimes, setRecommendedCallTimes] = useState({});
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  useEffect(() => {
    // Initialize the calendar data
    const initializeCalendar = async () => {
      await GetCalenderData();
      await GetRecommendedCallTime();
      await refreshDate();
      setIsProcessingComplete(true);
    };
    initializeCalendar();
  }, []);

  useEffect(() => {
    if (weekDates.length > 0 && CalenderData.Data) {
      UpdateActiveDateEvents(CalenderData, activeDate, hours);
    }
  }, [weekDates, CalenderData, activeDate]);

  useEffect(() => {
    if (weekDates.length > 0 && CalenderData.Data) {
      const updatedWeekDatesData = weekDates.map(() => ({
        CR: 0,
        CA: 0,
        AA: 0,
        S: 0,
        TT: 0,
      }));

      if(CalenderData.Data.events && Array.isArray(CalenderData.Data.events) && CalenderData.Data.events.length >0)
      {
        CalenderData.Data.events.forEach((event) => {
          const eStartTime = new Date(event.StartDate);
          const eEndTime = new Date(event.EndDate);
          const indexDate = weekDates.findIndex(
            (date) => date.toDateString() === eStartTime.toDateString()
          );

          if (indexDate !== -1) {
            switch (event.Color) {
              case 1:
                updatedWeekDatesData[indexDate].CR += 1;
                updatedWeekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 2:
                updatedWeekDatesData[indexDate].CA += 1;
                updatedWeekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 3:
                updatedWeekDatesData[indexDate].AA += 1;
                updatedWeekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 4:
                updatedWeekDatesData[indexDate].S += 1;
                updatedWeekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
            }
          }
        });
      }

      setWeekDatesData(updatedWeekDatesData);
    }
  }, [weekDates, CalenderData]);

  const refreshDate = async () => {
    const month = activeDate.getMonth();
    const year = activeDate.getFullYear();
    setActiveMonthYear(`${monthNames[month]} '${year.toString().slice(-2)}`);
    await calculateWeekDates();
  };

  const calculateWeekDates = () => {
    return new Promise((resolve) => {
      const tempDates = [];
      const startOfWeek = new Date(activeDate);

      // Adjust start date to Monday
      const dayOfWeek = activeDate.getDay();
      const offset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      startOfWeek.setDate(activeDate.getDate() + offset);

      const dayIndex = Math.floor(
        (activeDate - startOfWeek) / (1000 * 60 * 60 * 24)
      );
      setActiveDay(dayIndex);

      // Populate dates for the entire week
      for (let i = 0; i < 7; i++) {
        tempDates.push(new Date(startOfWeek));
        startOfWeek.setDate(startOfWeek.getDate() + 1);
      }
      setWeekDates(tempDates);
      resolve(); // Notify that the operation is complete
    });
  };

  const IsOnLeaveToday = (LeaveDates, CalenderData, activeDate, TimeArray, hours) =>
  {  
    if(LeaveDates && Array.isArray(LeaveDates) && LeaveDates.length > 0)
    {
      var Blocked = [];
      const activeDateFormatted = activeDate.toLocaleDateString("en-US");
      let isOnLeave = LeaveDates.some((leaveDate) => {
        const leaveDateFormatted = new Date(leaveDate).toLocaleDateString("en-US");
        return leaveDateFormatted === activeDateFormatted;
      });
      //Temporary disabled leaves
      //isOnLeave = false;
      const { Country } = CalenderData && CalenderData.Data;
      //Exclude NRI agents
      if (isOnLeave && (!Country || Country == "")) {
        if (CalenderData && CalenderData.Data &&
          CalenderData.Data.allowedtime &&
          Array.isArray(CalenderData.Data.allowedtime) &&
          CalenderData.Data.allowedtime.length > 0
        ) {
          CalenderData.Data.allowedtime.forEach((timeRange) => {
            const start = new Date(`${activeDateFormatted} ${timeRange.StartTime}`);
            const end = new Date(`${activeDateFormatted} ${timeRange.EndTime}`);
            Blocked = fillArray(start, end, Blocked, true, TimeArray, hours);
          });
        } else {
          // Block all slots from 00:00 to 23:57
          const startOfDay = new Date(`${activeDateFormatted} 00:00`);
          const endOfDay = new Date(`${activeDateFormatted} 23:57`);
          Blocked = fillArray(startOfDay, endOfDay, Blocked, true, TimeArray, hours);
        }
      }
      setleaveblocked(Blocked);
    }
  }

  // get hours and time in am pm format
  const hoursTimeAmPm = (date) => {
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? "PM" : "AM";
    hours = hours > 12 ? hours - 12 : hours;
    hours = hours < 10 ? "0" + hours : hours;
    minutes = minutes < 10 ? "0" + minutes : minutes;
    var strTime = hours + ":" + minutes + " " + ampm;
    return strTime;
  };

  const GetCalenderData = async () => {
    let ActiveDate = new Date();
    let hour = [];
    const input = {
      url: `coremrs/api/CallBackSchedular/GetCallBacks`,
      method: "POST",
      service: "MatrixCoreAPI",
      timeout: 2000,
      requestData: {
        AgentId: agentId,
        Id: parentId,
        IsCore: false,
      },
    };
    CALL_API(input).then((resultdata) => {
      if (resultdata && resultdata.ErrorCode == 0 && resultdata.Data) {
        setCalenderData(resultdata);

        let duration = DefaultDuration;
        if (resultdata.Data.duration > 0) {
          duration = resultdata.Data.duration;
          setDefaultDuration(duration);
        }
        const timeArray = [];
        for (let i = 0; i < 60; i += duration) {
          timeArray.push(i.toString().padStart(2, "0"));
        }
        setTimeArray(timeArray);
        setCellsInRow(60 / duration);

        if (source && source.toLowerCase() == "fosapp") {
          IsCallBackAllowed(parentId).then(function (res) {
              if (res && !res.status) {
                  window.alert(res.message);
              }
          });
      }

        var objActiveLeadIdEvent = {};
        if(resultdata.Data.events && Array.isArray(resultdata.Data.events) && resultdata.Data.events.length >0)
        {
          objActiveLeadIdEvent = resultdata.Data.events.find(
            (item) => item.Id === parentId
          );
        }
        if (objActiveLeadIdEvent && objActiveLeadIdEvent != {} && objActiveLeadIdEvent !== null && objActiveLeadIdEvent !== undefined && Object.keys(objActiveLeadIdEvent).length > 0) {
          setActiveLeadIdEvent(objActiveLeadIdEvent);
          var activeLeadIdEventDate = new Date(objActiveLeadIdEvent.StartDate);
          var date = activeLeadIdEventDate.getDate(),
            month = activeLeadIdEventDate.toString().substr(4, 3),
            year = activeLeadIdEventDate.getFullYear();
          setActiveLeadIdEventDate(
            date +
              " " +
              month +
              " " +
              year +
              " " +
              hoursTimeAmPm(activeLeadIdEventDate)
          );
          ActiveDate = new Date(objActiveLeadIdEvent.StartDate);
          setActiveDate(new Date(objActiveLeadIdEvent.StartDate));
          //setActiveDate(ActiveDate);
        } else {
          setActiveLeadIdEventDate(undefined);
          setActiveDate(ActiveDate)
        }

        // Extract start and end business times
        if (
          resultdata.Data &&
          resultdata.Data.start &&
          resultdata.Data.start.length > 0 &&
          resultdata.Data.end &&
          resultdata.Data.end.length > 0
        ) {
          const startBusiness = resultdata.Data.start.split(":");
          const endBusiness = resultdata.Data.end.split(":");

          // Parse start and end hours
          const startHour = parseInt(startBusiness[0], 10);
          const endHour =
            parseInt(endBusiness[1], 10) === 0
              ? parseInt(endBusiness[0], 10)
              : parseInt(endBusiness[0], 10) + 1;

          // Populate hours, considering cases where startHour > endHour
          if (startHour > endHour) {
            for (let i = startHour; i < 24; i++) {
              hour.push(i);
            }
            for (let i = 0; i < endHour; i++) {
              hour.push(i);
            }
          } else {
            for (let i = startHour; i < endHour; i++) {
              hour.push(i);
            }
          }
          sethours(hour);

          var Blocked = [];
          var eStDtTime = new Date(
            new Date().toDateString() + " " + startHour + ":00:00"
          );
          for (var i = 0; i < resultdata.Data.allowedtime.length; i++) {
            var endDtTime = new Date(
              new Date().toDateString() +
                " " +
                resultdata.Data.allowedtime[i]["StartTime"]
            );
            Blocked = fillArray(eStDtTime, endDtTime, Blocked, true, timeArray, hour);
            eStDtTime = new Date(
              new Date().toDateString() +
                " " +
                resultdata.Data.allowedtime[i]["EndTime"]
            );
          }
          endDtTime = new Date((new Date()).toDateString() + " " + endHour + ":00:00");
          Blocked = fillArray(eStDtTime, endDtTime, Blocked, true, timeArray, hour);
          setblocked(Blocked);
        }
        getCRCallBacks(resultdata);
        var LeaveDatesArray = [];
        if (
          resultdata.Data.LeaveData &&
          Array.isArray(resultdata.Data.LeaveData) &&
          resultdata.Data.LeaveData.length > 0
        ) {
          for (i = 0; i < resultdata.Data.LeaveData.length; i++) {
            LeaveDatesArray.push(resultdata.Data.LeaveData[i].LeaveDate);
          }
          IsOnLeaveToday(LeaveDatesArray, resultdata, ActiveDate, timeArray, hour);
          setLeaveDates(LeaveDatesArray);
        }
      } else {
        console.log("No Calender Data Found");
      }
      UpdateActiveDateEvents(resultdata, ActiveDate, hour);
    });
  };

  const GetRecommendedCallTime = async () => {
    const CustomerID = rootScopeService.getCustomerId();
    const input = {
      url: `onelead/api/LeadPrioritization/GetRecommendedCallTime/${CustomerID}`,
      method: "GET",
      service: "MatrixCoreAPI",
      timeout: 2000,
    };
    
    try {
      const resultData = await CALL_API(input);
      if (resultData) {
        setRecommendedCallTimes(resultData);
      }
      else {
        setRecommendedCallTimes({});
      }
    } catch (error) {
      setRecommendedCallTimes({});
      console.log("Error fetching recommended call times:", error);
    }
  };

  const isRecommendedTime = (hour, dayName) => {
    if (!RecommendedCallTimes || !RecommendedCallTimes[dayName]) {
      return false;
    }
    
    const dayRecommendations = RecommendedCallTimes[dayName];
    const recommendation = dayRecommendations.find(rec => rec.HourOfDay === hour);
    
    return recommendation && 
           (recommendation.Recommendation === "Recommended" || 
            recommendation.Recommendation === "Highly Recommended");
  };

  const UpdateActiveDate = () => {
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      setActiveDate(newDate);

      const startOfWeek = new Date(newDate);
      startOfWeek.setDate(newDate.getDate() - (newDate.getDay() - 1));
      const dayIndex = Math.floor(
        (newDate - startOfWeek) / (1000 * 60 * 60 * 24)
      ); // Difference in days
      setActiveDay(dayIndex);
      setActiveDateEvents([]);
      UpdateActiveDateEvents(CalenderData, newDate, hours);
    }
  };

  useEffect(() => {
    refreshDate();
    IsOnLeaveToday(LeaveDates, CalenderData, activeDate, TimeArray, hours);
  }, [activeDate]);

  const changeMonth = (direction) => {
    const newDate = new Date(
      activeDate.setMonth(activeDate.getMonth() + direction)
    );
    setActiveDate(newDate);
    setActiveDateEvents([]);
    UpdateActiveDateEvents(CalenderData, newDate, hours);
  };

  const changeWeek = (direction) => {
    const newDate = new Date(
      activeDate.setDate(activeDate.getDate() + direction * 7)
    );
    setActiveDate(newDate);
    setActiveDateEvents([]);
    UpdateActiveDateEvents(CalenderData, newDate, hours);
  };

  const changeDate = (index) => {
    setActiveDate(new Date(weekDates[index]));
    setActiveDay(index);
    setActiveDateEvents([]);
    UpdateActiveDateEvents(CalenderData, new Date(weekDates[index]), hours);
  };

  const validateleaveData = () => {
    var Isallowed = true;
    if (LeaveDates && array.isArray(LeaveDates) && LeaveDates.length > 0) {
      for (let i = 0; i < LeaveDates.length; i++) {
        if (
          activeDate.toDateString() == new Date(LeaveDates[i]).toDateString()
        ) {
          Isallowed = false;
        }
      }
    }
    return Isallowed;
  };
  const validateleaveDataOnBind = (dateIndex) => {
    var date = new Date(weekDates[dateIndex]);
    var Isallowed = true;
    for (var i = 0; i < LeaveDates.length; i++) {
      if (date.toDateString() == new Date(LeaveDates[i]).toDateString()) {
        Isallowed = false;
      }
    }
    return Isallowed;
  };

  const SetCallbackDuration = (duration) => {
    if (!validateleaveData()) {
      if (
        !window.confirm(
          "You have applied for leave/Comp off. Do you want set call back?"
        )
      ) {
        return;
      }
    }
    duration != undefined
      ? setCallbackDuration(duration)
      : setCallbackDuration(0);
  };

  const fillArray = (eStDtTime, endDtTime, fillThisArray, fillThisItem, timeArray, hourarray) => {
    let eCrDateTime = new Date(eStDtTime); // Clone the start date to avoid modifying the original
    const updatedArray = [...fillThisArray]; // Clone the existing array to avoid direct mutation
    // Extract the hour and minutes from the current time
    const hourTime = eCrDateTime.getHours(); // Format: "HH:MM"
    const getTimeMinute = eCrDateTime.getMinutes(); // Extract minutes
    const getMinute = getTimeMinute - (getTimeMinute % DefaultDuration); // Round to nearest default duration
    const timeIndex = timeArray && Array.isArray(timeArray) && timeArray.indexOf(getMinute.toString().padStart(2, "0")); // Find the index in the time array

    const hourIndex = hourarray && Array.isArray(hourarray) && hourarray.indexOf(hourTime); // Find the index in the hour array
    let itemIndex = hourIndex * CellsInRow + timeIndex; // Calculate the event index

    // Populate the array while the current time is within the event's duration
    while (eCrDateTime >= eStDtTime && eCrDateTime < endDtTime) {
      updatedArray[itemIndex++] = fillThisItem; // Add the item at the calculated index
      // Increment the current time by the default duration
      eCrDateTime = new Date(
        eCrDateTime.getTime() + DefaultDuration * 60 * 1000
      );
    }
    return updatedArray;
  };

  const UpdateActiveDateEvents = (calenderData, ActiveDate, hourArray) => {
    let activeDateEvents = [];
    let weekDatesData = [];
    let custReqEvents = {};

    if (calenderData.Data) {
      let duration = DefaultDuration;
      if (calenderData.Data.duration > 0) {
        duration = calenderData.Data.duration;
        setDefaultDuration(duration);
      }
      const timeArray = [];
      for (let i = 0; i < 60; i += duration) {
        timeArray.push(i.toString().padStart(2, "0"));
      }
      setTimeArray(timeArray);
      setCellsInRow(60 / duration);

      const events = calenderData.Data.events;
      const activeDateString = ActiveDate.toDateString();

      // Initialize week dates data with default values
      if(weekDates && Array.isArray(weekDates) && weekDates.length > 0)
      {
        weekDates.forEach((_, index) => {
          weekDatesData[index] = { CR: 0, CA: 0, AA: 0, S: 0, TT: 0 };
        });
      }

      // Process events
      if (events && Array.isArray(events) && events.length > 0) {
        events.forEach((event) => {
          const eStartTime = new Date(event.StartDate);
          const eEndTime = new Date(event.EndDate);

          // Events on the active date
          if (activeDateString === eStartTime.toDateString()) {
            let timeCss = "";
            let callBackType = "";
            let highIntentText = "";

            switch (event.Color) {
              case 1:
                timeCss = "redbg";
                callBackType = "Customer Requested";
                highIntentText = "High Intent Customer. Do not miss";

                if (calenderData.Data.LeadAssignedUser === User.UserId) {
                  event.PopopUpShown = 0;

                  const prevCallbacks = JSON.parse(
                    localStorage.getItem("custReqCallBacks") || "{}"
                  );
                  if (prevCallbacks) {
                    for (const [key, prevCallback] of Object.entries(
                      prevCallbacks
                    )) {
                      if (key !== "UserId" && prevCallback?.Id === event.Id) {
                        event.PopopUpShown = prevCallback.PopopUpShown;
                      }
                    }
                  }
                  custReqEvents[event.Id] = event;
                }
                break;
              case 2:
                timeCss = "greenbg";
                callBackType = "Customer Agreed";
                break;
              case 3:
                timeCss = "bluebg";
                callBackType = "Agent Agreed";
                break;
              default:
                callBackType = "System Callback";
                timeCss = "bluebg";
            }

            const eStartStr = `${eStartTime.toDateString()} ${hoursTimeAmPm(
              eStartTime
            )}`;
            const eEndStr = Math.floor((eEndTime - eStartTime) / (1000 * 60));
            const isPaymentCallback = event.IsPaymentCallback ? "Yes" : "No";

            // Construct event object
            const eventObj = {
              Color: timeCss,
              LeadStatus: event.LeadStatus,
              ProductName: event.ProductName,
              Name: event.Name,
              callBackType: callBackType,
              Id: event.MatrixLeadId || event.Id,
              StartDate: eStartStr,
              EndDate: eEndStr,
              Subject: event.Subject,
              IsPaymentCallback: isPaymentCallback,
              CallbackTypeId: event.Color,
              CustomerId: event.CustomerId,
              highIntenttext: highIntentText,
              IsGoogleInvite: event.IsGoogleInvite,
            };

            // Push events into active date events
            if(hourArray && Array.isArray(hourArray) && hourArray.length> 0 && timeArray && Array.isArray(timeArray) && timeArray.length> 0)
            {
              activeDateEvents = fillArray(
                eStartTime,
                eEndTime,
                activeDateEvents,
                eventObj,
                timeArray,
                hourArray
              );
              setActiveDateEvents(activeDateEvents);
            }
          }

          // Events within the week dates
          const indexDate = weekDates.findIndex(
            (date) => date.toDateString() === eStartTime.toDateString()
          );
          if (indexDate !== -1) {
            switch (event.Color) {
              case 1:
                weekDatesData[indexDate].CR += 1;
                weekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 2:
                weekDatesData[indexDate].CA += 1;
                weekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 3:
                weekDatesData[indexDate].AA += 1;
                weekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
              case 4:
                weekDatesData[indexDate].S += 1;
                weekDatesData[indexDate].TT +=
                  (eEndTime - eStartTime) / (1000 * 60);
                break;
            }
          }
        });
      }

      // Save customer requested events to local storage
      if (Object.keys(custReqEvents).length > 0) {
        custReqEvents.UserId = User.UserId;
        localStorage.setItem("custReqCallBacks", JSON.stringify(custReqEvents));
      }
    } else {
      alert(calenderData.Error);
    }

    // Update state with results
    if (
      weekDatesData &&
      Array.isArray(weekDatesData) &&
      weekDatesData.length > 0
    ) {
      setWeekDatesData(weekDatesData);
    }
  };

  const clearToggle = (type) => {
    setSubject("");
    setCallBackTypeId("");
    setPaymentCall(false);

    if (type === "hide") {
      setActiveCell(-1);
      if (toggleContainerRef.current) {
        toggleContainerRef.current.style.clipPath = "inset(0 0 0 %)";
        toggleContainerRef.current.style.backgroundColor = "#9c9999";
      }
      setCallbackPopupVisible(false);
    } else {
      //setSubject("callback");
      setCallbackPopupVisible(true);
    }
  };

  function addtime(time, TimeDiff) {
    var date = new Date(activeDate);;
    let FinalTime = "";
    let times = time.split(":");
    let TimeDiffs = TimeDiff.split(":");
    times[0] = parseInt(times[0]) + parseInt(TimeDiffs[0]);
    times[1] = parseInt(times[1]) + parseInt(TimeDiffs[1]);
    if (times[1] >= 60) {
      times[1] = times[1] - 60;
      times[0]++;
    }
    if (times[0] >= 24) {
      times[0] -= 24;
      date.setDate(date.getDate() + 1);
    }
    FinalTime =
      date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear();
    times[0] < 10 && (times[0] = "0" + times[0]);
    times[1] < 10 && (times[1] = "0" + times[1]);
    FinalTime = FinalTime + " " + times.join(":");
    return FinalTime;
  }
  function Subtracttime(time, TimeDiff) {
    var date = new Date(activeDate);;
    let FinalTime = "";
    let times = time.split(":");
    let TimeDiffs = TimeDiff.split(":");
    times[0] = parseInt(times[0]) - parseInt(TimeDiffs[0]);
    times[1] = parseInt(times[1]) - parseInt(TimeDiffs[1]);
    if (times[1] < 0) {
      times[1] = 60 + times[1];
      times[0]--;
      //if (times[1] >= 30) { times[0]--;}
    }
    if (times[1] >= 60) {
      times[1] = times[1] - 60;
      times[0]++;
    }
    if (times[0] <= 0) {
      times[0] = 24 + times[0];
      date.setDate(date.getDate() - 1);
    }
    FinalTime =
      date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear();
    times[0] < 10 && (times[0] = "0" + times[0]);
    times[1] < 10 && (times[1] = "0" + times[1]);
    if (times[0] == 24) {
      times[0] = "00";
    }

    FinalTime = FinalTime + " " + times.join(":");
    return FinalTime;
  }

  const setCallBack = (cellIndex, startTime, isGoogleInvite, isBlocked) => {
    if (isGoogleInvite) {
      alert("This slot is already booked for Customer Requested callback.");
      return;
    }
    if (isBlocked) {
      alert("You cannot set callback for this slot.");
      return;
    }

    if (!isShowCallBKDiv) {
      // Update active cell and start time
      setActiveCell(cellIndex);
      setStartTime(startTime);
      localStorage.setItem("StartTime", startTime);

      // Show callback div
      setIsShowCallBKDiv(true);
      clearToggle("show");

      // Handle NRI timings
      const { TimeDiffinSecs, Country } = CalenderData.Data;
      if (Country && Country.toLowerCase() !== "india" && TimeDiffinSecs) {
        let NRItime = "";
        if (TimeDiffinSecs.startsWith("+")) {
          NRItime = addtime(startTime, TimeDiffinSecs.substring(1));
        } else if (TimeDiffinSecs.startsWith("-")) {
          NRItime = Subtracttime(startTime, TimeDiffinSecs.substring(1));
        }
        setNRITime(NRItime);
        setnriTimingElement(true);
      }

      // Append current date to the callback start time
      const currentDate = activeDate;
      const finalDate = `${currentDate.getDate()}/${
        currentDate.getMonth() + 1
      }/${currentDate.getFullYear()}`;
      setSetCBStartTime(`${finalDate} ${startTime}`);

      // Set default callback type
      toggleCallbackType(1);
    } else {
      // Hide callback div
      setActiveCell(-1);
      setSetCBStartTime("-");
      setIsShowCallBKDiv(false);
      setnriTimingElement(false);

      clearToggle("hide");
    }
  };

  const handleMouseEnter = (kx, ky) => {
    clearTimeout(hoverTimer.current); // Clear any existing timer

    hoverTimer.current = setTimeout(() => {
      const cellIndex = kx * CellsInRow + ky; // Calculate cell index
      setCbHover(cellIndex); // Update hover state
    }, 1000); // Set hover delay
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    clearTimeout(hoverTimer.current); // Clear the hover timer
    hoverTimer.current = null; // Reset timer reference
    setCbHover(-1); // Reset hover state
  };
  const detailsBoxClass = `DetailsBox ${
    ActiveDateEvents &&
    Array.isArray(ActiveDateEvents) &&
    ActiveDateEvents.length > 0 &&
    ActiveDateEvents[cbHover] &&
    ActiveDateEvents[cbHover].CallbackTypeId === 1
      ? "bgRed"
      : ActiveDateEvents &&
        Array.isArray(ActiveDateEvents) &&
        ActiveDateEvents.length > 0 &&
        ActiveDateEvents[cbHover] &&
        ![1, 2, undefined].includes(ActiveDateEvents[cbHover].CallbackTypeId)
      ? "bgBlue"
      : ""
  }`;

  const toggleCallbackType = (type) => {
    let newToggleNumber;

    if (toggleNumber === undefined) {
      newToggleNumber = type !== 1;
    } else {
      newToggleNumber = !toggleNumber;
    }

    if (type === 1) {
      newToggleNumber = false;
    }

    setToggleNumber(newToggleNumber);

    if (toggleContainerRef.current) {
      toggleContainerRef.current.style.clipPath = newToggleNumber
        ? "inset(0 0 0 50%)"
        : "inset(0 50% 0 0)";
      toggleContainerRef.current.style.backgroundColor = "#0065ff";
    }

    //setSubject(newToggleNumber ? "BestGuess" : "CustomerAgreed");
    setCallBackTypeId(newToggleNumber ? 3 : 2);
  };

  const validateInsertCallback = () => {
    var user = window.localStorage.getItem("User");
    var isChatGroup = true;
    if (user) {
      user = JSON.parse(window.atob(user));
      var UserGroupId = user.GroupId;
      if ((SV_CONFIG["HealthChatCallBackGrps"] && Array.isArray(SV_CONFIG["HealthChatCallBackGrps"]) && SV_CONFIG["HealthChatCallBackGrps"].indexOf(UserGroupId) > -1) || (User.IsChat && ProductID==117) ) {
        isChatGroup = false;
      }
    }

    if (
      (CheckPrioritygrp() && isChatGroup) ||
      (source && source.length > 0 && source.toLowerCase() == "fosapp")
    ) {
      IsCallBackAllowed(parentId).then(
        function (resultdata) {
          if (
            resultdata &&
            !resultdata.status
          ) {
            window.alert(resultdata.message);
            GetCalenderData();
          } else {
            MarkCallBack();
          }
        },
        function () {
          MarkCallBack();
        }
      );
    } else {
      MarkCallBack();
    }
  };
  const CheckPrioritygrp = () => {
    var Ispriority = false;
    if (
      window.localStorage.getItem("isOneLead") != null &&
      window.localStorage.getItem("isOneLead") == "true"
    )
      Ispriority = true;
    return Ispriority;
  };

  const IsCallBackAllowed = (ParentLeadId) => {
    let cbTime = "";
    if (activeDate !== "" && StartTime !== "") {
      // Get the start time from localStorage
      const slot = localStorage.getItem("StartTime");
      const startDate = new Date(
        `${new Date(activeDate).toDateString()} ${slot}`
      );

      if (startDate) {
        // Format the callback time (MM/DD/YYYY HH:mm)
        cbTime =
          `${
            startDate.getMonth() + 1
          }/${startDate.getDate()}/${startDate.getFullYear()} ` +
          `${startDate.toString().substr(16, 5)}`;
      }
    }
    const input = {
      url: `onelead/api/LeadPrioritization/IsCallBackAllowed/${ParentLeadId}`,
      method: "GET",
      service: "MatrixCoreAPI",
    };
    return CALL_API(input);
  };
  const MarkCallBack = () => {
    let callbackDuration = CallbackDuration;
    if (CallbackDuration == "" || CallbackDuration == 0) {
      setCallbackDuration(3);
      callbackDuration = 3;
    }
    let CallBackTypeId = callBackTypeId;
    if (CallBackTypeId == "") {
      setCallBackTypeId(2);
      CallBackTypeId = 2;
    }
    let SubjectText = subject;
    if(subject == "")
    {
      SubjectText = toggleNumber ? "BestGuess" : "CustomerAgreed";
    }
    if (SubjectText && SubjectText.length > 0) {
      if (CallBackTypeId && parseInt(CallBackTypeId || 0) > 0) {
        let startDate = new Date(
          `${new Date(activeDate).toDateString()} ${StartTime}`
        );
        let endDate = new Date(
          startDate.getTime() + callbackDuration * 60 * 1000
        );
        if (CRcallBackTime <= startDate) {
          if (startDate > new Date()) {
            var d = new Date();
            if (
              IsRenewal == "1" &&
              [2, 116, 118, 130].indexOf(parseInt(ProductID)) !=
                -1 &&
              endDate > d.setDate(d.getDate() + 10)
            ) {
              console.log(d.setDate(d.getDate() + 10));
              window.alert(
                "Callback  for renewals cannot be set beyond 10 days"
              );
              return;
            }
            if (
              parseInt(ProductID) == 131 &&
              endDate > new Date(new Date().getTime() + 5184000000)
            ) {
              window.alert("Callback cannot be set beyond 60 days");
              return;
            }
            if (
              parseInt(ProductID) != 131 &&
              endDate > new Date(new Date().getTime() + 2592000000)
            ) {
              window.alert("Callback cannot be set beyond 30 days");
              return;
            }
            startDate =
              startDate.getMonth() +
              1 +
              "/" +
              startDate.getDate() +
              "/" +
              startDate.getFullYear() +
              " " +
              startDate.toString().substr(16, 5);
            endDate =
              endDate.getMonth() +
              1 +
              "/" +
              endDate.getDate() +
              "/" +
              endDate.getFullYear() +
              " " +
              endDate.toString().substr(16, 5);
            let insertData = {
              AgentId: agentId,
              Id: parentId,
              Subject: SubjectText,
              EventTypeId: 4, //event type id=4 for call back event
              StartDate: startDate,
              EndDate: endDate,
              CallBackTypeId: callBackTypeId,
              IsPaymentCallback: paymentCall,
              AdminId: User.UserId,
              IsCore: false,
              NeedId: 0,
              NeedStatusId: 0,
              ServiceLead: ServiceLead,
            };
            removeCallback();
            InsertCallBacks(insertData);
          } else {
            window.alert("Callback cannot be set in the past");
          }
        } else {
          window.alert(
            "Callback cannot be set before " +
              CRcallBackTime.toString().replace(/GMT.*/g, "")
          );
        }
      } else {
        window.alert("Please select a call back type.");
      }
    }
    //  else {
    //   window.alert("Subject is required.");
    // }
  };
  const removeCallback = () => {
    setCallbackDuration(0);
    setStartTime("0:0");
  };
  const InsertCallBacks = (insertData) => {
    const activeCallbackDateTime = insertData.StartDate;
    try {
      const input = {
        url: `coremrs/api/CallBackSchedular/InsertCallSchedulerEvent`,
        method: "POST",
        service: "MatrixCoreAPI",
        timeout: 2000,
        requestData: insertData,
      };
      CALL_API(input).then((data) => {
        if (data && data.IsInserted) {
          // Add logic to show notification
          if (
            source &&
            source.toLowerCase() == "fosapp" &&
            pageOpenedForProcess == "APP_COMPLETION" &&
            window.ReactNativeWebView
          ) {
            // for matrixgoapp, appointment complete
            window.ReactNativeWebView.postMessage("calendar_back_web");
          }
          let shiftedEvents = data.Events == null ? [] : data.Events;
          setShiftedEvents(shiftedEvents);
          window.sessionStorage.setItem(
            "activeCallbackDateTime",
            activeCallbackDateTime
          );
          var statusChklst = ["1", "2", "3", "4"];
          if (
            paymentCall == true &&
            statusChklst.indexOf(insertData.NeedStatusId) != -1
          ) {
            window.sessionStorage.setItem("IsPaymentCallabck", 1);
          }
          GetCalenderData();
          // if (window.top.getReminderData) {
          //   try {
          //     window.top.getReminderData();
          //   } catch (ex) {}
          // }
          clearToggle("hide");
          window.alert("Callback saved Successfully");
        } else {
          if (data.Error) {
            window.alert(data.Error);
          }
        }
      });
    } catch (err) {
      window.alert("Unable to save! Please try again.");
    }
  };

  const getCRCallBacks = (CalData) => {
    let ProductIDListForCallRestriction = [1, 2, 117, 106, 118, 130];
    let Productid = CalData.Data.ProductID;
    if (ProductIDListForCallRestriction.includes(Productid)) {
      let body = JSON.stringify({
        item: { Data: { Type: 2, LeadID: parentId } },
      });
      const input = {
        url: "CallRestriction/GetLeadCreditPointsData",
        method: "POST",
        service: "core",
        requestData: body,
      };
      CALL_API(input).then((response) => {
        var data = response?.data;
        if (
          data &&
          data.GetLeadCreditPointsDataResult &&
          data.GetLeadCreditPointsDataResult.Data
        ) {
          //Set When Agent can set callback
          let callRestrictData = data.GetLeadCreditPointsDataResult.Data;
          if (callRestrictData && callRestrictData.LastCallDate) {
            var date = callRestrictData.LastCallDate;
            var substringedDate = date.substring(6);
            var parsedIntDate = parseInt(substringedDate, 10);
            setCRcallBackTime(new Date(parsedIntDate));
          }
        }
      });
    }
  };

  const closeCallbackPopup = () => {
    clearToggle("hide");
  };

  return isProcessingComplete ? (
    <div className="calendarUI">
      {callbackPopupVisible && (
        <div className="container1">
          <a className="closebtn" onClick={closeCallbackPopup}>
            &times;
          </a>
          <span className="box_heading">
            Set Callback for:
            <br />
            <span className="selectTime">
              <b>{SetCBStartTime}</b>
            </span>
          </span>
          <hr className="verticalLine" />
          {nriTimingElement &&
            <span
              id="NRITimings"
              className="box_heading NRITime"
            >
              <span>
                <b>{CalenderData.Data.Country}</b>
              </span>{" "}
              : <br />
              <span className="selectTime">
                <b>{NRITime}</b>
              </span>{" "}
              <span className="timeSec">
                ({CalenderData.Data.TimeDiffinSecs} hours)
              </span>
            </span>
          }
          <div className="inner_box">
            <ul className="popup_block">
              <label>Callback Type</label>
              <li>
                <span style={{ position: "relative" }}>
                  <div id="container">
                    <div className="inner-container">
                      <div
                        className="toggle"
                        onClick={() => toggleCallbackType(2)}
                      >
                        <p>Best Guess</p>
                      </div>
                      <div
                        className="toggle"
                        onClick={() => toggleCallbackType(1)}
                      >
                        <p>Customer Agreed</p>
                      </div>
                    </div>
                    <div
                      className="inner-container"
                      id="toggle-container"
                      ref={toggleContainerRef}
                    >
                      <div
                        className="toggle"
                        onClick={() => toggleCallbackType(2)}
                      >
                        <p>Best Guess</p>
                      </div>
                      <div
                        className="toggle"
                        onClick={() => toggleCallbackType(1)}
                      >
                        <p>Customer Agreed</p>
                      </div>
                    </div>
                  </div>
                </span>
              </li>
              <li>
                <div className="form-item">
                  <label>Payment Callback</label>
                  <div className="toggle-switch">
                    <label>
                      <input
                        id="payment"
                        type="checkbox"
                        checked={paymentCall}
                        onChange={() => setPaymentCall(!paymentCall)}
                      />
                      <span>
                        <a></a>
                      </span>
                    </label>
                  </div>
                </div>
              </li>
              <li>
                <input
                  id="txtSubject"
                  type="text"
                  className="subject-input"
                  maxLength={200}
                  placeholder="Subject (Optional)"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </li>
              <li>
                <button className="submit-btn" onClick={validateInsertCallback}>
                  Submit
                </button>
              </li>
            </ul>
          </div>
        </div>
      )}
      <div className="MainCalContainer">
        {ActiveLeadIdEvent && ActiveLeadIdEventDate && (
          <label className="notification">
            Active callback : {ActiveLeadIdEventDate}
          </label>
        )}
        {ShiftedEvents &&
          Array.isArray(ShiftedEvents) &&
          ShiftedEvents.length > 0 && (
            <label className="float-right notification">
              Call Back Shifted to {ShiftedEvents[0].StartDate}
            </label>
          )}
        <div className="datepicker_box_top">
          <div className="left">
            <input
              type="date"
              id="txtGoToDatePicker"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
            <button onClick={UpdateActiveDate}>Go</button>
          </div>
          {CalenderData &&
            CalenderData.Data &&
            CalenderData.Data.UserName &&
            CalenderData.Data.UserName != "" && (
              <div className="setcallBack">
                <p className="callbackLabel">Set callback for</p>
                <p className="emyidName">
                  {CalenderData.Data.UserName} - {CalenderData.Data.EmployeeId}
                </p>
              </div>
            )}
          <div className="datepicker-header">
            {/* <button
              className="prev_icon"
              onClick={() => changeMonth(-1)}
            ></button> */}
            <ArrowCircleLeftOutlinedIcon
              onClick={() => changeMonth(-1)}
              className="prevIcon"
            />
            <label>{activeMonthYear}</label>
            {/* <button
              className="next_icon"
              onClick={() => changeMonth(1)}
            ></button> */}
            <ArrowCircleRightOutlinedIcon
              onClick={() => changeMonth(1)}
              className="NextBtn"
            />
          </div>
          <div className="calendarbox">
            <ArrowCircleLeftOutlinedIcon
              onClick={() => changeWeek(-1)}
              className="prevIcon"
            />
            <div className="daybx">
              {week.map((day, index) => (
                <ul
                  key={index}
                  className={index === activeDay ? "hightlightdayUl" : ""}
                  onClick={() => changeDate(index)}
                >
                  <li className="CalTitle">
                    <em>{weekDates[index]?.getDate()}</em> {day}
                  </li>
                  {!validateleaveDataOnBind(index) && (
                    <li className="bold">On Leave</li>
                  )}
                </ul>
              ))}
            </div>
            <ArrowCircleRightOutlinedIcon
              onClick={() => changeWeek(1)}
              className="NextBtn"
            />
            <div className="callBackno">
              <li>
                Customer Requested: <b>{WeekDatesData[activeDay]?.CR || 0}</b>
              </li>
              <li>
                Customer Agreed: <b>{WeekDatesData[activeDay]?.CA || 0}</b>
              </li>
              <li>
                Best Guess:{" "}
                <b>
                  {(WeekDatesData[activeDay]?.S || 0) +
                    (WeekDatesData[activeDay]?.AA || 0)}
                </b>
              </li>
              {(() => {
                const currentDayName = dayNames[activeDate.getDay()];
                const recommendedHours = RecommendedCallTimes[currentDayName]?.filter(rec => 
                  rec.Recommendation === "Recommended" || rec.Recommendation === "Highly Recommended"
                ).map(rec => rec.HourOfDay) || [];
                
                if (recommendedHours.length > 0) {
                  const formattedHours = recommendedHours.map(hour => {
                    const displayHour = hour;
                    return `${displayHour}-${displayHour + 1}`;
                  }).join(', ');
                  
                  return (
                    <li className="recommended-times-info">
                      <span className="recommended-label">Preferred Hrs : </span> <b>{formattedHours}</b>
                    </li>
                  );
                }
                return null;
              })()}
            </div>
          </div>
          <div
            className="timepickerbox"
            style={{ overflowY: "auto", height: "69vh", overflowX: "auto" }}
          >
            <ul>
              {hours.map((x, kx) => {
                const currentDayName = dayNames[activeDate.getDay()];
                const isRecommended = isRecommendedTime(x, currentDayName);
                
                return (
                <ul key={kx} className={isRecommended ? "recommended-time" : ""}>
                  {TimeArray.map((y, ky) => {
                    const index = kx * CellsInRow + ky;
                    const activeEvent = ActiveDateEvents[index];
                    const isBlocked = blocked[index] || leaveblocked[index];
                    
                    const classes = [
                      kx + 1 <= hours.length / 2 ? "v1" : "v2",
                      ky + 1 <= TimeArray.length / 2 ? "h1" : "h2",
                      activeEvent?.Color || "",
                      activeEvent?.Id === ActiveDateEvents[index - 1]?.Id
                        ? "bl"
                        : "",
                      activeEvent?.Id === ActiveDateEvents[index + 1]?.Id
                        ? "br"
                        : "",
                      activeEvent?.CustomerId === ActiveLeadIdEvent?.CustomerId
                        ? "bt"
                        : "",
                      activeCell === index ? "activedate" : "",
                    ].join(" ");

                    return (
                      <li
                        key={ky}
                        className={[
                          classes,
                          isBlocked && !activeEvent ? "blocked" : "",
                          activeEvent ? "active-event" : "",
                        ].join(" ")}
                        onClick={() =>
                          !isBlocked
                            ? setCallBack(index, `${x}:${y}`, activeEvent?.IsGoogleInvite, isBlocked)
                            : null // Disable onclick for blocked cells
                        }
                        onMouseLeave={handleMouseLeave}
                        onMouseEnter={() => handleMouseEnter(kx, ky)}
                        data-inactive={isBlocked && !activeEvent ? "true" : "false"}
                      >
                        {activeEvent?.CallbackTypeId === 1 && (
                          <span className="custReq">Cust Rq</span>
                        )}
                        {activeEvent?.IsPaymentCallback === "Yes" &&
                          activeEvent?.Id !==
                            ActiveDateEvents[index - 1]?.Id && (
                            <span className="payment-arrow"></span>
                          )}
                        {`${x}:${y}`}
                        {activeEvent?.LeadStatus === "Prospect" &&
                          activeEvent?.Id !==
                            ActiveDateEvents[index - 1]?.Id && (
                            <span className="prospect-dot"></span>
                          )}
                        {cbHover === index &&
                          ActiveDateEvents &&
                          Array.isArray(ActiveDateEvents) &&
                          ActiveDateEvents.length > 0 &&
                          ActiveDateEvents[cbHover] && (
                            <div className="tootippopup">
                              {ActiveDateEvents[cbHover].highIntenttext && (
                                <h3>
                                  {ActiveDateEvents[cbHover].highIntenttext}
                                </h3>
                              )}

                              <div className={detailsBoxClass}>
                                <div className="DisplayFlex">
                                  <div className="infobox">
                                    Product Name:{" "}
                                    <strong>
                                      {ActiveDateEvents[cbHover].ProductName}
                                    </strong>
                                  </div>
                                  <div className="infobox">
                                    Lead Id:{" "}
                                    <strong>
                                      {ActiveDateEvents[cbHover].Id}
                                    </strong>
                                  </div>
                                </div>

                                <div className="infobox">
                                  Customer Name:{" "}
                                  <strong>
                                    {ActiveDateEvents[cbHover].Name}
                                  </strong>
                                </div>
                                <div className="infobox">
                                  Subject:{" "}
                                  <strong>
                                    {ActiveDateEvents[cbHover].Subject}
                                  </strong>
                                </div>
                                <div className="infobox">
                                  Payment Call:{" "}
                                  <strong>
                                    {
                                      ActiveDateEvents[cbHover]
                                        .IsPaymentCallback
                                    }
                                  </strong>
                                </div>
                                {/* Conditionally hide Lead Status based on ProductName */}
                                {ActiveDateEvents[cbHover].ProductName !==
                                  "TermLife" &&
                                  ActiveDateEvents[cbHover].ProductName !==
                                    "NewCar" &&
                                  ActiveDateEvents[cbHover].ProductName !==
                                    "Investments" && (
                                    <div className="infobox">
                                      Lead Status:{" "}
                                      <strong>
                                        {ActiveDateEvents[cbHover].LeadStatus}
                                      </strong>
                                    </div>
                                  )}
                                <div className="infobox">
                                  Callback Type:{" "}
                                  <strong>
                                    {ActiveDateEvents[cbHover].callBackType}
                                  </strong>
                                </div>
                                <div className="infobox">
                                  Start Time:{" "}
                                  <strong>
                                    {ActiveDateEvents[cbHover].StartDate}
                                  </strong>
                                </div>
                                <div className="infobox">
                                  Duration:{" "}
                                  <strong>
                                    {ActiveDateEvents[cbHover].EndDate} Minutes
                                  </strong>
                                </div>
                              </div>
                            </div>
                          )}
                      </li>
                    );
                  })}
                </ul>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  ) : (
    <div>Loading...</div>
  );
};

export default NewSVCalendar;
