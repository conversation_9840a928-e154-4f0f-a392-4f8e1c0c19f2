import { SV_CONFIG } from "../../appconfig";
import User from "../../services/user.service";
import { updateStateInReduxGenie } from "../../store/actions";

export const page = {
    PROCESS             : "PROCESS",
    SUBISSUE            : "SUBISSUE",
    SUBISSUEQUERY       : "SUBISSUEQUERY",
    HOWTOUSE            : "HOWTOUSE",
    FEEDBACK            : "FEEDBACK",
    REQUESTSTATUS       : "REQUESTSTATUS",
    PBCONNECT           : "PBCONNECT",
    FOS                 : "FOS",
    CREATETICKET        : "CREATETICKET",
    SEARCHEXISTINGTCKT  : "SEARCHEXISTINGTCKT",
    CURRENTLEADTCKT     : "CURRENTLEADTCKT",
    SEARCHRESULT        : "SEARCHRESULT"
}

export const subIssueQueries = [
    {
        ID  : 0,
        Name: "How to use?"
    },
    {   
        ID  : 1,
        Name: "Raise a concern/feedback"
    }
];

export const products = [
    { 'ProductID': 115,'Name': 'Investment' },
    { 'ProductID': 7, 'Name': 'Term' },
    { 'ProductID': 2, 'Name': 'Health' },
    { 'ProductID': 117, 'Name': 'Motor' }     
];

export const relatedToCustomer = [
    {
        ID  : 1,
        Name: "Show tickets for current lead",
        showPage: page.CURRENTLEADTCKT
    },
    {
        ID  : 2,
        Name: "Search for existing tickets",
        showPage: page.SEARCHEXISTINGTCKT
    },
    {
        ID  : 3,
        Name: "Create new ticket",
        showPage: page.CREATETICKET
    }
];

export const searchUsingDropdown = [
    {
        Type: 0,
        Name: "Booking ID",
        disabled: false
    }, 
    {
        Type: 1,
        Name: "Ticket ID",
        disabled: false
    }
]

export const ticketDetailsTab = {
    MYTICKET        : "My Tickets",
    ESCALATEDTICKET : "Escalated Tickets",
    NOTIFICATION    : "Notification"
}


export const issueCodeForCustConsent = [
    "CHANGEINPOLICYDETAILS", 
    "HELPINCANCELLATIONOFTHEPOLICY", 
    "HELPINFREELOOKCANCELLATION", 
    "WANTTOSURRENDERTHEPOLICY"
]


export const ISSUE_MESSAGES = {
    "NEEDPOLICYCOPY": "Please inform the customer that for any Softcopy/Hardcopy concerns, the softcopy serves as the valid reference. If the softcopy has not been received yet, you can share it immediately (once the policy is issued). These days, Insurers no longer provide Hardcopies, so you may reassure the customer accordingly.",
    
    "CALLBACKREQUEST": "You can schedule a CTC for the Relationship Manager through the Call Transfer feature. Note: Scheduling a CTC ensures faster handling compared to raising a ticket.",
    
    "POLICYSTATUSREQUIRED": "Please start using the BMS portal (accessible through Matrix) to access the available information and resolve customer queries. Only create a ticket if the required details are not available.",

    "NEEDPOLICYCOPYORPAYMENTRECEIPT": "Please inform the customer that for any Softcopy/Hardcopy concerns, the Softcopy serves as the valid reference. If the Softcopy has not been received yet, you can share it immediately (once the policy is Issued). Insurers no longer provide hard copies, so you may reassure the customer accordingly."

};


export function encodeToBase64(clearText) {
    // Convert the clear text to Base64
    const plainTextBytes = new TextEncoder().encode(clearText); // Encodes text as UTF-8
    return btoa(String.fromCharCode(...plainTextBytes)); // Converts bytes to Base64 string
}

export const resetAllStateInReduxGenie = () => {

    return function(dispatch) {
        dispatch(updateStateInReduxGenie({ 
            showPage: page.PROCESS, 
            selectedProcess: {}, 
            selectedSubIssue: {}, 
            selectedSubIssueQuery: {},
            selectedDataForExstCustTckt: {Type: 0, Name: "Booking ID" }
        }));
    }
}

export const showEscalation = () => {
    const escalation = SV_CONFIG['showGenieTicketEscalationAll'] ||
            (
                User.ProductList && User.ProductList.length > 0 && SV_CONFIG['showGenieTicketEscalationPrduct'] && User.ProductList.some(product => SV_CONFIG['showGenieTicketEscalationPrduct'].includes(product.ProductId))
            )
    return escalation;
}