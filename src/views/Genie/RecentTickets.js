import React, { useState } from "react";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { Box, Button, Card, CardContent, Collapse, Grid, IconButton, LinearProgress, Link, Tooltip, Typography } from "@mui/material";
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { useDispatch, useSelector } from "react-redux";
import { GenieCreateTicket, GetCustTicketDetailsbyTcktId, GetUserDetailsBytoken, markReadNotification, openTicketUrl } from "../../services/GenieService";
import User from "../../services/user.service";
import { enqueueSnackbar } from "notistack";
import { updateStateInReduxGenie } from "../../store/actions";
import dayjs from "dayjs";
import { CONFIG, SV_CONFIG } from "../../appconfig";
import { showEscalation, issueCodeForCustConsent } from "./GenieHelper";
import CloseIcon from '@mui/icons-material/Close';
import { localStorageCache } from "../../utils/utility";

const RecentTickets = (props) => {

    const { setOpenMyTicketPopup } = props;

    const dispatch = useDispatch();
    const [ expandedCard, setExpandedCard ] = useState("");
    const [ custTcktLastComment, setCustTcktLastComment ] = useState("");
    const recentTicket = useSelector(state => state.genie.recentTicket);
    const showRecentTickets = useSelector(state => state.genie.showRecentTickets);


    const toggleCard = (ticketDisplayID) => {
        setExpandedCard((prev) => (prev === ticketDisplayID ? "" : ticketDisplayID));
    };

    const fetchCustTicketComment = (ticketDisplayID) => {

        GetCustTicketDetailsbyTcktId(ticketDisplayID)
        .then((result) => {
            if(result && Object.keys(result.Data).length > 0) {
                const sortedComments = result.Data.Remarks.sort((a, b) => new Date(b.CreatedOn) - new Date(a.CreatedOn));
                    setCustTcktLastComment(Array.isArray(sortedComments) && sortedComments.length > 0 ? sortedComments[0].Body : "");
            } else {
                setCustTcktLastComment("");
            }
        }).catch(() => {
            setCustTcktLastComment("");
        }).finally(() => {
            toggleCard(ticketDisplayID);
        })
        
    }

    const openFeedBackTicket = (ticketId) => {
        var cid = btoa(User.UserId);
        window.open(SV_CONFIG["feedback"][SV_CONFIG["environment"]] + "/Landing.html#/matrix/LandingPage/" + cid + "/notification/" + ticketId, "_blank")
    }

    const OpenfeedBackurl = () => {
        var cid = btoa(User.UserId);
        var secretToken = window.localStorage.getItem('AsteriskToken');
        window.open(SV_CONFIG["feedback"][SV_CONFIG["environment"]] + "/Landing.html#/matrix/LandingPage/" + secretToken + "/matrix/" + cid, "_blank")
    }

    const createEscalatedTicket = (data) => {
        GetUserDetailsBytoken({ "token": "", "userId": User.UserId })
			.then((res) => {
				const request = {
					LeadID: data.BookingID ? data.BookingID : 0,
					CreatedBy: User.UserId,
					SourceID: 11,
					IssueID: 57,
					Title: "Service Escalation",
					Comments: data.ProductId === 117 
						? `${data.TicketDisplayID} has been escalated by the sales advisor. Please help resolve the customer's concern.`
						: `${data.TicketDisplayID} has been escalated by the sales advisor. This ${data.ProductName} booking is of ${data["APE"]} APE. Please help resolve the customer's concern.`,
                    RefTicketID: data.TicketDisplayID,
					FileURL: data.TicketUrl,
					FileName: data.TicketDisplayID,
					ProductID: data.ProductId,
					PayID: "",
					OrderID: ""
				}

				GenieCreateTicket(request)
                    .then((result) => {
                        if(result && result.Status > 0) {	
                            dispatch(updateStateInReduxGenie({ refreshMyTckt: true }));
                            enqueueSnackbar("Ticket Escalated Successfully.", {
                                variant: 'success',
                                autoHideDuration: 3000,
                            });
                        } else {
                            enqueueSnackbar("Ticket Escalation Failed! Please try again.", {
                                variant: 'error',
                                autoHideDuration: 3000,
                            });
                        }
                    }).catch((e) => {
                        enqueueSnackbar("Ticket Escalation Failed! Please try again.", {
                            variant: 'error',
                            autoHideDuration: 3000,
                        });
                    })
			}).catch((e) => {
				enqueueSnackbar("Ticket Escalation Failed! Please try again.", {
					variant: 'error',
					autoHideDuration: 3000,
				});
			})
    }

	const markNotificationAsRead = (notificationId) => {
		let MongoNotificationData = localStorageCache.readFromCache('MongoNotificationData') != null ? JSON.parse(localStorageCache.readFromCache('MongoNotificationData')) : [];
        if (MongoNotificationData && Array.isArray(MongoNotificationData)) {
            MongoNotificationData.forEach((item) => {
                if (item.id == notificationId) {
                    item.IsRead = true;
                }
            })
            localStorageCache.writeToCache("MongoNotificationData", JSON.stringify(MongoNotificationData), 5 * 60 * 60 * 1000);
        }

		markReadNotification(notificationId);
	}

    if(!showRecentTickets)
    {
        return (
            <Grid sx={{ flexGrow: 1 }} container spacing={2}>
                <Grid item xs={12} sm={12}>
                    <LinearProgress color="secondary"/>
                </Grid>
            </Grid>
        )
    }
    
    return (
        <Grid sx={{ flexGrow: 1 }} container spacing={2}>
            <Grid item xs={12} sm={12}>
                {recentTicket.length > 0 ?
                    recentTicket.map((ticket, index) => 
                        {                            
                            return (
                                <Card className={`Card ${ticket.showNotification ? 'notificationCard' : ''}`} key={index}>
                                    <CardContent>
                                        <Box className="DFlex">
                                            <Typography variant="h6" className={ticket.className}>
                                                {ticket.Title}
                                            </Typography>  
                                            {ticket.className === "CustomerRelated" && 
                                                <>
                                                    {showEscalation() && 
                                                    <>  
                                                        {ticket.escalationAllowed === 1 && 
                                                            <p
                                                                className="EscalteIssue"
                                                                onClick={() => createEscalatedTicket({
                                                                    "BookingID": ticket.BookingID,
                                                                    "TicketDisplayID": ticket.TicketDisplayID,
                                                                    "IssueName": ticket.IssueSubIssueName,
                                                                    "TicketUrl": ticket.TicketURL,
                                                                    "ProductName": ticket.ProductName,
                                                                    "APE": ticket["APE"],
                                                                    "ProductId": ticket.ProductId
                                                                })}
                                                            >
                                                                Escalate issue
                                                            </p>
                                                        }
                                                        {
                                                            ticket.escalationAllowed === 2 && 
                                                            <Tooltip
                                                                title="Your ticket has been escalated"
                                                                placement="top-start"
                                                                PopperProps={{
                                                                    className: "custom-tooltip", 
                                                                }}
                                                            >
                                                                <div className="EscalteBtn">
                                                                    Escalated             
                                                                </div>
                                                            </Tooltip>
                                                        }
                                                        {
                                                            ticket.escalationAllowed === 3 &&
                                                            <Tooltip
                                                                title={ticket.escalationNotAllowedReason}
                                                                placement="top-start"
                                                                PopperProps={{
                                                                    className: "custom-tooltip", 
                                                                }}
                                                            >
                                                                <div className="EscalteBtn">
                                                                    <img src={CONFIG.PUBLIC_URL + "/images/Genie/emergency_home.svg"} alt=" " />   Escalate              
                                                                </div>
                                                            </Tooltip>
                                                        }
                                                        <IconButton onClick={() => fetchCustTicketComment(ticket.TicketDisplayID)}>
                                                            {expandedCard === ticket.TicketDisplayID ? <ExpandLess /> : <ExpandMore />}
                                                        </IconButton>
                                                    </>
                                                    }
                                                </>
                                            }
                                            {ticket.className !== "CustomerRelated" &&
                                                <IconButton onClick={() => toggleCard(ticket.TicketDisplayID)}>
                                                    {expandedCard === ticket.TicketDisplayID ? <ExpandLess /> : <ExpandMore />}
                                                </IconButton>
                                            }
                                        </Box>
                                        {ticket.showNotification && 
											<Box className='notification-ticket'>
												{ticket.notificationText}
												<CloseIcon 
													onClick={() => markNotificationAsRead(ticket.notificationId)}
												/>
											</Box>
                                        }
                                        {ticket.className === "CustomerRelated" && ticket.ParentTicketDetailsID &&
                                            <Box className="DFlex" mb={1}>
                                                <Typography variant="body2">
                                                    <strong>Parent Ticket ID</strong>
                                                </Typography>
                                                <Typography variant="body2">
                                                    <Link 
                                                        className="rcntTicket" 
                                                        href="#" 
                                                        target="_blank"
                                                        onClick={(e) => {
                                                            e.preventDefault();
                                                            openTicketUrl(ticket.ParentTicketDetailsID, ticket.ParentPrevObjectID);
                                                        }}
                                                    >
                                                        {ticket.ParentTicketDetailsID}
                                                    </Link>
                                                </Typography>
                                            </Box>
                                        }
                                        <Box className="DFlex" mb={1}>
                                            <Typography variant="body2">
                                                <strong>Ticket ID</strong>
                                            </Typography>
                                            <Typography variant="body2">
                                                <Link 
                                                    className="rcntTicket" 
                                                    href="#" 
                                                    target="_blank"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        ticket.className === "CustomerRelated" ? 
                                                            window.open(ticket.TicketURL, "_blank")
                                                            : openFeedBackTicket(ticket.TicketID);
                                                    }}
                                                >
                                                    {ticket.TicketDisplayID}
                                                </Link>
                                            </Typography>
                                        </Box>
                                        {ticket.StatusName && 
                                            <Box className="DFlex" mb={1}>
                                                <Typography variant="body2">
                                                    <strong>Status</strong>
                                                </Typography>
                                                <Typography variant="body2" className="LastUpdatedOn">
                                                    {ticket.className === "CustomerRelated" && ticket.ProductId == 7 && ticket.StatusName === "New" && issueCodeForCustConsent.includes(ticket.IssueSubIssueName.replace(/\s/g, '').toUpperCase()) 
                                                        ? "New - Waiting for customer Confirmation " 
                                                        : ticket.StatusName} 
                                                        (Last updated on {dayjs(ticket.UpdatedOn).format("DD/MM/YY HH:mm")})
                                                </Typography>
                                            </Box>
                                        }
                                        {!ticket.ParentTicketDetailsID && ticket.CreatedOn && 
                                            <Box className="DFlex" mb={1}>
                                                <Typography variant="body2">
                                                    <strong>Created on</strong>
                                                </Typography>
                                                <Typography variant="body2">{dayjs(ticket.CreatedOn).format("DD/MM/YY")}</Typography>
                                            </Box>
                                        }

                                      

                                        <Collapse in={ticket.className === "CustomerRelated" && expandedCard === ticket.TicketDisplayID} timeout="auto" unmountOnExit>
                                            {ticket.ParentTicketDetailsID && ticket.CreatedOn && 
                                                <Box className="DFlex" mb={1}>
                                                    <Typography variant="body2">
                                                        <strong>Created on</strong>
                                                    </Typography>
                                                    <Typography variant="body2">{dayjs(ticket.CreatedOn).format("DD/MM/YY")}</Typography>
                                                </Box>
                                            }
                                            {ticket.BookingID &&
                                                <Box className="DFlex" mb={1}>
                                                    <Typography variant="body2">
                                                        <strong>Booking ID</strong>
                                                    </Typography>
                                                    <Typography variant="body2">{ticket.BookingID}</Typography>
                                                </Box>
                                            }
                                            {ticket.IssueSubIssueName && 
                                                <>
                                                    <Box mb={1}>
                                                        <Typography variant="body2">
                                                            <strong>Issue</strong>
                                                        </Typography>
                                                    </Box>
                                                    <Typography variant="body2">
                                                        {ticket.IssueSubIssueName}
                                                    </Typography>
                                                </>
                                            }
                                            {custTcktLastComment &&
                                                <>
                                                    <Box mb={1}>
                                                        <Typography variant="body2">
                                                            <strong>Last comment</strong>
                                                        </Typography>
                                                    </Box>
                                                    <Typography variant="body2" title={custTcktLastComment} className="textEllipis">
                                                       {custTcktLastComment}
                                                    </Typography>
                                                </>
                                            }
                                        </Collapse>

                                        <Collapse in={ticket.className !== "CustomerRelated" && expandedCard === ticket.TicketDisplayID} timeout="auto" unmountOnExit>
                                            <Box mb={1}>
                                                <Typography variant="body2">
                                                    <strong>Last comment</strong>
                                                </Typography>
                                            </Box>
                                            <Typography variant="body2" title={ticket.LastComment ? ticket.LastComment : "N/A"} className="textEllipis">
                                                {ticket.LastComment ? ticket.LastComment : "N/A"} 
                                            </Typography>
                                        </Collapse>
                                        
                                    </CardContent>
                                        {ticket.ConfirmationPending && 
                                            <p className="confirmationPending">Resolved (Customer Confirmation Pending)</p>
                                        }
                                </Card>
                            )
                        }
                    )
                    :
                    <p>No recent ticket available</p>
                }
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
                <Button
                    target="_blank"
                    className="LodeMoreBtn"
                    onClick={(e) => {
                        e.preventDefault();
                        setOpenMyTicketPopup(true);
                    }}
                >
                    Load More Customer Tickets <OpenInNewIcon />
                </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
                <Button
                    target="_blank"
                    className="LodeMoreBtn"
                    onClick={(e) => {
                        e.preventDefault();
                        OpenfeedBackurl();
                    }}
                >
                    Load More Self Tickets<OpenInNewIcon />
                </Button>
            </Grid>
        </Grid>
    )
}

export default RecentTickets;