import { Box, LinearProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import Header from "./Header";
import { GenieGetAllIssueSubIssue, GenieProcessMasterService } from "../../services/GenieService";
import GenieProcess from "./Process";
import SubIssue from "./SubIssue";
import SubIssueQuery from "./SubIssueQuery";
import Feedback from "./Feedback";
import { page } from "./GenieHelper";
import { updateStateInReduxGenie } from "../../store/actions/Genie/Genie";
import { useDispatch, useSelector } from "react-redux";
import RequestStatus from "./RequestStatus";
import User from "../../services/user.service";
import HowToUse from "./HowToUse";
import CreateCustTicket from "./CreateCustTicket";
import SearchCustExstTicket from "./SearchCustExstTicket";
import SearchResult from "./SearchResult";
import CurrentLeadCustTckt from "./CurrentLeadCustTckt";
import PbConnectProcess from "./PbConnectProcess";

const GenieMain = (props) => {
    const dispatch = useDispatch();

    const { setOpenGenie, showGenieOpenView, setShowGenieOpenView  } = props;
    const [ showLoading, setShowLoading ] = useState(true);

    const showPage = useSelector(state => state.genie.showPage);

    useEffect(() => {
        try{
            GenieProcessMasterService().then((result) => {
                if(result && Array.isArray(result) && result.length > 0) {
                    result.push({ SourceID: "PBConnect", Name: "PBConnect" })

                    dispatch(updateStateInReduxGenie({ relatedToMeData: result }));

                    GenieGetAllIssueSubIssue().then((subIssueList) => {
                        if(subIssueList && Array.isArray(subIssueList) && subIssueList.length > 0) {

                            if( User.ProductList && User.ProductList.length > 0 && !User.ProductList.some(product => [7,115].includes(product.ProductId))) {
                                subIssueList = subIssueList.filter((item) => item.IssueID != 42);
                            }

                            if( User.ProductList && User.ProductList.length > 0 && !User.ProductList.some(product => product.ProductId === 7)) {
                                subIssueList = subIssueList.filter((item) => item.IssueID != 60);
                            }

                            const ProcessNdSubIssueMap = {};
                            result.forEach((process) => 
                                ProcessNdSubIssueMap[process.SourceID] = subIssueList.filter((issue) => issue.IssueID != 41 && issue.SourceID == process.SourceID)
                            )           
                            dispatch(updateStateInReduxGenie({ subIssueData : ProcessNdSubIssueMap }));
                        } else {
                            dispatch(updateStateInReduxGenie({ subIssueData: {} }));
                        }
                    }).catch((e) => {
                        dispatch(updateStateInReduxGenie({ subIssueData: {} }));
                    });
                } else {
                    dispatch(updateStateInReduxGenie({ relatedToMeData: [] }));
                }
            }).catch((e) => {
                dispatch(updateStateInReduxGenie({ relatedToMeData: [] }));
            }).finally(() => {
                setShowLoading(false);
            })
        }
        catch{
            dispatch(updateStateInReduxGenie({ relatedToMeData: [] }));
        } 
    },[]);

    if(showLoading) {
        return (
            <Box className="genieOpen">
                <Header setOpenGenie={setOpenGenie} />
                <LinearProgress />
            </Box>
        )
    }

    return (
        <Box className={showGenieOpenView ? "genieOpen" : "genieHide"}>

            <Header setOpenGenie={setOpenGenie} setShowGenieOpenView={setShowGenieOpenView} />

            {showPage === page.PROCESS && 
                <GenieProcess /> 
            }  
            
            {showPage === page.SUBISSUE &&
                <SubIssue />
            }

            {showPage === page.SUBISSUEQUERY &&
                <SubIssueQuery />
            }

            {showPage === page.HOWTOUSE &&
                <HowToUse />
            }

            {showPage === page.FEEDBACK &&
                <Feedback />
            }

            {showPage === page.REQUESTSTATUS &&
                <RequestStatus />
            }

            {showPage === page.PBCONNECT && 
                <PbConnectProcess />
            }

            {showPage === page.CREATETICKET &&
                <CreateCustTicket />
            }

            {showPage === page.SEARCHEXISTINGTCKT &&
                <SearchCustExstTicket />    
            }

            {showPage === page.SEARCHRESULT &&
                <SearchResult />
            }

            {showPage === page.CURRENTLEADTCKT &&
                <CurrentLeadCustTckt />    
            }
            
        </Box>
    );
};

export default GenieMain;
