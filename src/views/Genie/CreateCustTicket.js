import React, { useRef, useState } from "react";
import { <PERSON><PERSON><PERSON>, Box, Grid, Link, Button, TextareaAutosize, FormControl, Select, MenuItem, TextField, Checkbox, Alert } from "@mui/material";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import SendIcon from "@mui/icons-material/Send";
import { enqueueSnackbar } from "notistack";
import { GenieCreateCustTicket, GetIssueTypeByLeadId } from "../../services/GenieService";
import User from "../../services/user.service";
import { useDispatch, useSelector } from "react-redux";
import { updateStateInReduxGenie } from "../../store/actions";
import { page, issueCodeForCustConsent, ISSUE_MESSAGES } from "./GenieHelper";
import { isCreateTicketAllowed } from "../SV/RightBar/Tickets";

const CreateCustTicket = () => {

	const [	dataToSend, setDataToSend ] = useState({});
	const [ showLeadPage, setShowLeadPage ] = useState(true);
	const [ issueData, setIssueData ] = useState([]);
	const [fileAttachments, setFileAttachments] = useState([]);
	const [consentChecked, setConsentChecked] = useState(false);
	const ParentLeadId = useSelector((state) => state.salesview.parentLeadId);
	const dispatch = useDispatch();
	const inputRef = useRef(null);

	const selectedIssue = issueData.find(issue => issue.IssueTypeId === dataToSend.Status);
	const issueNameKey = selectedIssue ? selectedIssue.IssueName.replace(/\s/g, '').toUpperCase() : '';
	
	const requiresConsent = selectedIssue && (
		ISSUE_MESSAGES[issueNameKey] || 
		(selectedIssue.ProductID == 7 && issueCodeForCustConsent.includes(issueNameKey))
	);
	const askForConsent = requiresConsent && !consentChecked;

	const showNextPage = () => {
		if(!dataToSend.LeadId) {
			enqueueSnackbar("Please enter Lead ID.", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}

		GetIssueTypeByLeadId(dataToSend.LeadId).then(async (result) => {
			try{
				const res = JSON.parse(result.data);
				if(Array.isArray(res.Data.Issues) && res.Data.Issues.length > 0) {
					const _IsCreateTicketAllowed = await isCreateTicketAllowed(ParentLeadId);
					if (_IsCreateTicketAllowed.isAllowed) {
						setIssueData(res.Data.Issues);
						setShowLeadPage(false);
					}
					else {
						setIssueData([]);
						enqueueSnackbar(_IsCreateTicketAllowed.message, { 
							variant: 'error', 
							autoHideDuration: 3000 
						});
					}
				} else {
					setIssueData([]);
					enqueueSnackbar("No Data Found", {
						variant: 'error',
						autoHideDuration: 3000,
					});
				}
			} catch (err) {
				setIssueData([]);
				enqueueSnackbar(result.data, {
						variant: 'error',
						autoHideDuration: 3000,
				});
			}
		}).catch(() => {
			setIssueData([]);
			enqueueSnackbar("Something went wrong.", {
				variant: 'error',
				autoHideDuration: 3000,
			});
		})
	}

	const fileChanged = (event) => {
        const files = event.target.files;
        const fileData = [];
        let processedFiles = 0;

        if (files.length === 0) return;

        Array.from(files).forEach((file, index) => {
            const reader = new FileReader();
            const fileDetails = {
                FileName: file.name,
                AttachemntContent: "",
                AttachmentURL: "",
                ContentType: file.type,
            };

            reader.onload = (fileLoadedEvent) => {
                if (fileLoadedEvent.target.readyState === FileReader.DONE) {
                    fileDetails.AttachemntContent = btoa(fileLoadedEvent.target.result);
                    fileData[index] = fileDetails;
                    processedFiles++;

                    if (processedFiles === files.length) {
                        setFileAttachments(fileData);
                    }
                }
            };
			
            reader.readAsBinaryString(file);
        });
    };

	const GenieCreateCustTicketService = (request) => {
		GenieCreateCustTicket(request)
			.then((result) => {
				if(result && Object.keys(result.Data).length > 0) {	
					dispatch(updateStateInReduxGenie({ 
						ticketId: result.Data.TicketDetailsId, 
						reqStatusTicketTitle: {Title: "Customer Related Concerns", className: "CustomerRelated"}, 
						showPage: page.REQUESTSTATUS,
						refreshMyTckt: true 
					}));
				} else {
					enqueueSnackbar("Ticket Creation Failed! Please try again.", {
						variant: 'error',
						autoHideDuration: 3000,
					});
				}
			}).catch((e) => {
				enqueueSnackbar("Ticket Creation Failed! Please try again.", {
					variant: 'error',
					autoHideDuration: 3000,
				});
			})
	}

	const createCustTicketService = () => {

		if(!dataToSend.LeadId) {
			enqueueSnackbar("Please Add LeadId", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}

		if(!dataToSend.Status) {
			enqueueSnackbar("Please select Status", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}

		if(!dataToSend.SubStatus) {
			enqueueSnackbar("Please select SubStatus", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}


		if(!dataToSend.Comment) {
			enqueueSnackbar("Please add Remark", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}

		if(dataToSend.Comment.length <= 10) {
			enqueueSnackbar("Remark should be more than 10 char", {
				variant: 'error',
				autoHideDuration: 3000,
			});
			return;
		}

		const request = {
			Source: "Matrix",
			LeadID: dataToSend.LeadId ? parseInt(dataToSend.LeadId) : 0,
			CreatedBy: User.UserId,
			Issue: dataToSend.Status ? dataToSend.Status : 0,
			SubIssueId: dataToSend.SubStatus ? dataToSend.SubStatus : 0,
			Comments: dataToSend.Comment,
			CustomerID: 0,
			Attachments: [],
			IsAttachment: false
		}

		if(fileAttachments.length > 0) {
			request.Attachments[0] = {
										FileName: fileAttachments[0].FileName,
										AttachemntContent: fileAttachments[0].AttachemntContent
									};
			request.IsAttachment = true;
		}
	
		GenieCreateCustTicketService(request);
	}

	return (
		<Box mb={2} mt={2} className="ptTop FeedBack">
			{showLeadPage ? 
			<>
				<Typography variant="h6" >
					Please enter the Booking ID you want to enquire about
				</Typography>
				<FormControl fullWidth>
					<label>Booking ID</label>
					<TextField
						value={dataToSend.LeadId || ""}
						placeholder="Type here..."
						fullWidth
						variant="outlined"
						mb={3}
						onChange={(e) => {
							let numericValue = e.target.value;

							if (numericValue == "") {
								numericValue = null;
							}
							if (numericValue !== 0) {
								numericValue = e.target.value.replace(/^0+/, "").replace(/\D/g, "");
							}
							setDataToSend((prev) => ({...prev, LeadId: numericValue}))
						}}
					/>
				</FormControl>
				
					<div className="gotoHome textRight">
						<Button
							variant="contained"
							className="SubmitBtn"
							fullWidth
							onClick={showNextPage}
						>
							Next
						</Button>
					</div>
				
			</>
			:
			<>
				<Typography variant="h6" >
					Create new ticket for lead ID <span> {dataToSend.LeadId} </span>
				</Typography>

				<FormControl fullWidth>
					<label>Status</label>
					<Select
						labelId="demo-simple-select-label"
						id="demo-simple-select"
						value={dataToSend.Status || 0}
						onChange={(e) => {
							setDataToSend((prev) => ({ ...prev, Status: e.target.value }));
							setConsentChecked(false);
						}}
					>
						<MenuItem disabled value={0}>Select</MenuItem>
						{issueData.filter((status) => status.ParentID == 0).map((item) => (
							<MenuItem
								key={item.IssueTypeId}
								value={item.IssueTypeId}
							>
								{item.IssueName}
							</MenuItem>
						))}
					</Select>
				</FormControl>

				{askForConsent ? (
					<Box mt={2} mb={2}>
						<Alert 
							severity="info" 
							sx={{ mb: 2 }}
							icon={
								<Checkbox
									checked={consentChecked}
									onChange={(e) => setConsentChecked(e.target.checked)}
									color="primary"
								/>
							}
						>
							{ISSUE_MESSAGES[issueNameKey] || (
								<>
									Please inform the customer that for any policy-related changes, they must send an email to <a href="mailto:<EMAIL>"><EMAIL></a> or raise a ticket directly through Myaccount. No changes can be processed without the customer's written consent.
								</>
							)}
						</Alert>
					</Box>
				) : (
					<>
						<FormControl fullWidth>
							<label>Sub Status</label>
							<Select
								labelId="demo-simple-select-label"
								id="demo-simple-select"
								value={dataToSend.SubStatus || 0}
								onChange={(e) => setDataToSend((prev) => ({ ...prev, SubStatus: e.target.value }))}
							>
								<MenuItem disabled value={0}>Select</MenuItem>
								{issueData.filter((substatus) => dataToSend.Status && substatus.ParentID == dataToSend.Status).map((item) => (
									<MenuItem
										key={item.IssueTypeId}
										value={item.IssueTypeId}
									>
										{item.IssueName}
									</MenuItem>
								))}
							</Select>
						</FormControl>

						<label>Remarks</label>
						<TextareaAutosize
							label="Remarks"
							placeholder="Type here..."
							multiline="true"
							rows={4}
							variant="outlined"
							onChange={(e) => setDataToSend((prev) => ({...prev, Comment: e.target.value}))}
						/>
					</>
				)}

				<Grid container spacing={3} alignItems="center" mt={1}>

					<Grid item xs={4} md={4} sm={4} textAlign="left">
						<Link
							href="#"
							className="cancelRequest"
							onClick={() => {
								setShowLeadPage(true);
							}}
						>
							Cancel request
						</Link>
					</Grid>


					{!askForConsent &&
					<>
						<Grid item xs={4} md={5} sm={4} textAlign="right">
							<Button
								variant="outlined"
								fullWidth
								startIcon={<AttachFileIcon />}
								className="AttechmentBtn"
								onClick={() => inputRef.current.click()}
							>
								<span className="fileName">{fileAttachments.length == 0 ? "Attachment" : fileAttachments[0]?.FileName}</span>
							</Button>
							<input 
								ref={inputRef}
								type="file"
								accept=".jpg,.png,.pdf,.xlsx,.doc"
								hidden
								onChange={fileChanged}
							/>
						</Grid>

						<Grid item xs={4} md={3} sm={4} textAlign="right">
							<Button
								variant="contained"
								className="SubmitBtn"
								fullWidth
								endIcon={<SendIcon />}
								onClick={() => createCustTicketService()}
							>
								Submit
							</Button>
						</Grid>
					</>}
				</Grid>
			</>}
		</Box>
	);
};

export default CreateCustTicket;
