import { CONFIG } from "../appconfig";
import { IsSourceCustomerWhatsapp, isNonProgressiveWfhCalling, IsProgressiveApp, IsAppointmentDomain } from "../helpers";
import { cryptoService } from "../utils/utility";

// Helper function to calculate tenure in years
const calculateTenureYears = (createdOn) => {
  if (!createdOn || (typeof createdOn === 'string' && !createdOn.trim())) {
    return 0;
  }  
  try {
    const joinDate = new Date(createdOn);
    const currentDate = new Date();
    
    if (isNaN(joinDate.getTime())) return 0;
    
    const timeDiff = currentDate - joinDate;
    const yearsDiff = timeDiff / (1000 * 60 * 60 * 24 * 365.25);
    
    return yearsDiff;
  } catch (error) {
    return 0;
  }
};

let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;


// const { default: STORAGE } = require("../store/storage")

class clsUser {
  constructor() {
    let _user = userService();
    Object.assign(this, _user);
  }
}


const userService = () => {
  let user = {};
  //   let userToken = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

  //  localStorage.setItem('User', userToken);

  let userToken = localStorage.getItem('User');

  // LeadView:  for leadview page only userId is known from url
  if (window.location.href && window.location.href.toLowerCase().indexOf('leadview') !== -1) {
    let routeParams = window.location.pathname.split('/');
    if (!routeParams) {
      return 0;
    }
    user = {}
    user.UserId = parseInt(atob(routeParams[reqIndex]).split('/')[4]);
    // user.GroupId = parseInt(user.GroupId);
    // user.RoleId = parseInt(user.RoleId);
    user.IsInbound = false;
    // user.CallType = user.CallType === "undefined" ? "" : user.CallType;
    // user.Payment = user.Payment || 0;
    user.UserGroupList = [];
    user.IsCreateLead = 0;
    user.IsProgressive = false;
    user.Asterisk_IP = user.Asterisk_IP || "";
    user.Asterisk_Url = user.Asterisk_Url || "";
    user.IsSOSAgent = user.IsSOSAgent || 0;
    user.ViewGrpCount = user.ViewGrpCount || 0;
    user.viewPrd = user.viewPrd || 0;
    user.IsWebphone = user.IsWebphone || false;
    user.IsWFH = user.IsWFH || false;
    user.DIDNo = user.DIDNo || "";
    user.CallingCompany = user.CallingCompany || "";
    user.PrdGroupList = user.GroupList || [];
    user.Token = user.Token || "";
    user.UserBand = user.UserBand || "";
    user.Grade = user.Grade || 0;
    user.ProductList = user.ProductList || [];
    user.UserBUMapping = user.UserBUMapping || "";
    user.GroupList = user.GroupList || [];
    user.GroupProcessId = user.GroupProcessId || 0;
    
    // calculate tenure from User CreatedOn
    user.TenureYears = calculateTenureYears(user.UCreatedOn || "");
    
    // user.settings = settingsService.settings()  // old sv sharedService.js
    // console.log(user);
  }
  else if (userToken) {
    // SalesView 
    let reqIndex = CONFIG.PUBLIC_URL ? 4 : 2;
    let IsInbound = window.location.pathname.split('/')[reqIndex] != "Inbound" ? false : true; //  /:encryptURI/Inbound

    user = JSON.parse(cryptoService.decode(userToken));
    user.UserId = parseInt(user.UserId);
    user.GroupId = parseInt(user.GroupId);
    user.RoleId = parseInt(user.RoleId);
    user.IsInbound = IsInbound;
    user.CallType = user.CallType === "undefined" ? "" : user.CallType;
    user.Payment = user.Payment || 0;
    user.UserGroupList = user.UserGroupList || [];
    user.IsCreateLead = user.IsCreateLead || 0;
    user.IsProgressive = user.IsProgressive || false;
    user.Asterisk_IP = user.Asterisk_IP || "asterisk1304.policybazaar.com";
    user.Asterisk_Url = user.Asterisk_Url || "asterisk1304.policybazaar.com";
    user.IsSOSAgent = user.IsSOSAgent || 0;
    user.ViewGrpCount = user.ViewGrpCount || 0;
    user.viewPrd = user.viewPrd || 0;
    user.IsWebphone = user.IsWebphone || false;
    user.IsWFH = user.IsWFH || false;
    user.DIDNo = user.DIDNo || "";
    user.CallingCompany = user.CallingCompany || "";
    user.PrdGroupList = user.GroupList || [];
    user.Token = user.Token || "";
    user.UserBand = user.UserBand || "";
    user.Grade = user.Grade || 0;
    user.ProductList = user.ProductList || [];
    user.UserBUMapping = user.UserBUMapping || "";
    user.GroupList = user.GroupList || [];
    user.GroupProcessId = user.GroupProcessId || 0;
    
    //calculate tenure from UCreatedOn
    user.TenureYears = calculateTenureYears(user.UCreatedOn || "");
    
    // user.settings = settingsService.settings()  // old sv sharedService.js
    // console.log(user);
  }
  else {
    if (
      window.location.href.indexOf('localhost') === -1
      && window.location.href.indexOf('pgv/newsv/auth') === -1
      && window.location.href.indexOf(CONFIG.PUBLIC_URL + '/auth') === -1
      && window.location.href.indexOf(CONFIG.PUBLIC_URL + '/version') === -1
      && IsSourceCustomerWhatsapp() === false
    ) {
      if (IsAppointmentDomain()) {
        if (window.location.href.indexOf('not-found') === -1) {
          window.location.href = "/not-found";
        }
      }
      else {
        window.location.href = "/PGV/Login.aspx"
      }
    }
  }

  if (user.RoleId !== 13) {
    user.IsProgressive = false;
  }

  if (isNonProgressiveWfhCalling()) {
    // consider agent non-progressive if opened from app
    let _IsProgressiveApp = IsProgressiveApp();
    user.IsProgressive = _IsProgressiveApp
    user.IsWFH = true;
    user.CallingCompany = "WFH"
    user.IsOneLead = true;
  }

  return user;

}

//export default new User();
const User = new clsUser();
export default User;
