// import * as appConfig from './../appconfig/app.config';
// import * as services from "../services"

const DEFAULT_CACHE_TIME = 24 * 60 * 60 * 1000;

export const getTotalPages = (count, pageSize) => {
  if (count != undefined && typeof count === 'number') {
    return Math.ceil(count / pageSize);
  } else {
    return 1;
  }

};
export const getQueryParam = (key) => {
  let params = (new URL(document.location)).searchParams;
  return params.get(key);
}
// export const getQueryParams = (params) => {
//   const str = [];
//   for (const p in params) {
//     if (params.hasOwnProperty(p)) {
//       str.push(`${encodeURIComponent(p)}=${encodeURIComponent(params[p])}`);
//     }
//   }
//   return str.join('&');
// };
export const debounce = (fn, delay) => {
  let timerId = null;
  return function (...args) {
    if (timerId) {
      clearTimeout(timerId);
    }
    timerId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
};

export const cryptoService = {
  encode: (str) => {
    // first we use encodeURIComponent to get percent-encoded UTF-8,
    // then we convert the percent encodings into raw bytes which
    // can be fed into btoa.
    return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g,
      function toSolidBytes(match, p1) {
        return String.fromCharCode('0x' + p1);
      }));
  },
  decode: (str) => {
    // Going backwards: from bytestream, to percent-encoding, to original string.
    return decodeURIComponent(atob(str).split('').map(function (c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  }
}

export const JsonToNormalDate = (x) => {
  if (x === null) return null;
  let m = x.match(/\d+/);
  if (m) return new Date(parseInt(m));
  else return null;
}
export const ConvertToJSONDate = (date) => {
  var dt = new Date(date);
  var newDate = new Date(Date.UTC(dt.getFullYear(), dt.getMonth(), dt.getDate(), dt.getHours(), dt.getMinutes(), dt.getSeconds(), dt.getMilliseconds()));
  return '/Date(' + newDate.getTime() + ')/';
}
export const ToMinutesSeconds = (TalkTime) => {
  if (TalkTime === undefined)
    return "";
  else
    return TalkTime > 60 ? (Math.floor(TalkTime / 60) + ' mins ' + TalkTime % 60 + ' sec') : TalkTime + ' sec';
};

export const currency = (value, curr) => {
  if (curr === "INR") return (`₹ ${value}`)
  else return `$ ${value}`
}

export const localStorageCache = {
  writeToCache: (key, value, ttl_ms = DEFAULT_CACHE_TIME) => {
    let data = { value: value };
    try {
      data = { value: value, expires_at: new Date().getTime() + ttl_ms / 1 };
    } catch { }
    try {
      localStorage.setItem(key, JSON.stringify(data));
    }
    catch { }
  },
  readFromCache: (key) => {
    var data = null;
    try {
      data = JSON.parse(localStorage.getItem(key));
    } catch {
      data = null;
    }
    if (data !== null) {
      if (
        (data.expires_at !== null && data.expires_at < new Date().getTime()) // flush expired data
        || !((typeof data === 'object') && ("value" in data) && ("expires_at" in data)) // condition to handle previous data stored, in other format
      ) {
        localStorage.removeItem(key);
      } else {
        return data.value;
      }
    }
    return null;
  }
}


// export const localStorageCache = {
//   writeToCache: (key, data) => {
//     localStorage.setItem(key, JSON.stringify(data))
//   },
//   readFromCache: key => {
//     try {
//       return JSON.parse(localStorage.getItem(key)) || null;
//     }
//     catch{
//       return null;
//     }
//   }
// }