FROM 721537467949.dkr.ecr.ap-south-1.amazonaws.com/node-18-alpine as build-step
WORKDIR /app
COPY package*.json /app/
RUN npm install
#COPY . /app
#COPY . .
#RUN npm install
COPY ./ /app/
RUN npm run build

# production environment
FROM 721537467949.dkr.ecr.ap-south-1.amazonaws.com/nginx-1.28-alpine
RUN mkdir -p /usr/share/nginx/html/pgv/newsv
COPY --from=build-step /app/dist/ /usr/share/nginx/html/pgv/newsv
RUN rm /etc/nginx/conf.d/default.conf
#COPY ./nginx.conf /etc/nginx/conf.d
COPY --from=build-step /app/nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]

