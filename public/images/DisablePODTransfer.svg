<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35.999" height="35.999" viewBox="0 0 35.999 35.999">
  <defs>
    <filter id="Path_100191" x="4.967" y="12.165" width="26.055" height="23.835" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_100192" x="0" y="0" width="35.999" height="29.767" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_100193" x="4.554" y="4.619" width="26.89" height="19.517" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="1" result="blur-3"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="POD_Transfer" data-name="POD Transfer" transform="translate(-266.409 -260.359)">
    <g id="Group_89240" data-name="Group 89240" transform="translate(269.409 262.359)">
      <path id="Path_100202" data-name="Path 100202" d="M307.806,345.817l1.971,1.968,1.057-.913a2.8,2.8,0,0,1-.938-2.58,3.785,3.785,0,0,1,.766-1.66,2.037,2.037,0,0,1,3.141.082c1,1.111.919,2.609-.261,4.194a3.769,3.769,0,0,1,1.738,3.029,3.7,3.7,0,0,1-1.269,3.132,12.679,12.679,0,0,1-16.7-.035,3.912,3.912,0,0,1-.086-5.626c.185-.175.388-.325.641-.536a2.8,2.8,0,0,1-.944-2.583,3.743,3.743,0,0,1,.769-1.658,2.039,2.039,0,0,1,3.142.084c.991,1.1.911,2.561-.241,4.186l1.092.983c.316-.378.607-.778.945-1.118.321-.324.691-.585,1.113-.935a4.148,4.148,0,0,1-1.434-4.01,5.24,5.24,0,0,1,1.149-2.354,3.1,3.1,0,0,1,4.7.125C309.686,341.293,309.562,343.355,307.806,345.817Z" transform="translate(-290.179 -325.305)" fill="#bbc1c7"/>
      <g transform="matrix(1, 0, 0, 1, -3, -2)" filter="url(#Path_100191)">
        <path id="Path_100191-2" data-name="Path 100191" d="M308.322,345.858l2.058,1.979,1.1-.918a2.763,2.763,0,0,1-.98-2.594,3.745,3.745,0,0,1,.8-1.669,2.179,2.179,0,0,1,3.279.083c1.048,1.117.959,2.623-.273,4.217A3.766,3.766,0,0,1,316.124,350a3.65,3.65,0,0,1-1.325,3.149,13.62,13.62,0,0,1-17.433-.035,3.834,3.834,0,0,1-.089-5.657c.193-.176.4-.327.669-.539a2.757,2.757,0,0,1-.986-2.6,3.7,3.7,0,0,1,.8-1.667,2.181,2.181,0,0,1,3.281.085c1.035,1.109.951,2.575-.251,4.209l1.14.988c.33-.381.634-.783.987-1.125.335-.326.721-.588,1.162-.94a4.088,4.088,0,0,1-1.5-4.033,5.194,5.194,0,0,1,1.2-2.367,3.322,3.322,0,0,1,4.9.126C310.285,341.308,310.155,343.382,308.322,345.858Z" transform="translate(-288.11 -324.29)" fill="#bbc1c7"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -3, -2)" filter="url(#Path_100192)">
        <path id="Path_100192-2" data-name="Path 100192" d="M272.639,286.125c-4.048-4.512-4.913-13.32,1.041-19.328a15.2,15.2,0,0,1,21.265-.2c6.071,5.918,5.4,14.817,1.255,19.525-.163-.542-.356-1.065-.465-1.6a1.2,1.2,0,0,1,.126-.766,13.022,13.022,0,0,0-8.915-19.462,13.293,13.293,0,0,0-15.683,10.995,12.765,12.765,0,0,0,1.617,8.269,1.283,1.283,0,0,1,.1,1.343A7.007,7.007,0,0,0,272.639,286.125Z" transform="translate(-266.41 -260.36)" fill="#bbc1c7"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -3, -2)" filter="url(#Path_100193)">
        <path id="Path_100193-2" data-name="Path 100193" d="M314.545,300.672a3.141,3.141,0,0,1-1-2.29c-.11-5.246-3.822-9.524-8.466-9.907-4.816-.4-9.01,3.151-9.908,8.4a10.489,10.489,0,0,0-.116,1.481c-.034,1.253-.026,1.253-.978,2.228a11.747,11.747,0,0,1,3.867-11.081,9.788,9.788,0,0,1,11.943-.594A11.837,11.837,0,0,1,314.545,300.672Z" transform="translate(-286.3 -280.54)" fill="#bbc1c7"/>
      </g>
      <path id="Path_100199" data-name="Path 100199" d="M302.584,342.011a5.563,5.563,0,0,1,1.2-2.487,3.216,3.216,0,0,1,4.9.132C310.285,341.453,302.237,343.735,302.584,342.011Z" transform="translate(-291.106 -326.287)" fill="#bbc1c7" opacity="0.6"/>
      <path id="Path_100200" data-name="Path 100200" d="M302.58,340.57a3.2,3.2,0,0,1,.785-1.48,2.244,2.244,0,0,1,3.209.079C307.621,340.238,302.353,341.6,302.58,340.57Z" transform="translate(-296.678 -322.798)" fill="#bbc1c7" opacity="0.6"/>
      <path id="Path_100201" data-name="Path 100201" d="M302.58,340.57a3.22,3.22,0,0,1,.769-1.48,2.166,2.166,0,0,1,3.143.079C307.517,340.238,302.358,341.6,302.58,340.57Z" transform="translate(-283.106 -322.799)" fill="#bbc1c7" opacity="0.6"/>
    </g>
    <path id="Path_100203" data-name="Path 100203" d="M-7.179-.462a.654.654,0,0,0,.24-.045A.274.274,0,0,0-6.81-.672a1.238,1.238,0,0,0,.047-.4V-3.752q0-.392.023-.63l-.439.021V-4.9l1.662-.014a1.823,1.823,0,0,1,1.238.374A1.438,1.438,0,0,1-3.83-3.381a1.646,1.646,0,0,1-.187.763,1.5,1.5,0,0,1-.562.6,1.849,1.849,0,0,1-.913.252l-.228.007-.193-.007v.616q0,.4-.023.637a4.671,4.671,0,0,1,.5-.021V0H-7.179Zm1.528-1.813a.822.822,0,0,0,.694-.284,1.336,1.336,0,0,0,.22-.829q0-1.036-.784-1.036a.381.381,0,0,0-.3.105.725.725,0,0,0-.094.448V-2.3A1.565,1.565,0,0,0-5.651-2.275ZM-1.5.112A1.778,1.778,0,0,1-2.66-.259a2.14,2.14,0,0,1-.667-.952A3.606,3.606,0,0,1-3.538-2.45,3.246,3.246,0,0,1-3.3-3.661a2.356,2.356,0,0,1,.7-.969,1.711,1.711,0,0,1,1.121-.382A1.714,1.714,0,0,1-.35-4.63a2.285,2.285,0,0,1,.682.969A3.4,3.4,0,0,1,.554-2.45,3.237,3.237,0,0,1,.32-1.25a2.379,2.379,0,0,1-.7.977A1.7,1.7,0,0,1-1.5.112ZM-1.442-.4a.771.771,0,0,0,.62-.287,1.749,1.749,0,0,0,.345-.728,3.933,3.933,0,0,0,.105-.917A3.583,3.583,0,0,0-.664-3.913.93.93,0,0,0-1.53-4.5a.787.787,0,0,0-.632.294,1.767,1.767,0,0,0-.351.742,4.054,4.054,0,0,0-.105.931,4.2,4.2,0,0,0,.117.98,2.061,2.061,0,0,0,.383.822A.83.83,0,0,0-1.442-.4ZM.97-.462a.654.654,0,0,0,.24-.045.274.274,0,0,0,.129-.164,1.238,1.238,0,0,0,.047-.4V-3.752q0-.392.023-.63L.97-4.361V-4.9l1.7-.007a2.14,2.14,0,0,1,1.238.346,2.131,2.131,0,0,1,.755.9A2.8,2.8,0,0,1,4.915-2.5a3.063,3.063,0,0,1-.237,1.187,2.175,2.175,0,0,1-.749.942A2.133,2.133,0,0,1,2.644,0H.97Zm1.8-.049A.96.96,0,0,0,3.451-.77a1.547,1.547,0,0,0,.407-.675,2.993,2.993,0,0,0,.132-.907,2.741,2.741,0,0,0-.363-1.575A1.183,1.183,0,0,0,2.6-4.438a.325.325,0,0,0-.284.119.9.9,0,0,0-.085.476v2.7q0,.392-.023.63a.572.572,0,0,1,.111-.007l.252.007Q2.649-.511,2.767-.511Z" transform="translate(286.41 290.359)" fill="#898989" stroke="#898989" stroke-width="0.2"/>
  </g>
</svg>
