<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="70" height="48" viewBox="0 0 70 48">
  <defs>
    <linearGradient id="linear-gradient" x1="16.952" y1="-1.89" x2="18.124" y2="-1.89" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f6f6f6"/>
      <stop offset="0.527" stop-color="#f4f4f4"/>
      <stop offset="0.904" stop-color="#f4f4f4"/>
      <stop offset="1" stop-color="#f5f5f6"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="16.958" y1="-1.924" x2="18.131" y2="-1.924" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ededed"/>
      <stop offset="0.527" stop-color="#e9e9e9"/>
      <stop offset="0.904" stop-color="#e9e9e9"/>
      <stop offset="1" stop-color="#ececed"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="16.965" y1="-1.958" x2="18.137" y2="-1.958" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e4e4e5"/>
      <stop offset="0.527" stop-color="#dfdfdf"/>
      <stop offset="0.904" stop-color="#dfdfdf"/>
      <stop offset="1" stop-color="#e3e3e4"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="16.971" y1="-1.993" x2="18.144" y2="-1.993" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#dcdcdc"/>
      <stop offset="0.527" stop-color="#d4d4d4"/>
      <stop offset="0.904" stop-color="#d4d4d4"/>
      <stop offset="1" stop-color="#dadadb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="16.978" y1="-2.029" x2="18.15" y2="-2.029" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d3d3d3"/>
      <stop offset="0.527" stop-color="#c9c9ca"/>
      <stop offset="0.904" stop-color="#c9c9ca"/>
      <stop offset="1" stop-color="#d1d1d2"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="16.984" y1="-2.065" x2="18.157" y2="-2.065" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#cacacb"/>
      <stop offset="0.527" stop-color="#bebebf"/>
      <stop offset="0.904" stop-color="#bebebf"/>
      <stop offset="1" stop-color="#c8c8c9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="16.991" y1="-2.102" x2="18.163" y2="-2.102" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#c1c1c2"/>
      <stop offset="0.527" stop-color="#b4b4b4"/>
      <stop offset="0.904" stop-color="#b4b4b4"/>
      <stop offset="1" stop-color="#bebec0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="16.997" y1="-2.14" x2="18.17" y2="-2.14" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b9b9b9"/>
      <stop offset="0.527" stop-color="#a9a9aa"/>
      <stop offset="0.904" stop-color="#a9a9aa"/>
      <stop offset="1" stop-color="#b5b5b7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="17.004" y1="-2.179" x2="18.176" y2="-2.179" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b0b0b1"/>
      <stop offset="0.527" stop-color="#9e9e9f"/>
      <stop offset="0.904" stop-color="#9e9e9f"/>
      <stop offset="1" stop-color="#acacae"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="17.01" y1="-2.219" x2="18.183" y2="-2.219" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a7a7a8"/>
      <stop offset="0.527" stop-color="#949494"/>
      <stop offset="0.904" stop-color="#949494"/>
      <stop offset="1" stop-color="#a3a3a5"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="17.017" y1="-2.259" x2="18.189" y2="-2.259" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#9e9e9f"/>
      <stop offset="0.527" stop-color="#89898a"/>
      <stop offset="0.904" stop-color="#89898a"/>
      <stop offset="1" stop-color="#9a9a9c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="17.023" y1="-2.3" x2="18.196" y2="-2.3" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#969697"/>
      <stop offset="0.527" stop-color="#7e7e7f"/>
      <stop offset="0.904" stop-color="#7e7e7f"/>
      <stop offset="1" stop-color="#919192"/>
    </linearGradient>
    <linearGradient id="linear-gradient-13" x1="17.03" y1="-2.343" x2="18.202" y2="-2.343" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8d8d8e"/>
      <stop offset="0.527" stop-color="#737375"/>
      <stop offset="0.904" stop-color="#737375"/>
      <stop offset="1" stop-color="#888889"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="17.036" y1="-2.386" x2="18.209" y2="-2.386" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#848485"/>
      <stop offset="0.527" stop-color="#69696a"/>
      <stop offset="0.904" stop-color="#69696a"/>
      <stop offset="1" stop-color="#7f7f80"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="17.043" y1="-2.43" x2="18.215" y2="-2.43" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#7b7b7d"/>
      <stop offset="0.527" stop-color="#5e5e5f"/>
      <stop offset="0.904" stop-color="#5e5e5f"/>
      <stop offset="1" stop-color="#767677"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="17.049" y1="-2.475" x2="18.222" y2="-2.475" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#737374"/>
      <stop offset="0.527" stop-color="#535355"/>
      <stop offset="0.904" stop-color="#535355"/>
      <stop offset="1" stop-color="#6d6d6e"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="17.056" y1="-2.521" x2="18.228" y2="-2.521" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#6a6a6b"/>
      <stop offset="0.527" stop-color="#49494a"/>
      <stop offset="0.904" stop-color="#49494a"/>
      <stop offset="1" stop-color="#636365"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="17.062" y1="-2.569" x2="18.235" y2="-2.569" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#616163"/>
      <stop offset="0.527" stop-color="#3e3e3f"/>
      <stop offset="0.904" stop-color="#3e3e3f"/>
      <stop offset="1" stop-color="#5a5a5c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="17.069" y1="-2.617" x2="18.241" y2="-2.617" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#58585a"/>
      <stop offset="0.527" stop-color="#333335"/>
      <stop offset="0.904" stop-color="#333335"/>
      <stop offset="1" stop-color="#515153"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="17.075" y1="-2.667" x2="18.248" y2="-2.667" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#505051"/>
      <stop offset="0.527" stop-color="#28282a"/>
      <stop offset="0.904" stop-color="#28282a"/>
      <stop offset="1" stop-color="#48484a"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="17.082" y1="-2.718" x2="18.254" y2="-2.718" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#474749"/>
      <stop offset="0.527" stop-color="#1e1e20"/>
      <stop offset="0.904" stop-color="#1e1e20"/>
      <stop offset="1" stop-color="#3f3f41"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="17.088" y1="-2.77" x2="18.261" y2="-2.77" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3e3e40"/>
      <stop offset="0.527" stop-color="#131315"/>
      <stop offset="0.904" stop-color="#131315"/>
      <stop offset="1" stop-color="#363638"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="0.015" y1="0.392" x2="1.188" y2="0.392" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-24" x1="0.015" y1="0.39" x2="1.188" y2="0.39" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-25" x1="0.015" y1="0.388" x2="1.188" y2="0.388" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-26" x1="0.015" y1="0.387" x2="1.188" y2="0.387" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-27" x1="0.015" y1="0.385" x2="1.188" y2="0.385" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-28" x1="0.015" y1="0.383" x2="1.188" y2="0.383" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-29" x1="0.015" y1="0.381" x2="1.188" y2="0.381" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-30" x1="0.015" y1="0.379" x2="1.188" y2="0.379" xlink:href="#linear-gradient-8"/>
    <linearGradient id="linear-gradient-31" x1="0.015" y1="0.377" x2="1.188" y2="0.377" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-32" x1="0.015" y1="0.375" x2="1.188" y2="0.375" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-33" x1="0.015" y1="0.372" x2="1.188" y2="0.372" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-34" x1="0.015" y1="0.37" x2="1.188" y2="0.37" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-35" x1="0.015" y1="0.368" x2="1.188" y2="0.368" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-36" x1="0.015" y1="0.366" x2="1.188" y2="0.366" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-37" x1="0.015" y1="0.363" x2="1.188" y2="0.363" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-38" x1="0.015" y1="0.361" x2="1.188" y2="0.361" xlink:href="#linear-gradient-16"/>
    <linearGradient id="linear-gradient-39" x1="0.015" y1="0.359" x2="1.188" y2="0.359" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-40" x1="0.015" y1="0.356" x2="1.188" y2="0.356" xlink:href="#linear-gradient-18"/>
    <linearGradient id="linear-gradient-41" x1="0.015" y1="0.353" x2="1.188" y2="0.353" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-42" x1="0.015" y1="0.351" x2="1.188" y2="0.351" xlink:href="#linear-gradient-20"/>
    <linearGradient id="linear-gradient-43" x1="0.015" y1="0.348" x2="1.188" y2="0.348" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-44" x1="0.015" y1="0.345" x2="1.188" y2="0.345" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-45" x1="0.894" y1="0.553" x2="2.799" y2="0.809" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#080b0b"/>
      <stop offset="0.188" stop-color="#29292b"/>
      <stop offset="0.461" stop-color="#131713"/>
      <stop offset="0.588" stop-color="#131315"/>
      <stop offset="0.904" stop-color="#080b0b"/>
      <stop offset="1" stop-color="#363638"/>
    </linearGradient>
    <linearGradient id="linear-gradient-46" x1="-0.375" y1="0.604" x2="0.985" y2="0.366" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-47" x1="-0.368" y1="0.603" x2="0.985" y2="0.368" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-48" x1="-0.362" y1="0.603" x2="0.985" y2="0.369" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-49" x1="-0.355" y1="0.603" x2="0.985" y2="0.371" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-50" x1="-0.348" y1="0.602" x2="0.985" y2="0.372" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-51" x1="-0.341" y1="0.602" x2="0.985" y2="0.374" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-52" x1="-0.334" y1="0.602" x2="0.985" y2="0.375" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-53" x1="-0.327" y1="0.601" x2="0.986" y2="0.377" xlink:href="#linear-gradient-8"/>
    <linearGradient id="linear-gradient-54" x1="-0.32" y1="0.601" x2="0.986" y2="0.378" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-55" x1="-0.313" y1="0.6" x2="0.986" y2="0.38" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-56" x1="-0.305" y1="0.6" x2="0.986" y2="0.382" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-57" x1="-0.298" y1="0.599" x2="0.986" y2="0.383" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-58" x1="-0.29" y1="0.599" x2="0.986" y2="0.385" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-59" x1="-0.282" y1="0.598" x2="0.987" y2="0.386" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-60" x1="-0.274" y1="0.598" x2="0.987" y2="0.388" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-61" x1="-0.266" y1="0.597" x2="0.987" y2="0.389" xlink:href="#linear-gradient-16"/>
    <linearGradient id="linear-gradient-62" x1="-0.258" y1="0.597" x2="0.987" y2="0.391" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-63" x1="-0.249" y1="0.596" x2="0.987" y2="0.392" xlink:href="#linear-gradient-18"/>
    <linearGradient id="linear-gradient-64" x1="-0.241" y1="0.595" x2="0.987" y2="0.394" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-65" x1="-0.233" y1="0.594" x2="0.988" y2="0.395" xlink:href="#linear-gradient-20"/>
    <linearGradient id="linear-gradient-66" x1="-0.224" y1="0.594" x2="0.988" y2="0.397" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-67" x1="-0.216" y1="0.592" x2="0.988" y2="0.398" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-68" x1="0.795" y1="0.227" x2="-0.839" y2="0.711" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3e3e40"/>
      <stop offset="0.193" stop-color="#2a2a2c"/>
      <stop offset="0.423" stop-color="#19191b"/>
      <stop offset="0.594" stop-color="#131315"/>
      <stop offset="1" stop-color="#363638"/>
    </linearGradient>
    <linearGradient id="linear-gradient-69" x1="-0.129" y1="0.579" x2="0.952" y2="0.404" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3e3e40"/>
      <stop offset="0.244" stop-color="#2d2d2f"/>
      <stop offset="0.448" stop-color="#131315"/>
      <stop offset="0.791" stop-color="#3e3e40"/>
      <stop offset="1" stop-color="#363638"/>
    </linearGradient>
    <linearGradient id="linear-gradient-70" x1="32.849" y1="-0.514" x2="34.208" y2="-0.751" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-71" x1="33.106" y1="-0.533" x2="34.459" y2="-0.768" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-72" x1="33.367" y1="-0.552" x2="34.713" y2="-0.785" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-73" x1="33.632" y1="-0.571" x2="34.972" y2="-0.803" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-74" x1="33.901" y1="-0.59" x2="35.234" y2="-0.82" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-75" x1="34.174" y1="-0.61" x2="35.5" y2="-0.838" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-76" x1="34.451" y1="-0.63" x2="35.771" y2="-0.856" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-77" x1="34.733" y1="-0.649" x2="36.045" y2="-0.874" xlink:href="#linear-gradient-8"/>
    <linearGradient id="linear-gradient-78" x1="35.019" y1="-0.669" x2="36.324" y2="-0.892" xlink:href="#linear-gradient-9"/>
    <linearGradient id="linear-gradient-79" x1="35.309" y1="-0.69" x2="36.608" y2="-0.91" xlink:href="#linear-gradient-10"/>
    <linearGradient id="linear-gradient-80" x1="35.604" y1="-0.71" x2="36.895" y2="-0.928" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-81" x1="35.904" y1="-0.73" x2="37.188" y2="-0.946" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-82" x1="36.209" y1="-0.751" x2="37.485" y2="-0.965" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-83" x1="36.519" y1="-0.771" x2="37.787" y2="-0.983" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-84" x1="36.834" y1="-0.792" x2="38.094" y2="-1.002" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-85" x1="37.154" y1="-0.812" x2="38.407" y2="-1.02" xlink:href="#linear-gradient-16"/>
    <linearGradient id="linear-gradient-86" x1="37.479" y1="-0.833" x2="38.724" y2="-1.039" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-87" x1="37.81" y1="-0.854" x2="39.047" y2="-1.057" xlink:href="#linear-gradient-18"/>
    <linearGradient id="linear-gradient-88" x1="38.147" y1="-0.874" x2="39.375" y2="-1.076" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-89" x1="38.489" y1="-0.895" x2="39.709" y2="-1.094" xlink:href="#linear-gradient-20"/>
    <linearGradient id="linear-gradient-90" x1="38.837" y1="-0.915" x2="40.049" y2="-1.112" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-91" x1="39.191" y1="-0.935" x2="40.395" y2="-1.13" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-92" x1="93.229" y1="-7.614" x2="91.595" y2="-7.13" xlink:href="#linear-gradient-68"/>
    <linearGradient id="linear-gradient-93" x1="39.278" y1="-0.949" x2="40.359" y2="-1.124" xlink:href="#linear-gradient-69"/>
    <linearGradient id="linear-gradient-94" x1="0.334" y1="0.129" x2="0.856" y2="1.252" gradientUnits="objectBoundingBox">
      <stop offset="0.203" stop-color="#fde17e"/>
      <stop offset="0.495" stop-color="#ffc438"/>
      <stop offset="0.695" stop-color="#f8981d"/>
    </linearGradient>
    <linearGradient id="linear-gradient-95" x1="0.335" y1="0.13" x2="0.856" y2="1.253" xlink:href="#linear-gradient-94"/>
    <linearGradient id="linear-gradient-96" x1="0.336" y1="0.131" x2="0.857" y2="1.256" xlink:href="#linear-gradient-94"/>
    <linearGradient id="linear-gradient-97" x1="0.587" y1="0.663" x2="0.433" y2="0.182" gradientUnits="objectBoundingBox">
      <stop offset="0.1" stop-color="#fff" stop-opacity="0"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
  </defs>
  <g id="vip" transform="translate(-390.276 -69.13)">
    <g id="Group_7112" data-name="Group 7112" transform="translate(390.276 69.13)">
      <g id="Group_7101" data-name="Group 7101" transform="translate(0 0)">
        <g id="Group_7097" data-name="Group 7097" transform="translate(4.169 5.097)" opacity="0.5" style="mix-blend-mode: multiply;isolation: isolate">
          <path id="Path_9253" data-name="Path 9253" d="M148.1,255.117s-3.189,5.372-3.362,9.427c10.861,2.686,26.133-.536,30.825-1.728l-.307-.223C156.469,264.036,150.819,258.4,148.1,255.117Z" transform="translate(-142.635 -255.117)" opacity="0" fill="url(#linear-gradient)"/>
          <path id="Path_9254" data-name="Path 9254" d="M147.066,255.994s-3.044,5.269-3.217,9.324c10.861,2.686,26.133-.536,30.824-1.728l-.292-.213C155.593,264.821,149.784,259.274,147.066,255.994Z" transform="translate(-141.845 -255.895)" opacity="0.048" fill="url(#linear-gradient-2)"/>
          <path id="Path_9255" data-name="Path 9255" d="M146.03,256.871s-2.9,5.166-3.072,9.221c10.861,2.686,26.133-.536,30.825-1.728l-.278-.2C154.717,265.606,148.747,260.151,146.03,256.871Z" transform="translate(-141.054 -256.674)" opacity="0.095" fill="url(#linear-gradient-3)"/>
          <path id="Path_9256" data-name="Path 9256" d="M144.993,257.748s-2.753,5.063-2.926,9.118c10.861,2.686,26.133-.536,30.825-1.728l-.263-.191C153.84,266.39,147.711,261.028,144.993,257.748Z" transform="translate(-140.263 -257.452)" opacity="0.143" fill="url(#linear-gradient-4)"/>
          <path id="Path_9257" data-name="Path 9257" d="M143.958,258.624s-2.608,4.96-2.781,9.015c10.861,2.686,26.133-.536,30.825-1.728l-.249-.181C152.965,267.174,146.676,261.9,143.958,258.624Z" transform="translate(-139.474 -258.229)" opacity="0.19" fill="url(#linear-gradient-5)"/>
          <path id="Path_9258" data-name="Path 9258" d="M142.922,259.5s-2.462,4.857-2.636,8.912c10.861,2.686,26.133-.536,30.825-1.728l-.234-.17C152.088,267.96,145.639,262.782,142.922,259.5Z" transform="translate(-138.683 -259.009)" opacity="0.238" fill="url(#linear-gradient-6)"/>
          <path id="Path_9259" data-name="Path 9259" d="M141.886,260.378s-2.317,4.754-2.49,8.809c10.861,2.686,26.133-.536,30.824-1.728l-.22-.16C151.213,268.743,144.6,263.658,141.886,260.378Z" transform="translate(-137.893 -259.786)" opacity="0.286" fill="url(#linear-gradient-7)"/>
          <path id="Path_9260" data-name="Path 9260" d="M140.85,261.255a27.434,27.434,0,0,0-2.345,8.706c10.861,2.686,26.133-.536,30.825-1.728l-.205-.149C150.337,269.528,143.568,264.535,140.85,261.255Z" transform="translate(-137.102 -260.564)" opacity="0.333" fill="url(#linear-gradient-8)"/>
          <path id="Path_9261" data-name="Path 9261" d="M139.815,262.132a28.079,28.079,0,0,0-2.2,8.6c10.861,2.686,26.133-.536,30.824-1.728l-.19-.138C149.461,270.313,142.532,265.412,139.815,262.132Z" transform="translate(-136.312 -261.343)" opacity="0.381" fill="url(#linear-gradient-9)"/>
          <path id="Path_9262" data-name="Path 9262" d="M138.778,263.009a28.9,28.9,0,0,0-2.054,8.5c10.861,2.686,26.133-.536,30.824-1.728l-.176-.128C148.585,271.1,141.5,266.289,138.778,263.009Z" transform="translate(-135.522 -262.121)" opacity="0.429" fill="url(#linear-gradient-10)"/>
          <path id="Path_9263" data-name="Path 9263" d="M137.742,263.886a29.947,29.947,0,0,0-1.909,8.4c10.861,2.686,26.133-.536,30.825-1.728l-.161-.117C147.708,271.882,140.46,267.166,137.742,263.886Z" transform="translate(-134.731 -262.899)" opacity="0.476" fill="url(#linear-gradient-11)"/>
          <path id="Path_9264" data-name="Path 9264" d="M136.707,264.762a31.272,31.272,0,0,0-1.764,8.295c10.861,2.686,26.133-.536,30.825-1.728l-.147-.107C146.833,272.666,139.424,268.042,136.707,264.762Z" transform="translate(-133.941 -263.677)" opacity="0.524" fill="url(#linear-gradient-12)"/>
          <path id="Path_9265" data-name="Path 9265" d="M135.671,265.639a32.981,32.981,0,0,0-1.618,8.192c10.861,2.686,26.133-.536,30.825-1.728l-.132-.1C145.956,273.45,138.388,268.919,135.671,265.639Z" transform="translate(-133.15 -264.455)" opacity="0.571" fill="url(#linear-gradient-13)"/>
          <path id="Path_9266" data-name="Path 9266" d="M134.635,266.516a35.2,35.2,0,0,0-1.473,8.089c10.861,2.686,26.133-.536,30.825-1.728l-.118-.085C145.081,274.235,137.353,269.8,134.635,266.516Z" transform="translate(-132.36 -265.233)" opacity="0.619" fill="url(#linear-gradient-14)"/>
          <path id="Path_9267" data-name="Path 9267" d="M133.6,267.393a38.163,38.163,0,0,0-1.328,7.986c10.861,2.686,26.133-.536,30.825-1.728l-.1-.075C144.2,275.02,136.317,270.673,133.6,267.393Z" transform="translate(-131.57 -266.012)" opacity="0.667" fill="url(#linear-gradient-15)"/>
          <path id="Path_9268" data-name="Path 9268" d="M132.563,268.27a42.239,42.239,0,0,0-1.183,7.883c10.861,2.686,26.133-.536,30.825-1.728l-.088-.064C143.328,275.8,135.28,271.55,132.563,268.27Z" transform="translate(-130.779 -266.79)" opacity="0.714" fill="url(#linear-gradient-16)"/>
          <path id="Path_9269" data-name="Path 9269" d="M131.527,269.146a48.087,48.087,0,0,0-1.037,7.78c10.861,2.686,26.133-.536,30.825-1.728l-.074-.054C142.453,276.588,134.245,272.426,131.527,269.146Z" transform="translate(-129.989 -267.568)" opacity="0.762" fill="url(#linear-gradient-17)"/>
          <path id="Path_9270" data-name="Path 9270" d="M130.492,270.023a57,57,0,0,0-.892,7.677c10.861,2.686,26.133-.536,30.824-1.728l-.059-.043C141.577,277.373,133.21,273.3,130.492,270.023Z" transform="translate(-129.199 -268.346)" opacity="0.81" fill="url(#linear-gradient-18)"/>
          <path id="Path_9271" data-name="Path 9271" d="M129.456,270.9a72.07,72.07,0,0,0-.747,7.574c10.861,2.686,26.133-.536,30.825-1.728l-.045-.033C140.7,278.157,132.173,274.18,129.456,270.9Z" transform="translate(-128.408 -269.124)" opacity="0.857" fill="url(#linear-gradient-19)"/>
          <path id="Path_9272" data-name="Path 9272" d="M128.419,271.777s-.428,3.416-.6,7.471c10.861,2.686,26.133-.536,30.825-1.728l-.03-.022C139.824,278.942,131.137,275.057,128.419,271.777Z" transform="translate(-127.618 -269.903)" opacity="0.905" fill="url(#linear-gradient-20)"/>
          <path id="Path_9273" data-name="Path 9273" d="M127.384,272.654s-.283,3.313-.456,7.368c10.861,2.686,26.133-.536,30.825-1.728l-.016-.011C138.949,279.727,130.1,275.934,127.384,272.654Z" transform="translate(-126.828 -270.681)" opacity="0.952" fill="url(#linear-gradient-21)"/>
          <path id="Path_9274" data-name="Path 9274" d="M126.348,273.531s-.137,3.21-.311,7.265c10.861,2.686,26.133-.536,30.825-1.728h0C138.073,280.511,129.065,276.811,126.348,273.531Z" transform="translate(-126.037 -271.459)" fill="url(#linear-gradient-22)"/>
        </g>
        <g id="Group_7098" data-name="Group 7098" transform="translate(32.903 32.435)" opacity="0.5" style="mix-blend-mode: multiply;isolation: isolate">
          <path id="Path_9275" data-name="Path 9275" d="M408.757,513.574s3.189-5.372,3.362-9.427c-10.861-2.685-26.133.536-30.825,1.728l.307.223C400.39,504.654,406.04,510.293,408.757,513.574Z" transform="translate(-381.295 -503.106)" opacity="0" fill="url(#linear-gradient-23)"/>
          <path id="Path_9276" data-name="Path 9276" d="M409.793,513.509s3.043-5.269,3.217-9.324c-10.861-2.685-26.133.536-30.825,1.728l.293.213C401.266,504.682,407.075,510.229,409.793,513.509Z" transform="translate(-382.085 -503.14)" opacity="0.048" fill="url(#linear-gradient-24)"/>
          <path id="Path_9277" data-name="Path 9277" d="M410.829,513.444s2.9-5.166,3.072-9.221c-10.861-2.685-26.133.536-30.825,1.728l.278.2C402.142,504.71,408.111,510.164,410.829,513.444Z" transform="translate(-382.876 -503.174)" opacity="0.095" fill="url(#linear-gradient-25)"/>
          <path id="Path_9278" data-name="Path 9278" d="M411.865,513.38s2.753-5.063,2.926-9.118c-10.861-2.685-26.133.536-30.825,1.728l.263.191C403.018,504.737,409.148,510.1,411.865,513.38Z" transform="translate(-383.666 -503.208)" opacity="0.143" fill="url(#linear-gradient-26)"/>
          <path id="Path_9279" data-name="Path 9279" d="M412.9,513.315s2.608-4.96,2.781-9.015c-10.861-2.685-26.133.536-30.825,1.728l.249.181C403.894,504.765,410.183,510.035,412.9,513.315Z" transform="translate(-384.456 -503.242)" opacity="0.19" fill="url(#linear-gradient-27)"/>
          <path id="Path_9280" data-name="Path 9280" d="M413.937,513.25s2.462-4.857,2.636-8.912c-10.861-2.685-26.133.536-30.825,1.728l.234.17C404.77,504.792,411.219,509.97,413.937,513.25Z" transform="translate(-385.247 -503.275)" opacity="0.238" fill="url(#linear-gradient-28)"/>
          <path id="Path_9281" data-name="Path 9281" d="M414.972,513.185s2.317-4.754,2.49-8.809c-10.861-2.685-26.133.536-30.825,1.728l.22.159C405.646,504.819,412.254,509.9,414.972,513.185Z" transform="translate(-386.037 -503.309)" opacity="0.286" fill="url(#linear-gradient-29)"/>
          <path id="Path_9282" data-name="Path 9282" d="M416.009,513.12a27.432,27.432,0,0,0,2.345-8.706c-10.861-2.685-26.133.536-30.825,1.728l.205.149C406.522,504.847,413.291,509.84,416.009,513.12Z" transform="translate(-386.828 -503.343)" opacity="0.333" fill="url(#linear-gradient-30)"/>
          <path id="Path_9283" data-name="Path 9283" d="M417.044,513.055a28.079,28.079,0,0,0,2.2-8.6c-10.861-2.685-26.133.536-30.825,1.728l.19.138C407.4,504.874,414.326,509.775,417.044,513.055Z" transform="translate(-387.617 -503.377)" opacity="0.381" fill="url(#linear-gradient-31)"/>
          <path id="Path_9284" data-name="Path 9284" d="M418.08,512.99a28.9,28.9,0,0,0,2.054-8.5c-10.861-2.685-26.133.536-30.825,1.728l.176.128C408.274,504.9,415.362,509.71,418.08,512.99Z" transform="translate(-388.408 -503.41)" opacity="0.429" fill="url(#linear-gradient-32)"/>
          <path id="Path_9285" data-name="Path 9285" d="M419.116,512.925a29.943,29.943,0,0,0,1.909-8.4c-10.861-2.685-26.133.536-30.825,1.728l.161.117C409.15,504.929,416.4,509.645,419.116,512.925Z" transform="translate(-389.199 -503.444)" opacity="0.476" fill="url(#linear-gradient-33)"/>
          <path id="Path_9286" data-name="Path 9286" d="M420.152,512.86a31.272,31.272,0,0,0,1.764-8.295c-10.861-2.686-26.133.536-30.825,1.728l.147.106C410.026,504.956,417.434,509.58,420.152,512.86Z" transform="translate(-389.989 -503.478)" opacity="0.524" fill="url(#linear-gradient-34)"/>
          <path id="Path_9287" data-name="Path 9287" d="M421.188,512.8a32.979,32.979,0,0,0,1.619-8.191c-10.861-2.686-26.133.536-30.825,1.728l.132.1C410.9,504.984,418.47,509.516,421.188,512.8Z" transform="translate(-390.78 -503.512)" opacity="0.571" fill="url(#linear-gradient-35)"/>
          <path id="Path_9288" data-name="Path 9288" d="M422.224,512.73a35.208,35.208,0,0,0,1.473-8.088c-10.861-2.686-26.133.536-30.825,1.728l.117.085C411.779,505.012,419.507,509.451,422.224,512.73Z" transform="translate(-391.57 -503.545)" opacity="0.619" fill="url(#linear-gradient-36)"/>
          <path id="Path_9289" data-name="Path 9289" d="M423.26,512.666a38.169,38.169,0,0,0,1.328-7.986c-10.861-2.686-26.133.536-30.825,1.728l.1.075C412.654,505.039,420.542,509.386,423.26,512.666Z" transform="translate(-392.36 -503.58)" opacity="0.667" fill="url(#linear-gradient-37)"/>
          <path id="Path_9290" data-name="Path 9290" d="M424.3,512.6a42.242,42.242,0,0,0,1.182-7.883c-10.861-2.686-26.133.536-30.825,1.728l.088.064C413.531,505.067,421.578,509.321,424.3,512.6Z" transform="translate(-393.151 -503.613)" opacity="0.714" fill="url(#linear-gradient-38)"/>
          <path id="Path_9291" data-name="Path 9291" d="M425.331,512.536a48.069,48.069,0,0,0,1.037-7.78c-10.861-2.686-26.133.536-30.825,1.728l.074.053C414.406,505.094,422.614,509.257,425.331,512.536Z" transform="translate(-393.941 -503.647)" opacity="0.762" fill="url(#linear-gradient-39)"/>
          <path id="Path_9292" data-name="Path 9292" d="M426.368,512.471a57.013,57.013,0,0,0,.892-7.677c-10.861-2.686-26.133.536-30.825,1.728l.059.043C415.282,505.122,423.65,509.192,426.368,512.471Z" transform="translate(-394.732 -503.681)" opacity="0.81" fill="url(#linear-gradient-40)"/>
          <path id="Path_9293" data-name="Path 9293" d="M427.4,512.406a72.066,72.066,0,0,0,.747-7.574c-10.861-2.685-26.133.536-30.825,1.728l.045.032C416.158,505.149,424.685,509.127,427.4,512.406Z" transform="translate(-395.521 -503.714)" opacity="0.857" fill="url(#linear-gradient-41)"/>
          <path id="Path_9294" data-name="Path 9294" d="M428.439,512.341s.428-3.416.6-7.471c-10.861-2.686-26.133.536-30.825,1.728l.03.022C417.034,505.177,425.722,509.062,428.439,512.341Z" transform="translate(-396.312 -503.748)" opacity="0.905" fill="url(#linear-gradient-42)"/>
          <path id="Path_9295" data-name="Path 9295" d="M429.475,512.276s.283-3.313.456-7.368c-10.861-2.686-26.133.536-30.825,1.728l.016.011C417.91,505.2,426.757,509,429.475,512.276Z" transform="translate(-397.102 -503.782)" opacity="0.952" fill="url(#linear-gradient-43)"/>
          <path id="Path_9296" data-name="Path 9296" d="M430.511,512.212l.311-7.265c-10.861-2.686-26.133.536-30.825,1.728h0C418.786,505.231,427.793,508.932,430.511,512.212Z" transform="translate(-397.893 -503.816)" fill="url(#linear-gradient-44)"/>
        </g>
        <path id="Path_9297" data-name="Path 9297" d="M152.223,278.921l-.742.055h0c-18.81,1.419-27.8-2.22-30.52-5.445,0,0-.855,19.676-.993,22.845s11.789,6.9,30.771,5.427l.742-.055h0c18.81-1.42,27.8,2.219,30.52,5.445,0,0,.855-19.675.993-22.845S171.205,277.45,152.223,278.921Z" transform="translate(-116.481 -266.362)" fill="url(#linear-gradient-45)"/>
        <g id="Group_7099" data-name="Group 7099" transform="translate(53.478 17.899)" opacity="0.35" style="mix-blend-mode: multiply;isolation: isolate">
          <path id="Path_9298" data-name="Path 9298" d="M564.167,399c.24,1.169,4.086,2.424,6,2.3,3.867-.246,7.685-4.223,7.685-4.223l2.836,21.753s-10.262,10.187-12.94,3.423C565.657,416.968,564.167,399,564.167,399Z" transform="translate(-564.167 -394.472)" opacity="0" fill="url(#linear-gradient-46)"/>
          <path id="Path_9299" data-name="Path 9299" d="M564.928,397.932c.24,1.17,4.033,2.381,5.947,2.259,3.867-.246,7.62-4.215,7.62-4.215l2.832,21.758s-9.877,9.9-12.748,3.405C566.586,416.1,564.928,397.932,564.928,397.932Z" transform="translate(-564.842 -393.493)" opacity="0.048" fill="url(#linear-gradient-47)"/>
          <path id="Path_9300" data-name="Path 9300" d="M565.689,396.863c.24,1.17,3.98,2.337,5.894,2.215,3.867-.246,7.555-4.206,7.555-4.206l2.828,21.763s-9.493,9.615-12.558,3.387C567.516,415.239,565.689,396.863,565.689,396.863Z" transform="translate(-565.518 -392.514)" opacity="0.095" fill="url(#linear-gradient-48)"/>
          <path id="Path_9301" data-name="Path 9301" d="M566.45,395.794c.24,1.17,3.927,2.293,5.841,2.171,3.867-.246,7.49-4.2,7.49-4.2l2.825,21.767s-9.108,9.328-12.367,3.37C568.445,414.374,566.45,395.794,566.45,395.794Z" transform="translate(-566.193 -391.535)" opacity="0.143" fill="url(#linear-gradient-49)"/>
          <path id="Path_9302" data-name="Path 9302" d="M567.211,394.726c.24,1.169,3.874,2.25,5.788,2.128,3.867-.246,7.424-4.188,7.424-4.188l2.821,21.772s-8.724,9.042-12.176,3.352C569.374,413.51,567.211,394.726,567.211,394.726Z" transform="translate(-566.869 -390.556)" opacity="0.19" fill="url(#linear-gradient-50)"/>
          <path id="Path_9303" data-name="Path 9303" d="M567.972,393.658c.24,1.169,3.821,2.206,5.735,2.084,3.867-.246,7.359-4.179,7.359-4.179l2.817,21.777s-8.339,8.756-11.985,3.334C570.3,412.646,567.972,393.658,567.972,393.658Z" transform="translate(-567.544 -389.577)" opacity="0.238" fill="url(#linear-gradient-51)"/>
          <path id="Path_9304" data-name="Path 9304" d="M568.733,392.589c.24,1.17,3.768,2.162,5.682,2.04,3.867-.246,7.294-4.17,7.294-4.17l2.813,21.782s-7.954,8.47-11.794,3.316C571.233,411.78,568.733,392.589,568.733,392.589Z" transform="translate(-568.219 -388.597)" opacity="0.286" fill="url(#linear-gradient-52)"/>
          <path id="Path_9305" data-name="Path 9305" d="M569.494,391.521c.24,1.17,3.715,2.119,5.629,2,3.867-.246,7.228-4.162,7.228-4.162l2.809,21.786s-7.569,8.183-11.6,3.3C572.163,410.916,569.494,391.521,569.494,391.521Z" transform="translate(-568.895 -387.618)" opacity="0.333" fill="url(#linear-gradient-53)"/>
          <path id="Path_9306" data-name="Path 9306" d="M570.255,390.452c.24,1.17,3.662,2.075,5.576,1.953,3.867-.246,7.163-4.153,7.163-4.153l2.805,21.791s-7.185,7.9-11.411,3.28C573.092,410.051,570.255,390.452,570.255,390.452Z" transform="translate(-569.57 -386.638)" opacity="0.381" fill="url(#linear-gradient-54)"/>
          <path id="Path_9307" data-name="Path 9307" d="M571.017,389.383c.24,1.17,3.609,2.031,5.523,1.909,3.867-.246,7.1-4.144,7.1-4.144l2.8,21.8s-6.8,7.611-11.22,3.262C574.023,409.186,571.017,389.383,571.017,389.383Z" transform="translate(-570.246 -385.659)" opacity="0.429" fill="url(#linear-gradient-55)"/>
          <path id="Path_9308" data-name="Path 9308" d="M571.777,388.315c.24,1.169,3.556,1.988,5.47,1.866,3.867-.246,7.033-4.135,7.033-4.135l2.8,21.8s-6.415,7.325-11.029,3.244C574.951,408.322,571.777,388.315,571.777,388.315Z" transform="translate(-570.921 -384.681)" opacity="0.476" fill="url(#linear-gradient-56)"/>
          <path id="Path_9309" data-name="Path 9309" d="M572.538,387.247c.24,1.17,3.5,1.944,5.417,1.822,3.867-.246,6.968-4.126,6.968-4.126l2.793,21.805s-6.031,7.039-10.838,3.226C575.881,407.458,572.538,387.247,572.538,387.247Z" transform="translate(-571.596 -383.702)" opacity="0.524" fill="url(#linear-gradient-57)"/>
          <path id="Path_9310" data-name="Path 9310" d="M573.3,386.178c.24,1.17,3.45,1.9,5.364,1.779,3.867-.246,6.9-4.117,6.9-4.117l2.789,21.81s-5.646,6.752-10.647,3.209C576.81,406.592,573.3,386.178,573.3,386.178Z" transform="translate(-572.272 -382.722)" opacity="0.571" fill="url(#linear-gradient-58)"/>
          <path id="Path_9311" data-name="Path 9311" d="M574.06,385.11c.24,1.17,3.4,1.857,5.311,1.735,3.867-.246,6.837-4.109,6.837-4.109l2.785,21.815s-5.261,6.466-10.456,3.19C577.74,405.728,574.06,385.11,574.06,385.11Z" transform="translate(-572.947 -381.743)" opacity="0.619" fill="url(#linear-gradient-59)"/>
          <path id="Path_9312" data-name="Path 9312" d="M574.821,384.041c.24,1.17,3.344,1.813,5.258,1.691,3.867-.246,6.772-4.1,6.772-4.1l2.781,21.82s-4.876,6.18-10.265,3.173C578.669,404.864,574.821,384.041,574.821,384.041Z" transform="translate(-573.622 -380.764)" opacity="0.667" fill="url(#linear-gradient-60)"/>
          <path id="Path_9313" data-name="Path 9313" d="M575.582,382.973c.24,1.17,3.291,1.769,5.2,1.648,3.867-.246,6.706-4.091,6.706-4.091l2.777,21.825s-4.492,5.894-10.074,3.155C579.6,404,575.582,382.973,575.582,382.973Z" transform="translate(-574.298 -379.785)" opacity="0.714" fill="url(#linear-gradient-61)"/>
          <path id="Path_9314" data-name="Path 9314" d="M576.344,381.9c.24,1.17,3.238,1.726,5.152,1.6,3.867-.246,6.641-4.082,6.641-4.082l2.773,21.829s-4.107,5.607-9.883,3.137C580.529,403.134,576.344,381.9,576.344,381.9Z" transform="translate(-574.974 -378.805)" opacity="0.762" fill="url(#linear-gradient-62)"/>
          <path id="Path_9315" data-name="Path 9315" d="M577.1,380.836c.24,1.17,3.185,1.682,5.1,1.56,3.867-.246,6.576-4.073,6.576-4.073l2.769,21.834s-3.723,5.321-9.692,3.119C581.458,402.269,577.1,380.836,577.1,380.836Z" transform="translate(-575.648 -377.827)" opacity="0.81" fill="url(#linear-gradient-63)"/>
          <path id="Path_9316" data-name="Path 9316" d="M577.865,379.768c.24,1.17,3.132,1.638,5.046,1.517,3.867-.246,6.511-4.064,6.511-4.064l2.765,21.839a8.432,8.432,0,0,1-9.5,3.1C582.387,401.405,577.865,379.768,577.865,379.768Z" transform="translate(-576.324 -376.848)" opacity="0.857" fill="url(#linear-gradient-64)"/>
          <path id="Path_9317" data-name="Path 9317" d="M578.626,378.7c.24,1.17,3.079,1.595,4.993,1.473,3.867-.246,6.445-4.055,6.445-4.055l2.762,21.844a8.3,8.3,0,0,1-9.31,3.083C583.317,400.541,578.626,378.7,578.626,378.7Z" transform="translate(-576.999 -375.869)" opacity="0.905" fill="url(#linear-gradient-65)"/>
          <path id="Path_9318" data-name="Path 9318" d="M579.388,377.63c.24,1.17,3.026,1.551,4.94,1.429,3.867-.246,6.38-4.047,6.38-4.047l2.758,21.849s-2.568,4.463-9.119,3.066C584.247,399.675,579.388,377.63,579.388,377.63Z" transform="translate(-577.675 -374.889)" opacity="0.952" fill="url(#linear-gradient-66)"/>
          <path id="Path_9319" data-name="Path 9319" d="M580.148,376.562c.24,1.17,2.973,1.507,4.887,1.386,3.867-.246,6.315-4.038,6.315-4.038l2.754,21.853s-2.184,4.176-8.928,3.048Z" transform="translate(-578.35 -373.91)" fill="url(#linear-gradient-67)"/>
        </g>
        <path id="Path_9320" data-name="Path 9320" d="M579.46,373.7c-.48-1.827,2.241-2.517,4.588-2.853.512,1.816,1.217,4.113,1.217,4.113l-4.8,3.139" transform="translate(-524.212 -352.981)" fill="url(#linear-gradient-68)"/>
        <path id="Path_9321" data-name="Path 9321" d="M580.148,376.562c.24,1.17,2.973,1.507,4.887,1.386,3.867-.246,6.315-4.038,6.315-4.038l2.754,21.853s-2.184,4.176-8.928,3.048Z" transform="translate(-524.872 -356.011)" fill="url(#linear-gradient-69)"/>
        <g id="Group_7100" data-name="Group 7100" opacity="0.35" style="mix-blend-mode: multiply;isolation: isolate">
          <path id="Path_9322" data-name="Path 9322" d="M105.51,235.386c-.24-1.17-4.086-2.424-6-2.3-3.867.246-7.685,4.223-7.685,4.223l-2.836-21.753s10.262-10.187,12.94-3.423C104.02,217.417,105.51,235.386,105.51,235.386Z" transform="translate(-88.988 -209.812)" opacity="0" fill="url(#linear-gradient-70)"/>
          <path id="Path_9323" data-name="Path 9323" d="M105.713,237.426c-.24-1.17-4.033-2.381-5.947-2.259-3.867.246-7.62,4.215-7.62,4.215l-2.832-21.758s9.877-9.9,12.749-3.405C104.055,219.254,105.713,237.426,105.713,237.426Z" transform="translate(-89.277 -211.763)" opacity="0.048" fill="url(#linear-gradient-71)"/>
          <path id="Path_9324" data-name="Path 9324" d="M105.916,239.465c-.24-1.17-3.98-2.337-5.894-2.215-3.867.246-7.555,4.206-7.555,4.206l-2.828-21.763s9.493-9.614,12.557-3.387C104.09,221.089,105.916,239.465,105.916,239.465Z" transform="translate(-89.566 -213.713)" opacity="0.095" fill="url(#linear-gradient-72)"/>
          <path id="Path_9325" data-name="Path 9325" d="M106.119,241.5c-.24-1.17-3.927-2.293-5.841-2.171-3.867.246-7.489,4.2-7.489,4.2l-2.825-21.767s9.108-9.328,12.366-3.369C104.124,222.92,106.119,241.5,106.119,241.5Z" transform="translate(-89.854 -215.658)" opacity="0.143" fill="url(#linear-gradient-73)"/>
          <path id="Path_9326" data-name="Path 9326" d="M106.322,243.532c-.24-1.17-3.874-2.25-5.788-2.128-3.867.246-7.424,4.188-7.424,4.188L90.289,223.82s8.723-9.042,12.175-3.351C104.159,224.749,106.322,243.532,106.322,243.532Z" transform="translate(-90.143 -217.601)" opacity="0.19" fill="url(#linear-gradient-74)"/>
          <path id="Path_9327" data-name="Path 9327" d="M106.525,245.562c-.24-1.17-3.821-2.206-5.735-2.084-3.867.246-7.359,4.179-7.359,4.179L90.614,225.88s8.339-8.756,11.984-3.334C104.193,226.574,106.525,245.562,106.525,245.562Z" transform="translate(-90.431 -219.542)" opacity="0.238" fill="url(#linear-gradient-75)"/>
          <path id="Path_9328" data-name="Path 9328" d="M106.728,247.586c-.24-1.17-3.768-2.163-5.682-2.041-3.867.246-7.294,4.17-7.294,4.17L90.94,227.935s7.954-8.47,11.793-3.316C104.228,228.395,106.728,247.586,106.728,247.586Z" transform="translate(-90.72 -221.477)" opacity="0.286" fill="url(#linear-gradient-76)"/>
          <path id="Path_9329" data-name="Path 9329" d="M106.931,249.608c-.24-1.17-3.715-2.119-5.629-2-3.867.246-7.228,4.162-7.228,4.162l-2.809-21.786s7.569-8.183,11.6-3.3C104.262,230.212,106.931,249.608,106.931,249.608Z" transform="translate(-91.009 -223.409)" opacity="0.333" fill="url(#linear-gradient-77)"/>
          <path id="Path_9330" data-name="Path 9330" d="M107.134,251.623c-.24-1.17-3.662-2.075-5.576-1.953-3.867.246-7.163,4.153-7.163,4.153l-2.8-21.791s7.185-7.9,11.411-3.28C104.3,232.024,107.134,251.623,107.134,251.623Z" transform="translate(-91.297 -225.335)" opacity="0.381" fill="url(#linear-gradient-78)"/>
          <path id="Path_9331" data-name="Path 9331" d="M107.337,253.634c-.24-1.17-3.609-2.031-5.523-1.91-3.867.246-7.1,4.144-7.1,4.144l-2.8-21.8s6.8-7.611,11.22-3.262C104.331,233.831,107.337,253.634,107.337,253.634Z" transform="translate(-91.586 -227.256)" opacity="0.429" fill="url(#linear-gradient-79)"/>
          <path id="Path_9332" data-name="Path 9332" d="M107.54,255.638c-.24-1.17-3.556-1.988-5.47-1.866-3.867.246-7.033,4.135-7.033,4.135l-2.8-21.8s6.415-7.325,11.029-3.244C104.365,235.631,107.54,255.638,107.54,255.638Z" transform="translate(-91.874 -229.171)" opacity="0.476" fill="url(#linear-gradient-80)"/>
          <path id="Path_9333" data-name="Path 9333" d="M107.742,257.635c-.24-1.17-3.5-1.944-5.417-1.822-3.867.246-6.967,4.126-6.967,4.126l-2.793-21.805s6.031-7.039,10.838-3.226C104.4,237.424,107.742,257.635,107.742,257.635Z" transform="translate(-92.163 -231.079)" opacity="0.524" fill="url(#linear-gradient-81)"/>
          <path id="Path_9334" data-name="Path 9334" d="M107.945,259.624c-.24-1.17-3.45-1.9-5.364-1.779-3.867.246-6.9,4.117-6.9,4.117l-2.789-21.81s5.646-6.752,10.647-3.208C104.434,239.21,107.945,259.624,107.945,259.624Z" transform="translate(-92.451 -232.979)" opacity="0.571" fill="url(#linear-gradient-82)"/>
          <path id="Path_9335" data-name="Path 9335" d="M108.148,261.6c-.24-1.17-3.4-1.857-5.311-1.735-3.867.246-6.837,4.108-6.837,4.108l-2.785-21.815s5.261-6.466,10.456-3.191C104.468,240.984,108.148,261.6,108.148,261.6Z" transform="translate(-92.739 -234.868)" opacity="0.619" fill="url(#linear-gradient-83)"/>
          <path id="Path_9336" data-name="Path 9336" d="M108.352,263.571c-.24-1.17-3.344-1.813-5.258-1.691-3.867.246-6.772,4.1-6.772,4.1l-2.781-21.82s4.877-6.18,10.265-3.173C104.5,242.749,108.352,263.571,108.352,263.571Z" transform="translate(-93.029 -236.747)" opacity="0.667" fill="url(#linear-gradient-84)"/>
          <path id="Path_9337" data-name="Path 9337" d="M108.555,265.525c-.24-1.17-3.291-1.769-5.2-1.648-3.867.246-6.706,4.091-6.706,4.091l-2.777-21.825s4.492-5.894,10.074-3.155C104.538,244.5,108.555,265.525,108.555,265.525Z" transform="translate(-93.317 -238.611)" opacity="0.714" fill="url(#linear-gradient-85)"/>
          <path id="Path_9338" data-name="Path 9338" d="M108.758,267.463c-.24-1.17-3.238-1.726-5.152-1.6-3.867.246-6.641,4.082-6.641,4.082l-2.773-21.829s4.107-5.607,9.883-3.137C104.572,246.234,108.758,267.463,108.758,267.463Z" transform="translate(-93.606 -240.461)" opacity="0.762" fill="url(#linear-gradient-86)"/>
          <path id="Path_9339" data-name="Path 9339" d="M108.96,269.383c-.24-1.17-3.185-1.682-5.1-1.56-3.867.246-6.576,4.073-6.576,4.073l-2.77-21.834s3.723-5.321,9.692-3.119C104.607,247.949,108.96,269.383,108.96,269.383Z" transform="translate(-93.894 -242.291)" opacity="0.81" fill="url(#linear-gradient-87)"/>
          <path id="Path_9340" data-name="Path 9340" d="M109.163,271.278c-.24-1.17-3.132-1.639-5.046-1.517-3.867.246-6.511,4.064-6.511,4.064l-2.766-21.839a8.432,8.432,0,0,1,9.5-3.1C104.641,249.64,109.163,271.278,109.163,271.278Z" transform="translate(-94.182 -244.096)" opacity="0.857" fill="url(#linear-gradient-88)"/>
          <path id="Path_9341" data-name="Path 9341" d="M109.367,273.144c-.24-1.17-3.079-1.595-4.993-1.473-3.867.246-6.446,4.055-6.446,4.055l-2.762-21.844a8.3,8.3,0,0,1,9.31-3.083C104.676,251.3,109.367,273.144,109.367,273.144Z" transform="translate(-94.472 -245.874)" opacity="0.905" fill="url(#linear-gradient-89)"/>
          <path id="Path_9342" data-name="Path 9342" d="M109.57,274.973c-.24-1.17-3.026-1.551-4.94-1.429-3.867.246-6.38,4.047-6.38,4.047l-2.758-21.848s2.569-4.463,9.119-3.065C104.711,252.928,109.57,274.973,109.57,274.973Z" transform="translate(-94.76 -247.613)" opacity="0.952" fill="url(#linear-gradient-90)"/>
          <path id="Path_9343" data-name="Path 9343" d="M109.772,276.754c-.24-1.17-2.973-1.507-4.887-1.386-3.867.246-6.315,4.038-6.315,4.038l-2.754-21.853s2.184-4.176,8.928-3.048Z" transform="translate(-95.049 -249.305)" fill="url(#linear-gradient-91)"/>
        </g>
        <path id="Path_9344" data-name="Path 9344" d="M174.313,420.347c.481,1.827-2.241,2.517-4.588,2.853-.512-1.816-1.217-4.113-1.217-4.113l4.8-3.139" transform="translate(-159.562 -393.071)" fill="url(#linear-gradient-92)"/>
        <path id="Path_9345" data-name="Path 9345" d="M109.772,276.754c-.24-1.17-2.973-1.507-4.887-1.386-3.867.246-6.315,4.038-6.315,4.038l-2.754-21.853s2.184-4.176,8.928-3.048Z" transform="translate(-95.049 -249.305)" fill="url(#linear-gradient-93)"/>
      </g>
    </g>
    <g id="Group_7095" data-name="Group 7095" transform="translate(408.129 76.95)">
      <path id="Path_9226" data-name="Path 9226" d="M241.141,121.347l3.336,7.345a3.352,3.352,0,0,0,2.6,1.89l8.5,1.264a.65.65,0,0,1,.321,1.162l-6.6,5.618a3.455,3.455,0,0,0-1.249,3l.914,8.043a.753.753,0,0,1-1.1.7l-7.416-3.873a3.828,3.828,0,0,0-3.371-.036l-7.937,3.707a.679.679,0,0,1-1-.727l2.018-8.012a3.133,3.133,0,0,0-.835-3.022l-5.819-5.752a.693.693,0,0,1,.48-1.154l8.663-1.079A3.9,3.9,0,0,0,235.5,128.6l4.341-7.262A.744.744,0,0,1,241.141,121.347Z" transform="translate(-222.084 -118.349)" fill="#060d1d" style="mix-blend-mode: multiply;isolation: isolate"/>
      <g id="Group_7094" data-name="Group 7094">
        <path id="Path_9227" data-name="Path 9227" d="M228.5,96.284l3.83,7.76a3.614,3.614,0,0,0,2.721,1.977l8.564,1.244a.721.721,0,0,1,.4,1.23l-6.2,6.041a3.614,3.614,0,0,0-1.039,3.2l1.463,8.53a.721.721,0,0,1-1.047.76L229.53,123a3.615,3.615,0,0,0-3.364,0l-7.66,4.027a.721.721,0,0,1-1.047-.76l1.463-8.53a3.614,3.614,0,0,0-1.039-3.2l-6.2-6.041a.721.721,0,0,1,.4-1.23l8.564-1.244a3.614,3.614,0,0,0,2.721-1.977l3.83-7.76A.721.721,0,0,1,228.5,96.284Z" transform="translate(-211.468 -95.882)" fill="url(#linear-gradient-94)"/>
        <path id="Path_9228" data-name="Path 9228" d="M239.5,128.858a.488.488,0,0,1-.229-.058l-7.66-4.027a3.844,3.844,0,0,0-3.578,0l-7.66,4.027a.487.487,0,0,1-.229.058.5.5,0,0,1-.379-.179.481.481,0,0,1-.106-.4l1.463-8.53a3.843,3.843,0,0,0-1.106-3.4l-6.2-6.041a.492.492,0,0,1,.273-.839l8.564-1.244a3.843,3.843,0,0,0,2.894-2.1l3.83-7.76a.492.492,0,0,1,.882,0l3.83,7.76a3.843,3.843,0,0,0,2.894,2.1l8.564,1.244a.492.492,0,0,1,.272.839l-6.2,6.041a3.844,3.844,0,0,0-1.106,3.4l1.463,8.53a.48.48,0,0,1-.106.4A.5.5,0,0,1,239.5,128.858Z" transform="translate(-213.441 -97.858)" opacity="0.51" fill="url(#linear-gradient-95)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_9229" data-name="Path 9229" d="M237.643,130.1a4.419,4.419,0,0,0-4.112,0l-7.512,3.949,1.435-8.364a4.418,4.418,0,0,0-1.271-3.911l-6.077-5.924,8.4-1.22a4.418,4.418,0,0,0,3.327-2.417l3.756-7.61,3.756,7.61a4.418,4.418,0,0,0,3.327,2.417l8.4,1.22-6.077,5.924a4.418,4.418,0,0,0-1.271,3.911l1.435,8.364Z" transform="translate(-219.206 -103.694)" opacity="0.51" fill="url(#linear-gradient-96)" style="mix-blend-mode: multiply;isolation: isolate"/>
        <path id="Path_9230" data-name="Path 9230" d="M237.643,133.543a4.419,4.419,0,0,0-4.112,0l-7.512,3.949,1.435-8.364a4.418,4.418,0,0,0-1.271-3.911l-6.077-5.924,8.4-1.22a4.418,4.418,0,0,0,3.327-2.417l3.756-7.61,3.756,7.61a4.418,4.418,0,0,0,3.327,2.417l8.4,1.22-6.077,5.924a4.418,4.418,0,0,0-1.271,3.911l1.435,8.364Z" transform="translate(-219.206 -106.779)" opacity="0.46" fill="url(#linear-gradient-97)"/>
        <path id="Path_9231" data-name="Path 9231" d="M335.574,138.459c-.121.346-.228.657-.331.875-.251.531-1.247,1.928-.042,2.094a1.328,1.328,0,0,0,.939-.305,2.664,2.664,0,0,0,.7-.769,4.773,4.773,0,0,0,.794-2.579c.008-.52.011-1.785-.838-1.57C336.021,136.4,335.792,137.835,335.574,138.459Z" transform="translate(-321.79 -131.982)" fill="#fff" opacity="0.27"/>
        <path id="Path_9232" data-name="Path 9232" d="M395.059,197.232a3.108,3.108,0,0,1-1.371-.6c-.272-.24-.484-.577-.83-.687-.619-.2-1.185.462-1.359,1.087a6.035,6.035,0,0,0,.888,4.272.931.931,0,0,0,.279.366.842.842,0,0,0,.478.1,7.529,7.529,0,0,0,2.322-.521c.882-.3,5.815-1.632,5.507-2.738-.182-.652-1.6-.787-2.112-.832A25.928,25.928,0,0,1,395.059,197.232Z" transform="translate(-372.632 -185.493)" fill="#fff" opacity="0.27"/>
        <path id="Path_9233" data-name="Path 9233" d="M410.387,292.3a2.84,2.84,0,0,0-.485.738,2.633,2.633,0,0,0-.134.9,6.028,6.028,0,0,0,.343,2.465,2.62,2.62,0,0,0,1.785,1.619.9.9,0,0,0,.912-.239,1.141,1.141,0,0,0,.135-.752l-.24-4.572a1.774,1.774,0,0,0-.265-1.017C411.831,290.659,410.768,291.83,410.387,292.3Z" transform="translate(-389.107 -270.835)" fill="#fff" opacity="0.27"/>
        <path id="Path_9234" data-name="Path 9234" d="M289.333,278.784c-.5.01-.693.394-.643,1.346a12.8,12.8,0,0,1-.148,2.721c-.118.727-.935,3.842.393,3.356,1.063-.389,1.716-1.652,2.107-2.888a14.6,14.6,0,0,0,.487-2.048,3.12,3.12,0,0,0,.04-1.024c-.148-.856-1.021-1.176-1.635-1.359A2.037,2.037,0,0,0,289.333,278.784Z" transform="translate(-280.194 -259.732)" fill="#fff" opacity="0.27"/>
        <g id="Group_7093" data-name="Group 7093" transform="translate(9.082 13.45)">
          <g id="Group_7087" data-name="Group 7087" transform="translate(0 0.312)" opacity="0.27">
            <g id="Group_7086" data-name="Group 7086">
              <path id="Path_9235" data-name="Path 9235" d="M302.465,228.625l.177.027a.6.6,0,0,1,.332.121.4.4,0,0,1,.071.272c0,.035,0,.073-.006.113a1.238,1.238,0,0,1-.025.143q-.02.09-.047.185c-.019.065-.037.127-.054.181l-1.16,3.322-.942-3.285c-.046-.15-.081-.286-.105-.4a1.581,1.581,0,0,1-.034-.311.39.39,0,0,1,.032-.183.182.182,0,0,1,.083-.076,1.052,1.052,0,0,1,.278-.077l.173-.03V228h-2.584v.608l.16.038a.593.593,0,0,1,.234.1.673.673,0,0,1,.157.212,3.726,3.726,0,0,1,.211.585l1.66,5.494H302l2.04-5.446a4.523,4.523,0,0,1,.271-.612.776.776,0,0,1,.183-.231.657.657,0,0,1,.24-.1l.159-.039V228h-2.43Z" transform="translate(-298.653 -228.003)" fill="#fff"/>
              <path id="Path_9236" data-name="Path 9236" d="M360.1,228.6l.152.043a1.392,1.392,0,0,1,.267.094.2.2,0,0,1,.073.066.4.4,0,0,1,.048.153,3.153,3.153,0,0,1,.021.449v4.147a3.084,3.084,0,0,1-.028.49.266.266,0,0,1-.069.151.788.788,0,0,1-.308.121l-.156.04v.6h2.44v-.6l-.156-.041a1.12,1.12,0,0,1-.266-.093.2.2,0,0,1-.074-.069A.453.453,0,0,1,362,234a3.487,3.487,0,0,1-.019-.44V229.41a3.111,3.111,0,0,1,.023-.455.4.4,0,0,1,.047-.152.237.237,0,0,1,.08-.069,1.094,1.094,0,0,1,.253-.085l.158-.039V228H360.1Z" transform="translate(-353.698 -228.003)" fill="#fff"/>
              <path id="Path_9237" data-name="Path 9237" d="M395.046,228.476a2.9,2.9,0,0,0-1.8-.473h-2.586v.6l.151.043a1.391,1.391,0,0,1,.268.094.2.2,0,0,1,.073.066.4.4,0,0,1,.048.153,3.153,3.153,0,0,1,.021.449v4.147a3.083,3.083,0,0,1-.028.49.266.266,0,0,1-.069.151.79.79,0,0,1-.308.121l-.156.04v.6h2.521v-.61l-.163-.037a1.388,1.388,0,0,1-.3-.091.236.236,0,0,1-.094-.082.519.519,0,0,1-.065-.19,2.389,2.389,0,0,1-.028-.412v-1.373c.17.006.376.009.616.009a3.871,3.871,0,0,0,.958-.117,2.163,2.163,0,0,0,.827-.4,1.887,1.887,0,0,0,.55-.732,2.449,2.449,0,0,0,.19-.98A1.759,1.759,0,0,0,395.046,228.476Zm-2.511.373c.137-.005.306-.008.506-.008a2.133,2.133,0,0,1,.533.063.871.871,0,0,1,.626.556,1.67,1.67,0,0,1,.1.611,1.563,1.563,0,0,1-.16.767.846.846,0,0,1-.436.379,2.23,2.23,0,0,1-.817.126h-.348Z" transform="translate(-381.077 -228.003)" fill="#fff"/>
            </g>
          </g>
          <g id="Group_7088" data-name="Group 7088">
            <path id="Path_9238" data-name="Path 9238" d="M302.465,225.625l.177.027a.6.6,0,0,1,.332.121.4.4,0,0,1,.071.272c0,.035,0,.073-.006.113a1.235,1.235,0,0,1-.025.143q-.02.09-.047.185c-.019.065-.037.127-.054.18l-1.16,3.322-.942-3.285c-.046-.15-.081-.286-.105-.4a1.581,1.581,0,0,1-.034-.311.39.39,0,0,1,.032-.183.182.182,0,0,1,.083-.076,1.05,1.05,0,0,1,.278-.077l.173-.03V225h-2.584v.608l.16.038a.593.593,0,0,1,.234.1.672.672,0,0,1,.157.212,3.728,3.728,0,0,1,.211.585l1.66,5.494H302l2.04-5.446a4.526,4.526,0,0,1,.271-.612.776.776,0,0,1,.183-.231.656.656,0,0,1,.24-.1l.159-.039V225h-2.43Z" transform="translate(-298.653 -225.003)" fill="#8c2d04"/>
            <path id="Path_9239" data-name="Path 9239" d="M360.1,225.6l.152.043a1.391,1.391,0,0,1,.267.094.2.2,0,0,1,.073.066.4.4,0,0,1,.048.153,3.153,3.153,0,0,1,.021.449v4.147a3.084,3.084,0,0,1-.028.49.265.265,0,0,1-.069.15.786.786,0,0,1-.308.121l-.156.04v.6h2.44v-.6l-.156-.041a1.119,1.119,0,0,1-.266-.093.2.2,0,0,1-.074-.069A.453.453,0,0,1,362,231a3.487,3.487,0,0,1-.019-.44V226.41a3.111,3.111,0,0,1,.023-.455.4.4,0,0,1,.047-.152.236.236,0,0,1,.08-.069,1.1,1.1,0,0,1,.253-.085l.158-.039V225H360.1Z" transform="translate(-353.698 -225.003)" fill="#8c2d04"/>
            <path id="Path_9240" data-name="Path 9240" d="M395.046,225.476a2.9,2.9,0,0,0-1.8-.473h-2.586v.6l.151.043a1.39,1.39,0,0,1,.268.094.2.2,0,0,1,.073.066.4.4,0,0,1,.048.153,3.153,3.153,0,0,1,.021.449v4.147a3.082,3.082,0,0,1-.028.49.266.266,0,0,1-.069.15.788.788,0,0,1-.308.121l-.156.04v.6h2.521v-.61l-.163-.037a1.384,1.384,0,0,1-.3-.091.234.234,0,0,1-.094-.082.518.518,0,0,1-.065-.19,2.39,2.39,0,0,1-.028-.412V229.17c.17.006.376.009.616.009a3.869,3.869,0,0,0,.958-.117,2.162,2.162,0,0,0,.827-.4,1.886,1.886,0,0,0,.55-.732,2.449,2.449,0,0,0,.19-.98A1.759,1.759,0,0,0,395.046,225.476Zm-2.511.373c.137-.005.306-.008.506-.008a2.132,2.132,0,0,1,.533.063.915.915,0,0,1,.379.193.925.925,0,0,1,.247.363,1.67,1.67,0,0,1,.1.611,1.563,1.563,0,0,1-.16.767.845.845,0,0,1-.436.379,2.229,2.229,0,0,1-.817.126h-.348Z" transform="translate(-381.077 -225.003)" fill="#8c2d04"/>
          </g>
          <g id="Group_7092" data-name="Group 7092" transform="translate(0)">
            <g id="Group_7089" data-name="Group 7089" transform="translate(0 0)">
              <rect id="Rectangle_4203" data-name="Rectangle 4203" width="2.584" height="0.274" fill="#601903"/>
              <path id="Path_9241" data-name="Path 9241" d="M318.06,236.129c-.011-.053-.018-.1-.024-.153a.594.594,0,0,0-.01.116,1.583,1.583,0,0,0,.034.311c.024.119.06.255.105.4l.942,3.285,1.16-3.322c.017-.053.035-.115.054-.181s.034-.125.048-.185a1.24,1.24,0,0,0,.025-.143c0-.04.006-.078.006-.113a.746.746,0,0,0-.011-.127c0,.033-.011.068-.02.109q-.02.09-.048.185c-.018.065-.037.127-.054.181l-1.16,3.322-.942-3.285C318.12,236.384,318.084,236.247,318.06,236.129Z" transform="translate(-316.008 -234.833)" fill="#601903"/>
              <rect id="Rectangle_4204" data-name="Rectangle 4204" width="2.43" height="0.274" transform="translate(3.811)" fill="#601903"/>
            </g>
            <g id="Group_7090" data-name="Group 7090" transform="translate(6.4 0)">
              <path id="Path_9242" data-name="Path 9242" d="M360.563,279.237a.266.266,0,0,0,.069-.151,3.092,3.092,0,0,0,.028-.49v-.274a3.091,3.091,0,0,1-.028.49.266.266,0,0,1-.069.151.787.787,0,0,1-.308.121l-.156.04v.274l.156-.04A.788.788,0,0,0,360.563,279.237Z" transform="translate(-360.099 -272.768)" fill="#601903"/>
              <rect id="Rectangle_4205" data-name="Rectangle 4205" width="2.44" height="0.274" fill="#601903"/>
              <path id="Path_9243" data-name="Path 9243" d="M378.276,278.991a.2.2,0,0,1-.074-.069.454.454,0,0,1-.046-.16,3.485,3.485,0,0,1-.019-.44v.274a3.485,3.485,0,0,0,.019.44.454.454,0,0,0,.046.16.2.2,0,0,0,.074.069,1.13,1.13,0,0,0,.266.093l.156.041v-.274l-.156-.041A1.116,1.116,0,0,1,378.276,278.991Z" transform="translate(-376.258 -272.768)" fill="#601903"/>
            </g>
            <g id="Group_7091" data-name="Group 7091" transform="translate(9.584)">
              <path id="Path_9244" data-name="Path 9244" d="M409,247.659a2.229,2.229,0,0,0,.817-.126.846.846,0,0,0,.436-.379,1.562,1.562,0,0,0,.16-.767c0-.045,0-.087,0-.129a1.417,1.417,0,0,1-.155.622.845.845,0,0,1-.436.379,2.23,2.23,0,0,1-.817.126h-.348v.274Z" transform="translate(-406.779 -244.043)" fill="#601903"/>
              <path id="Path_9245" data-name="Path 9245" d="M395.046,225.75a1.717,1.717,0,0,1,.624,1.356c0-.053,0-.106,0-.16a1.759,1.759,0,0,0-.629-1.471,2.9,2.9,0,0,0-1.8-.473h-2.586v.274h2.586A2.9,2.9,0,0,1,395.046,225.75Z" transform="translate(-390.661 -225.003)" fill="#601903"/>
              <path id="Path_9246" data-name="Path 9246" d="M391.126,279.237a.266.266,0,0,0,.069-.151,3.083,3.083,0,0,0,.028-.49v-.274a3.082,3.082,0,0,1-.028.49.266.266,0,0,1-.069.151.789.789,0,0,1-.308.121l-.156.04v.274l.156-.04A.79.79,0,0,0,391.126,279.237Z" transform="translate(-390.661 -272.768)" fill="#601903"/>
              <path id="Path_9247" data-name="Path 9247" d="M408.84,278.869a.235.235,0,0,1-.094-.082.517.517,0,0,1-.065-.19,2.39,2.39,0,0,1-.028-.412v.274a2.39,2.39,0,0,0,.028.412.519.519,0,0,0,.065.19.235.235,0,0,0,.094.082,1.391,1.391,0,0,0,.3.091l.163.037V279l-.163-.037A1.385,1.385,0,0,1,408.84,278.869Z" transform="translate(-406.779 -272.645)" fill="#601903"/>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
